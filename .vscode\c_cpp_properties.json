{"configurations": [{"cStandard": "c99", "compilerPath": "C:/JL/pi32/bin/clang.exe", "cppStandard": "c++11", "defines": ["NOFLOAT", "CONFIG_USE_USER_UVC_DRIVER_EN", "CONFIG_USE_JPEG_DRIVER_EN", "__PRINTF_DEBUG", "ISP0_EN", "__CPU_dv20", "__ARCH_q32s", "USB_HW_20", "__Q32S__"], "includePath": ["${workspaceFolder}/include", "${workspaceFolder}/include/generic", "${workspaceFolder}/include/include_lib", "${workspaceFolder}/include/fs", "${workspaceFolder}/include/drivers", "${workspaceFolder}/include/drivers/flash", "${workspaceFolder}/include/drivers/key", "${workspaceFolder}/include/drivers/eeprom/E2BApi", "${workspaceFolder}/include/include_lib/ejpeg_header_new", "${workspaceFolder}/cpu/dv20", "${workspaceFolder}/include/cpu/dv20", "${workspaceFolder}/include/cpu/dv20/asm", "${workspaceFolder}/include/cpu/dv20/husb", "${workspaceFolder}/include/drivers/usb", "${workspaceFolder}/include/drivers/usb/device", "C:/JL/pi32/q32s-include"], "intelliSenseMode": "clang-x86", "name": "dv20"}], "version": 4}
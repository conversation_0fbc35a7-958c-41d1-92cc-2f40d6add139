{
    "version": "2.0.0",
    "tasks": [
        {
            "label": "all",
            "type": "shell",
            "windows": {
                "command": ".vscode/winmk.bat all"
            },
            "command": "make all -j`nproc`",
            "problemMatcher": [],
            "group": {
                "kind": "build",
                "isDefault": true
            }
        }
        ,{
            "label": "clean",
            "type": "shell",
            "windows": {
                "command": ".vscode/winmk.bat clean"
            },
            "command": "make clean -j`nproc`",
            "problemMatcher": [],
            "group": "build"
        }
        // ,{
        //     "label": "setup env",
        //     "type": "process",
        //     "windows": {
        //         "command": "tools/make_prompt.bat"
        //     },
        //     "command": "",
        //     "runOptions": {
        //         "runOn": "default"
        //     }
        // }
    ]
}

#include "video.h"
#include "jpeg_encoder.h"
#include "jpeg_abr.h"
#include "yuv_recorder.h"
#include "gpio.h"
#include "app_config.h"
#include "isp_customize.h"
#include "isp_scenes.h"
#include "osd.h"
#include "delay.h"
#include "clock.h"
#include "cpu.h"

#ifdef CAR_CAM_CASE

//bulk mode
#if !UVC_ISO_MODE

struct video_fh {
    u8 isp_debug: 1;
    u8 sub_isp: 1;
    u8 format: 1;
    u8 uvc_bulk_send: 1;
    u8 jpeg_set_fps_en: 1;
    u8 jpeg_drop_frame: 1;
    u8 delay_out_frames;
    u8 *video_buf;
    cbuffer_t 	video_cbuf;
    int video_cbuf_size;
    int frame_end_byte;
    u8 change_fps;
    void *camera;
    u16 imc_width;
    u16 imc_height;

    u32 speed;
    u32 data_len;
    u32 time_stamp;
    u8 time_stamp_overflow;

    /* volatile u8 one_frame; */
};
struct video_fh _video ;

#define TAR_WIDTH   (1280)
#define TAR_HEIGHT   (720)

#define YUV_MAX_WIDTH (640)

#define LINE_N   (18)
#define YUV_LINE_N   (2)
#ifdef ISP0_EN
#ifdef CONFIG_NLPFIX_ENABLE
#define VIDEO_BUF_SIZE  (7*1024)
#else
#define VIDEO_BUF_SIZE  (12*1024)
#endif
#else
#define VIDEO_BUF_SIZE  (60*1024)
#endif

/* #define JPEG_BIT_SIZE   (2*1024) */
#define JPEG_BIT_SIZE   (4*1024)

#define VIDEO_SPEED_CALC_TIM (200)
#define VIDEO_SPEED_CALC_FLITER (90)

#define __this   (&_video)

/* static u8 video_yuv_buf[TAR_WIDTH * LINE_N * 2] sec(.encode_buf) ALIGNE(64); //兼容yuv422 */
/* static u8 video_buf[VIDEO_BUF_SIZE] sec(.encode_buf) ALIGNE(64); */
static u8 *video_yuv_buf;
static u8 *video_buf;
static u8 *jpeg_bit_buf;
static u8 video_alloc_buf[(JPEG_BIT_SIZE + 4)  + (TAR_WIDTH * LINE_N * 2) + VIDEO_BUF_SIZE] sec(.encode_buf) ALIGNE(64);

#ifdef CONFIG_YUYV_ENABLE
u8 video_yuv_dest_buf[YUV_MAX_WIDTH * YUV_LINE_N * 2] sec(.encode_buf) ALIGNE(64);
/* #else */
/* u8 jpeg_bit_buf[JPEG_BIT_SIZE + 4] ALIGNE(64); */
#endif

static void alloc_video_buf(u16 width, u16 height)
{
    int buf_offset = 0;
    video_yuv_buf = video_alloc_buf;
    buf_offset += width * LINE_N * 2;
    memset(video_yuv_buf, 0, width * LINE_N * 2);
    jpeg_bit_buf = &video_alloc_buf[buf_offset];
    buf_offset += JPEG_BIT_SIZE + 4;
    video_buf = &video_alloc_buf[buf_offset];
    __this->video_cbuf_size = sizeof(video_alloc_buf) - buf_offset;
 ////   printf("video buf size = %dB\n", __this->video_cbuf_size);

}


void video_calc_speed(int len)
{
    u32 tim = jiffies_to_msecs(get_jiffies());
    __this->data_len += len;

    if (__this->time_stamp_overflow) {
        if (tim >= __this->time_stamp) {
            return;
        } else {
            __this->time_stamp_overflow = 0;
        }
    }

    if (time_after(tim, __this->time_stamp + VIDEO_SPEED_CALC_TIM - 1)) {
        u32 video_speed = __this->data_len / 128  * 1000 / (tim - __this->time_stamp);
        __this->speed = __this->speed * VIDEO_SPEED_CALC_FLITER / 100 + video_speed * (100 - VIDEO_SPEED_CALC_FLITER) / 100;
        if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
            __this->time_stamp_overflow = 1;
        }

        __this->time_stamp = tim;
        __this->data_len = 0;
    }
}

u32 video_get_speed()
{
    return (60000);
    /* return __this->speed; */
}


void set_isp_debug_state(u8 state)
{
    __this->isp_debug = state;
}

int isp_get_ae_awb_stc_buf_flag(void)
{
    if (__this->imc_width * __this->imc_height > (1280 * 720)) {
        return 1;
    }
    return 0;
}
int __attribute__((weak)) set_camera_freq(u32 frame_freq)
{
    return 0;
}

unsigned int video_get_bufsize()
{
    unsigned int data_len = cbuf_get_data_size(&__this->video_cbuf);

    return data_len;
}

int video_put_buf(void *buf, int len, u32 arg);
int video_open(const char *camera_name, int idx, int fmt, int frame_id, int fps, u16 width, u16 height)
{
    struct camera_device_info camera_info = {0};
    struct jpeg_attr jattr = {0};
    if (__this->camera) {
        //避免重复开启
        return 0;
    }

    eva_clk_init();
    eva_xbus_init();

    __this->camera = NULL;
    __this->imc_width = width;
    __this->imc_height = height;

    __this->data_len = 0;
    __this->time_stamp = jiffies_to_msecs(get_jiffies());
    if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
        __this->time_stamp_overflow = 1;
    }

    // set_board_ldo_power_init(); //开启电源

    /** camera_open **/
    const char *cam_name = get_board_camera_name();
    /* __this->camera = dev_open(cam_name, 0); */
    __this->camera = dev_open(cam_name, (void *)&camera0_data);
    if (!__this->camera) {
        puts(">>> video open faild!\n");
        return 0;
    }
    if (cam_name[strlen(cam_name) - 1] == '0') {
        //isp0
#ifdef ISP0_EN
        __this->sub_isp = sub_isp_open();
#endif
        if (__this->change_fps != fps) {
            set_camera_freq(fps);
            __this->change_fps = fps;
        }
    }

    dev_ioctl(__this->camera, CAMERA_GET_SENSOR_INFO, (u32)&camera_info);

    ////printf("ccamera handle:%x, %d,%d\n", __this->camera, camera_info.width, camera_info.height);
    ////printf("vvideo open idx:%d, fmt:%d, frame_id:%d fps:%d tar:%d x %d\n", idx, fmt, frame_id, fps, width, height);
    if (cam_name[strlen(cam_name) - 1] == '0') {
        // isp0
        // load isp config
        if (!__this->isp_debug) {
            struct isp_generic_cfg cfg;
            struct ispt_customize_cfg cus_cfg;

            cfg.id = ISP_GET_LV;
            int err = dev_ioctl((void *)__this->camera, ISP_IOCTL_GET_GENERIC_CFG, (unsigned int)&cfg);
            load_default_camera_config(cfg.lv, (void *)&cus_cfg);
            if (!err) {
                dev_ioctl((void *)__this->camera, ISP_IOCTL_SET_CUSTOMIZE_CFG, (unsigned int)&cus_cfg);
                start_update_isp_scenes((void *)__this->camera);
            }
        }
    }

    alloc_video_buf(width, height);

    u8 *out_buffer = video_yuv_buf;

    __this->video_buf = video_buf;
    cbuf_init(&__this->video_cbuf, __this->video_buf, __this->video_cbuf_size);

    /** imc open **/
    struct imc_iattr attr = {0};
    if (cam_name[strlen(cam_name) - 1] == '0') {
        attr.src = IMC_SRC_ISP0;
    } else {
        attr.src = IMC_SRC_ISP1;
    }
    attr.src_w = camera_info.width;
    attr.src_h = camera_info.height;
    attr.tar_w = width;
    attr.tar_h = (height == 1080) ? 1088 : height;
    attr.yuvformat = IMC_YUV422;
    //attr.yuvformat = IMC_YUV420;

    attr.imc_buf_mode = IMC_ROUND_BUFF;
    attr.imc_int_cfg = IMC_16LINE;
    attr.crop_x = 0;
    attr.crop_y = 0;
    attr.crop_width = attr.src_w;
    attr.crop_height = attr.src_h;
    // get sensor name
    void *isp_sen;
    dev_ioctl(get_video_device(), CAMERA_GET_SENSOR_HANDLE, (u32)&isp_sen);
    isp_sen_t *c = (isp_sen_t *)isp_sen;
    if (camera_info.width == 1280 && camera_info.height == 720) {
            int crop_x_l = 0, crop_x_r = 0;
        if (strncmp("1A4T", (char *)c->logo, strlen((char *)c->logo)) == 0) {
    #if defined(CONFIG_PRODUCT_YU7_BULK)
            // bulk 模式,这里的裁剪不能太多,否则会导致jpeg压缩不过来.....图像花屏
            crop_x_l = 130; // YU7 SC1346 lense shading是均匀的，对称裁剪
            crop_x_r = 130;
    #endif
        }  
        else if (strncmp("H62M", (char *)c->logo, strlen((char *)c->logo)) == 0) {
    #if defined(CONFIG_PRODUCT_SU7PRO_BULK) || defined(CONFIG_PRODUCT_SU7Z_BULK)
            crop_x_l = 200;
            crop_x_r = 50;
    #endif
        }
        int dst_w = camera_info.width - (crop_x_l + crop_x_r);
        int dst_h = (int)(height / (float)width * dst_w);
        int crop_y = (camera_info.height - dst_h) / 2;
        if (crop_y <= 0)
            crop_y = 0;
        attr.crop_x = crop_x_l;
        attr.crop_y = crop_y;
        attr.crop_width = attr.src_w-(crop_x_l + crop_x_r);
        attr.crop_height = attr.src_h-2*crop_y;
        printf("crop_x:%d crop_y:%d width:%d height:%d origin width:%d origin height %d\n",
            attr.crop_x, attr.crop_y,
            attr.crop_width, attr.crop_height,
            width, height);
        // else if (width == 640 && height == 480) {
        //     //720p->vga,需要剪裁
        //     attr.crop_x = 160;
        //     attr.crop_y = 0;
        //     attr.crop_width = attr.src_w;
        //     attr.crop_height = attr.src_h;
        // }
    }
    else if (camera_info.width == 640 && camera_info.height == 480) {
        if (width == 864 && height == 480) {
            int crop_x = 0;
            int dst_w = camera_info.width - 2 * crop_x;
            int dst_h = (int)(height / (float)width * dst_w);
            int crop_y = (camera_info.height - dst_h) / 2;
            if (crop_y <= 0)
                crop_y = 0;
            attr.crop_x = crop_x;
            attr.crop_y = crop_y;
            attr.crop_width = attr.src_w-2 * crop_x;
            attr.crop_height = attr.src_h-2*crop_y;
        }
    }
    attr.dma_cnt = LINE_N;
    attr.out_buf = out_buffer;
    attr.mirror = 0; //水平镜像
    if (fmt == 1) {
        //yuv格式输出,imc配置yuv422
        attr.yuvformat = IMC_YUV422;
    }

    void *imc = imc_open(&attr);
    /* void *imc = ex_imc_open(&attr); */
    if (!imc) {
        puts(">>> imc open faild!\n");
        return 0;
    }
    /* printf("imc==="); */
    /* printf_buf(imc, 64); */
    //osd config
    /* imc_osd_open(32,32); */
    if (fmt == 0) {
        //jpeg config
        jattr.jpeg_clock = 192; //96M
#ifdef FPGA
        jattr.jpeg_clock = 96; //96M
#endif

        jattr.width = width;
        jattr.height = height;
        jattr.height = (height == 1080) ? 1088 : height;
        jattr.enc_line = LINE_N;
        jattr.yuv_fmt = attr.yuvformat;
        jattr.fps = fps;
        jattr.in_buffer = out_buffer;
#ifdef CONFIG_YUYV_ENABLE
        jattr.bits_buffer = video_yuv_dest_buf;
        memset(video_yuv_dest_buf, 0, sizeof(video_yuv_dest_buf));
#else
        jattr.bits_buffer = jpeg_bit_buf;
#endif
        jattr.bits_size = JPEG_BIT_SIZE;
        jattr.bits_buffer_size = __this->video_cbuf_size;
        if (!usb_full_speed_mode()) {
            //USB2.0
            jattr.tar_Kbps = 15000;
            jattr.usb_mode = 0;
            __this->speed = jattr.tar_Kbps;
            jattr.high_speed_th = 50000;         //usb2.0速度阈值(速率小于阈值则是异常的usb2.0主机)
            jattr.abnormal_limit_kbps = 15000;   //异常情况下限制码率
        } else {
            jattr.tar_Kbps = 3500;
            jattr.usb_mode = 1;
            __this->speed = 3500;//jattr.tar_Kbps;
        }
        jattr.set_fps_en = 1; //动态帧率设置开关
        __this->jpeg_set_fps_en = jattr.set_fps_en;
        jattr.abr_en = 1;
        jattr.abr_mode = 1;
        jattr.dri_en = 0;
        jattr.jpeg_dri_mcu_cnt = 4; //jpeg分段位流mcu配置
        jattr.jpeg_output_buf = video_put_buf;
        jattr.jpeg_get_usb_speed = video_get_speed;
        jattr.jpeg_get_usb_bufsize = video_get_bufsize;
        jattr.jpeg_set_fps = NULL;

#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
        int jpeg_ex_encoder_start(struct jpeg_attr * jattr);
        jpeg_ex_encoder_start(&jattr);
#else
        //使用maskrom jpeg驱动
        jpeg_encoder_start(&jattr);
#endif
    } else {
        //yuv recorder config
        struct yuv_recorder_attr yuv_attr = {0};
        u8 *y = out_buffer;
        u8 *u = out_buffer + width * LINE_N;
        u8 *v = u + width * LINE_N / 2;
        yuv_attr.src_line_num = LINE_N;
        yuv_attr.y_start_addr = y;
        yuv_attr.u_start_addr = u;
        yuv_attr.v_start_addr = v;
        yuv_attr.image_width = width;
        yuv_attr.image_height = height;
        yuv_attr.des_line_num = YUV_LINE_N;
        yuv_attr.src_y_stride = width;
        yuv_attr.src_u_stride = width / 2;
        yuv_attr.src_v_stride = width / 2;
        yuv_attr.des_yuv_stride = width * 2;
#ifdef CONFIG_YUYV_ENABLE
        yuv_attr.des_start_addr = video_yuv_dest_buf;
#endif
        yuv_attr.yuv_output_buf = video_put_buf;
        if (width >= 1280) {
            //720p 最大5fps
            set_camera_freq(5);
            __this->change_fps = 1;
        }
        yuyv_recorder_open(&yuv_attr);

        yuyv_recorder_start();
    }
    __this->format = fmt;
    //imc kick start
    imc_kstart();

    __this->frame_end_byte = 0;
    /* __this->one_frame = 0; */
    __this->jpeg_drop_frame = 0;
    __this->uvc_bulk_send = 0;
    static u8 first_open = 0;
    if (first_open == 0) {
        __this->delay_out_frames = 3; /* 第一次开摄像头收敛慢,丢掉前面几帧 */
        first_open = 1;
    }

    return 0;

}

/*****************************************************************************************/
//osd总的窗口宽度，必须是32的整数倍，例如宏OSD_TOTAL_W和imc_set_osd_font_config函数里面的osd->width

// 由于这次OSD必须要做32bit的置换，所以如果单个字符的像素行宽osd_font_width, 刚好是64bit的整数倍,
// 就可以实现每次更新只更新变化的字符的动作，从而大大优化运算时间

/*****************************************************************************************/

static char *osd_str_format = "**AB";  //填实际显示的字符, '*' 表示显示汉字
#define STRING_NUM    (4)  //这里填osd_str_format字符串的长度

#define OSD_C_FONT_W  (32)  //汉字宽度
#define OSD_C_FONT_H  (32)  //汉字高度
#define OSD_A_FONT_W  (16)  //ascii字符宽度
#define OSD_A_FONT_H  (32)  //ascii字符高度
#define OSD_TOTAL_W   (STRING_NUM * OSD_C_FONT_W)
static u8 osd_buf_base[OSD_TOTAL_W * OSD_A_FONT_H / 8] ALIGNE(64);

static struct imc_ch_reg ch0_reg = {
    .scale = (struct scale_reg *) &imc_ch0_com_con,
    .dma = (struct dma_reg *) &imc_ch0_dma_con,
    .osd = (struct osd_reg *) &imc_ch0_osd_con,
};

void video_close()
{
    if (__this->camera) {
        puts("video close\n");
        stop_update_isp_scenes();
        dev_close(__this->camera);
        __this->camera = NULL;
        __this->format = 0;

        __this->speed = 0;
        __this->data_len = 0;
        __this->time_stamp = 0;
        __this->time_stamp_overflow = 0;

        if (__this->sub_isp) {
#ifdef ISP0_EN
            sub_isp_close();
#endif
            __this->sub_isp = 0;
        }
#ifdef CONFIG_YUYV_ENABLE
        yuyv_recorder_stop();;
#endif
        imc_close();

#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
        int jpeg_ex_encoder_stop(void);
        jpeg_ex_encoder_stop();
#else
        // jpeg close
        while (JL_JPG->CON2 & BIT(11)); //等待jpeg总线空闲
        JL_JPG->CON0 = 0;
#endif

    }
}


int video_put_buf(void *buf, int len, u32 arg)
{
    int wlen;
    int data_len = 0;
    /* if( __this->one_frame == 2){ */
    /* return 0; */
    /* } */
    /* printf("len:%d end byte=%d \n", len,__this->frame_end_byte); */

    if (__this->jpeg_drop_frame) {
        if (arg) {
            __this->jpeg_drop_frame = 0;
        }
        return len;
    }
    if (__this->imc_width <= 640 && usb_full_speed_mode()) {
        wlen = cbuf_prewrite(&__this->video_cbuf, buf, len);
    } else {
        wlen = cbuf_write(&__this->video_cbuf, buf, len);
    }
    if (wlen != len) {
      ////  putchar('M');
        if (__this->imc_width <= 640 && usb_full_speed_mode()) {
            cbuf_discard_prewrite(&__this->video_cbuf);
            __this->jpeg_drop_frame = 1;
        }
        return -1;
    }

    if (arg) {
        if (__this->imc_width <= 640 && usb_full_speed_mode()) {
            cbuf_updata_prewrite(&__this->video_cbuf);
        }
        data_len = cbuf_get_data_size(&__this->video_cbuf);
        if (!__this->frame_end_byte) {
            __this->frame_end_byte = data_len;
        }
        /* __this->one_frame++; */

    }
    if (__this->format) {
        //yuv
        return 0;
    }

    //bulk mode
    if (((cbuf_get_data_size(&__this->video_cbuf) >= UVC_FIFO_TXMAXP) && (__this->uvc_bulk_send == 0))) { // bulk传输已停并且缓存中数据大于512,再次启动发送
        //bulk start send
        __this->uvc_bulk_send = 1;
        int uvc_cyc_bulk_transfer(usb_dev usb_id);
        uvc_cyc_bulk_transfer(0);
    } else if (((__this->uvc_bulk_send == 0) && __this->frame_end_byte)) { // bulk传输已停,缓存数据不足512但已经到了帧尾，再次启动发送
        printf("uvc bulk send frame end\n");
        __this->uvc_bulk_send = 1;
        int uvc_cyc_bulk_transfer(usb_dev usb_id);
        uvc_cyc_bulk_transfer(0);
    }

    return wlen;
}

int video_get_buf(void *buf, int len, u32 *arg)
{
    int rlen;
    int need_len = len;
    int data_len = cbuf_get_data_size(&__this->video_cbuf);
    /* if( __this->one_frame == 2){ */
    /* return 0; */
    /* } */
    /* video_calc_speed(need_len); */

    if (!__this->frame_end_byte && (data_len <= need_len)) {
        /* printf("err: %d < %d\n",data_len,need_len); */
        //bulk stop send
        __this->uvc_bulk_send = 0;
        return 0;
    }

    if (__this->format == 0) { //mjpeg
        if (__this->frame_end_byte) {
            len = (__this->frame_end_byte < len) ? __this->frame_end_byte : len;
            __this->frame_end_byte -= len ;
            if (__this->frame_end_byte <= 0) {
                *arg = 1;
                /* __this->one_frame++; */
            }
        }
    }

    /* if(len) { */
    /* printf("\nrlen=%d %d\n", len,*arg); */
    /* } */
    rlen = cbuf_read(&__this->video_cbuf, buf, len);
    if (rlen != len) {
        printf("video buf read error! %d\n", rlen);
    }
    if (__this->delay_out_frames) {
        memset(buf, 0, len);
    }
    if (__this->format) {
        data_len = cbuf_get_data_size(&__this->video_cbuf);
        if (len && (data_len == 0)) {
            yuyv_recorder_start();
            if (__this->frame_end_byte) {
                __this->frame_end_byte = 0;
                *arg = 1;
            }
        }
    }
    //丢掉前面几帧
    if (*arg) {
        if (__this->delay_out_frames) {
            __this->delay_out_frames--;
        }
    }
    /* if(len){ */
    /* if(*arg) { */
    /* printf("frame end:"); */
    /* printf_buf(&buf[rlen-2],2); */
    /* } */
    /* else { */
    /* printf_buf(buf,len); */
    /* __this->one_frame ++ ; */
    /* if(__this->one_frame == 4) { */
    /* printf_buf(buf,len); */

    /* } */

    /* } */
    /* } */

    return len;
}

int video_get_buf2(void **buf, int len, u32 *arg)
{
    return 0;
}
void uvc_bulk_send()
{
#if CONFIG_USE_USER_UVC_DRIVER_EN
#if !UVC_ISO_MODE
    int uvc_cyc_bulk_transfer(u8 usb_id);
    int len = 1;
    __this->uvc_bulk_send = 1;
    while (len) {
        len = uvc_cyc_bulk_transfer(0);
        udelay(256);
    }
    __this->uvc_bulk_send = 0;
#endif
#endif
}
void *get_video_device()
{
    return (void *)__this->camera;
}
#endif
#endif

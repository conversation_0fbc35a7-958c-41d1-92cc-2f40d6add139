#include "device.h"
#include "printf.h"
#include "msg.h"

#include "fs.h"
volatile u8 jpeg_test = 0;
void device_event_change(void *arg, u8 event, int value)
{
#if (defined SDC_EN && defined FAT_EN)
    //挂载sd文件系统
    static void *mnt = NULL;
    if (!strcmp((const char *)arg, "sd0")) {
        switch (value) {
        case DEVICE_EVENT_IN:
#if 1
            if (dev_online("sd0")) {
                if (mnt == NULL) {
                    mnt = mount("sd0", "storage/sd0", "fat", 0, NULL);
                    if (mnt == NULL) {
                        printf("mount jlfat failed");
                    } else {
                        printf("mount jlfat sucess");
                        /* const char *test_string = "This is Test Strings for Fat32!"; */
                        /* FILE *f = fopen("storage/sd0/C/test001.txt","w+"); */
                        /* if(f) { */
                        /* puts(">>>> open file sucess!\n"); */
                        /* fwrite(f,test_string,strlen(test_string)); */
                        /* fclose(f); */
                        /* } */
                    }
                }
            }
#endif
            break;
        case DEVICE_EVENT_OUT:
            unmount("storage/sd0");
            mnt = NULL;
            break;
        }
    }
#endif
}

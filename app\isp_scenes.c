

#include "printf.h"
#include "hw_timer.h"
#include "isp.h"
#include "device.h"
#include "isp_scenes.h"
#include "isp_dev.h"
#include "app_config.h"

#include "osd.h"
#include "resfile.h"
#include "delay.h"

#define DEBUG_ISP  0

/**********************************isp效果配置修改****************************************/

//添加场景名字从0开始往后递增0,1,2,3,4,5.........
#define SCENE_NIGHT             0
#define SCENE_INDOOR            1
#define SCENE_OUTCLUDY          2
#define SCENE_OUTDOOR           3
#define SCENE_SCANCODE          4

// 只开UVC不开UAC的时候,多增加的第5个场景会导致crash,不清楚原因;TODO:第五个场景目前对接的客户均没有用到,考虑后续统一删除
#if (USB_DEVICE_CLASS_CONFIG == VIDEO_CLASS)
#define MAX_ISP_SCENES 4
unsigned char scane_type[] = {SCENE_NIGHT, SCENE_INDOOR, SCENE_OUTCLUDY, SCENE_OUTDOOR};
#else
//isp场景效果总段数修改
#define MAX_ISP_SCENES 5
//将场景添加到scane_type数组中
unsigned char scane_type[] = {SCENE_NIGHT, SCENE_INDOOR, SCENE_OUTCLUDY, SCENE_OUTDOOR, SCENE_SCANCODE};
#endif

//添加isp效果对应段的阈值范围20230308 for led openning more early
////#define USER_ISP_CFG_NIGHT_L    ((0 << 8))
////#define USER_ISP_CFG_NIGHT_H    ((1 << 8) 	- 40)
#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT)                      \
    || defined(CONFIG_PRODUCT_SU7PRO_2ND)                       \
    || defined(CONFIG_PRODUCT_SU7PRO_USBV11)                    \
    || defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)            \
    || defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)   \
    || defined(CONFIG_PRODUCT_SU7PRO_BULK)                      \
    || defined(CONFIG_PRODUCT_SU7Z_BULK)                        \
    || defined(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)
    #define USER_ISP_CFG_NIGHT_L    ((0 << 8))
    #define USER_ISP_CFG_NIGHT_H    ((2 << 8))

    #define USER_ISP_CFG_INDOOR_L   ((3 << 8) 	+ 256)
    #define USER_ISP_CFG_INDOOR_H   ((7 << 8) 	+ 256)

    #define USER_ISP_CFG_OUTCLUDY_L ((7 << 8) 	+ 0)
    #define USER_ISP_CFG_OUTCLUDY_H ((12 << 8) 	- 128)

    #define USER_ISP_CFG_OUTDOOR_L  ((12 << 8) 	+ 128)
    #define USER_ISP_CFG_OUTDOOR_H  ((20 << 8)  + 128)

    #define LED_ON_OFF_LV_LOW (800)
    #define LED_ON_OFF_LV_HIGH (1920)

#elif defined(CONFIG_PRODUCT_YU7_DEFAULT) || defined(CONFIG_PRODUCT_YU7_VM0) || defined(CONFIG_PRODUCT_YU7_BULK)

    #define USER_ISP_CFG_NIGHT_L    ((0 << 8))
    #define USER_ISP_CFG_NIGHT_H    ((4 << 8) 	- 0)

    #define USER_ISP_CFG_INDOOR_L   ((5 << 8) 	+ 0)
    #define USER_ISP_CFG_INDOOR_H   ((7 << 8) 	+ 256)

    #define USER_ISP_CFG_OUTCLUDY_L ((7 << 8) 	+ 0)
    #define USER_ISP_CFG_OUTCLUDY_H ((12 << 8) 	- 128)

    #define USER_ISP_CFG_OUTDOOR_L  ((12 << 8) 	+ 128)
    #define USER_ISP_CFG_OUTDOOR_H  ((20 << 8)  + 128)

    #define LED_ON_OFF_LV_LOW (1000)
    #define LED_ON_OFF_LV_HIGH (1600)

#elif (CONFIG_IS_PRODUCT_S30)
    #define USER_ISP_CFG_NIGHT_L    ((0 << 8))
    #define USER_ISP_CFG_NIGHT_H    ((4 << 8) 	- 0) // 125 on, 130 off

    #define USER_ISP_CFG_INDOOR_L   ((5 << 8) 	+ 0)
    #define USER_ISP_CFG_INDOOR_H   ((7 << 8)  - 256) // 5248

    #define USER_ISP_CFG_OUTCLUDY_L ((7 << 8) 	+ 256)
    #define USER_ISP_CFG_OUTCLUDY_H ((12 << 8)  - 128)

    #define USER_ISP_CFG_OUTDOOR_L  ((12 << 8) 	+ 128)
    #define USER_ISP_CFG_OUTDOOR_H  ((20 << 8)  + 128)

    #define LED_ON_OFF_LV_LOW (900)
    #define LED_ON_OFF_LV_HIGH (1100)

#elif (CONFIG_PRODUCT_YU7L_DEFAULT) || (CONFIG_PRODUCT_YU7L_XISHANG)

    #define USER_ISP_CFG_NIGHT_L    ((0 << 8))
    #define USER_ISP_CFG_NIGHT_H    (450)

    #define USER_ISP_CFG_INDOOR_L   (1200)
    #define USER_ISP_CFG_INDOOR_H   (2300)

    #define USER_ISP_CFG_OUTCLUDY_L (2301)
    #define USER_ISP_CFG_OUTCLUDY_H (2800)

    #define USER_ISP_CFG_OUTDOOR_L  (2801)
    #define USER_ISP_CFG_OUTDOOR_H  (5248)

    #define LED_ON_OFF_LV_LOW (300)
    #define LED_ON_OFF_LV_HIGH (800)

#elif (CONFIG_PRODUCT_ZB01) || (CONFIG_PRODUCT_ZB01_QIXI)

    #define USER_ISP_CFG_NIGHT_L    (0)
    #define USER_ISP_CFG_NIGHT_H    (500)

    #define USER_ISP_CFG_INDOOR_L   (600)
    #define USER_ISP_CFG_INDOOR_H   (1400)

    #define USER_ISP_CFG_OUTCLUDY_L (1500)
    #define USER_ISP_CFG_OUTCLUDY_H (2944)

    #define USER_ISP_CFG_OUTDOOR_L  (3200)
    #define USER_ISP_CFG_OUTDOOR_H  (5248)

    #define LED_ON_OFF_LV_LOW (550)
    #define LED_ON_OFF_LV_HIGH (1100)

#else
#error "add isp cfg for new product"
#endif
//将isp效果阈值范围添加到isp_par数组,按照场景的顺序添加
struct usr_isp_cfg_par {
    u32 levl;
    u32 levh;
};
struct usr_isp_cfg_par isp_par[] = {
    {USER_ISP_CFG_NIGHT_L, 		USER_ISP_CFG_NIGHT_H},
    {USER_ISP_CFG_INDOOR_L, 	USER_ISP_CFG_INDOOR_H},
    {USER_ISP_CFG_OUTCLUDY_L, 	USER_ISP_CFG_OUTCLUDY_H},
    {USER_ISP_CFG_OUTDOOR_L,	USER_ISP_CFG_OUTDOOR_H},
};

const char *isp_cfg_file[] = {
    "RES/isp_cfg_res/isp_cfg_0.bin",
    "RES/isp_cfg_res/isp_cfg_1.bin",
    "RES/isp_cfg_res/isp_cfg_2.bin",
    "RES/isp_cfg_res/isp_cfg_3.bin",
    "RES/isp_cfg_res/isp_cfg_4.bin",
};
/**************************************************************************/

#if (DEBUG_ISP == 1)
#define isp_puts  puts
#define __isp_cfg_printf  printf
#else
#define isp_puts(...)
#define __isp_cfg_printf(...)
#endif

#define SCENE_BUF_LEN (1352)

#define USER_ISP_MODE_NORMAL    0x0
#define USER_ISP_MODE_SPECIAL   0x1

#define USER_CFG_STATE_UNINIT    0x0
#define USER_CFG_STATE_INIT     0x1
#define USER_CFG_STATE_RUN      0x2
#define USER_CFG_STATE_SWITCH   0x3
#define USER_CFG_STATE_STOP     0x4
#ifdef FILL_LIGHT_ENABLE
////start
#define LED_STA_ON		    1
#define LED_STA_OFF		    0
#define LED_ON_CNT		    1
#if defined(CONFIG_PRODUCT_YU7_DEFAULT) || defined(CONFIG_PRODUCT_YU7_VM0) || defined(CONFIG_PRODUCT_YU7_BULK)
#define LED_OFF_CNT		    5
#else
#define LED_OFF_CNT		    1
#endif
////end
#endif
struct isp_scenes {
    u8 *buf;
    u32 size;
};


struct user_isp_cfg {
    u8 state;
    u8 mode;
    s8 current_scene;
    int cur_lv;
    int prev_lv;
    struct isp_scenes scenes[MAX_ISP_SCENES];
    int timer;
    void *server;
    int (*usb_tool_requset)(void *tool, void *arg, u8 type);
    void *tool;
#ifdef FILL_LIGHT_ENABLE
	////start
    u8 cur_led;
    u8 prev_led;
    int on_cnt;
    int off_cnt;
	////end
#endif
};

static struct user_isp_cfg  local_cfg = {
    .state = USER_CFG_STATE_UNINIT,
    .mode = USER_ISP_MODE_NORMAL,
    .current_scene = 1,
    .timer = 0,
#ifdef FILL_LIGHT_ENABLE
	////start
    .cur_led = LED_STA_OFF,
    .prev_led = LED_STA_OFF,
    .on_cnt = 0,
    .off_cnt = 0,
	////end
#endif
};

#define     __this   (&local_cfg)
#ifdef FILL_LIGHT_ENABLE
////start
extern void fill_light_off(void);
extern void fill_light_on(void);
////end
#endif

int isp_cfg_judge_first(u32 lev)
{
    u32 i;
    u32 i_num = sizeof(isp_par) / sizeof(isp_par[0]);
    if (i_num >= 2) {
        for (i = 0; i < (i_num - 2); i++) {
            if (lev >= (isp_par[i].levh + isp_par[i + 1].levl) / 2  && lev <= (isp_par[i + 1].levh + isp_par[i + 2].levl) / 2) {
                //中间
                return (i + 1);
            }
        }

        if (lev >= (isp_par[i_num - 2].levh + isp_par[i_num - 1].levl) / 2) {
            //最高
            return (i_num - 1);
        }
        if (lev <= (isp_par[0].levh + isp_par[1].levl) / 2) {
            //最低
            return 0;
        }
    } else {
    }
    return 0;
}
int isp_cfg_gap(u32 lev, u32 i_pre)
{
    u32 i_gap;
    u32 i_num;
    i_num = sizeof(isp_par) / sizeof(isp_par[0]);
    for (i_gap = 0;  i_gap < (i_num - 1); i_gap++) {
        if (lev >= isp_par[i_gap].levh && lev <= isp_par[i_gap + 1].levl) {
            if (i_gap > i_pre || (i_gap + 1) < i_pre) {
                //非相邻段,重新配置
                return i_gap;
            }
        }
    }
    return i_pre;//相邻段
}
int isp_cfg_judge(u32 lev)
{
    static u32 i_pre;
    u32 i_num = sizeof(isp_par) / sizeof(isp_par[0]);
    u32 i;
    for (i = 0; i < i_num; i++) {
        if (lev > isp_par[i].levl && lev < isp_par[i].levh) {
            i_pre = i;
            return i;
        }
    }
    if (lev >= isp_par[i_num - 1].levh) {
        i_pre = i_num - 1;
        return (i_num - 1);
    }
    if (lev <= isp_par[0].levl) {
        i_pre = 0;
        return 0;
    }
    i_pre = isp_cfg_gap(lev, i_pre);
    //__isp_cfg_printf("\ni_pre %d\n", i_pre);
    return i_pre;
}
static int user_isp_cfg_init(void)
{
    int i;
    if (__this->state == USER_CFG_STATE_UNINIT) {
        memset(__this->scenes, 0x0, sizeof(__this->scenes));
        extern u8 *get_isp_tool_buf(void);
        u8 *scenebuf = get_isp_tool_buf();

        for (i = 0; i < MAX_ISP_SCENES; i++) {
            __this->scenes[i].buf = scenebuf + (i * SCENE_BUF_LEN);
            __this->scenes[i].size =  SCENE_BUF_LEN;

            void *fp;
            fp = resfile_open((char *)isp_cfg_file[i]);
            if (fp) {
                int len = resfile_get_len(fp);
                __isp_cfg_printf("open %s sucess:%d\n", (char *)isp_cfg_file[i], len);
                len = resfile_read(fp, __this->scenes[i].buf, __this->scenes[i].size);
                resfile_close(fp);
                /* printf_buf(__this->scenes[i].buf,__this->scenes[i].size); */
            }
        }
    }
    return 0;
}
int load_default_camera_config(u32 lv, void *arg)
{
    u8 i;
    u8 scene;
    struct ispt_customize_cfg *cfg = (struct ispt_customize_cfg *)arg;
    /*
     *如果已经有效果，则不需要配置
     */
    if (__this->state != USER_CFG_STATE_UNINIT) {
        return -EINVAL;
    }

    /*
     *效果配置初始化
     */
    user_isp_cfg_init();
    __this->cur_lv = lv;
    __this->prev_lv = lv;

    i = isp_cfg_judge_first(__this->cur_lv);

    scene = scane_type[i];
    /*
     *获取当前效果文件
     */
    cfg->mode = ISP_CUSTOMIZE_MODE_FILE;
    cfg->data = __this->scenes[scene].buf;
    cfg->len = __this->scenes[scene].size;

    __this->state = USER_CFG_STATE_INIT;
    return 0;
}
int flush_isp_cfg(void *arg, u8 scene_type)
{
    void *camera_fd = arg;
    struct ispt_customize_cfg cfg;
    /* union video_req req; */

    if (scene_type > MAX_ISP_SCENES) {
        return -EINVAL;
    }
   //// printf("\n scene_type--------------: %d\r\n", scene_type);
    if (__this->scenes[scene_type].buf) {
        cfg.mode = ISP_CUSTOMIZE_MODE_FILE;
        cfg.data = __this->scenes[scene_type].buf;
        cfg.len = __this->scenes[scene_type].size;
       //// printf("\n scene_type222222--------------: %d\r\n", scene_type);
        dev_ioctl(camera_fd, ISP_IOCTL_SET_CUSTOMIZE_CFG, (unsigned int)&cfg);
    }

    return 0;
}

static void isp_scene_switch(void *arg, u8 force)
{
    void *camera_fd = arg;
    int i = 0;
    int ret = 0;
    int scene = __this->current_scene;
    struct isp_generic_cfg cfg;

    __this->prev_lv = __this->cur_lv;
    cfg.id = ISP_GET_LV;
    ret = dev_ioctl(camera_fd, ISP_IOCTL_GET_GENERIC_CFG, (unsigned int)&cfg);
    if (!ret) {
        __this->cur_lv = cfg.lv;
    }
    __isp_cfg_printf("\n cur lv %d\r\n", __this->cur_lv);
  ////  printf("\n cur lv %d\r\n", __this->cur_lv);
/////start
#ifdef FILL_LIGHT_ENABLE
    if (__this->cur_led == LED_STA_OFF) {
        if (__this->cur_lv <= LED_ON_OFF_LV_LOW) {
           //// printf("\n cur_lv <= 800 %d\r\n", __this->cur_lv);
            __this->on_cnt++;
           //// printf("\n 2222---on_cnt >= 800 %d\r\n", __this->on_cnt);
            if (__this->on_cnt >= LED_ON_CNT) {
                __this->on_cnt = 0;
                __this->cur_led = LED_STA_ON;
            }
        } else {
            //// printf("\n -------------6666666\r\n");
            __this->on_cnt = 0;
        }
    } else {

        if ((__this->cur_lv >= LED_ON_OFF_LV_HIGH)) {
           ////     printf("\n cur_lv >= 1200: %d\r\n", __this->cur_lv);
            __this->off_cnt++;
            ////printf("\n 111---off_cnt >= 1200 %d\r\n", __this->off_cnt);
            if (__this->off_cnt >= LED_OFF_CNT) {
 ////              printf("\n ----------44444\r\n");
                __this->off_cnt = 0;
                __this->cur_led = LED_STA_OFF;
            }
        } else {
            //// printf("\n----------------3333\r\n");
            __this->off_cnt = 0;
        }

    }
    if (__this->prev_led != __this->cur_led) {
        ////    printf("\n __this->prev_led != __this->cur_led\r\n");
        __this->prev_led = __this->cur_led;
        if (__this->cur_led == LED_STA_ON) {
           ////printf("\n fill_light_on-------\r\n");
            fill_light_on();
        } else {
          //// printf("\n fill_light_off-------\r\n");
            fill_light_off();
        }
    }
#endif
////end
    if (force) {
        i = isp_cfg_judge_first(__this->cur_lv);
    } else {
        i = isp_cfg_judge(__this->cur_lv);
        __isp_cfg_printf("\n cur scene %d\r\n", scane_type[i]);
    }
    scene = scane_type[i];

    if ((!force)  && (scene == __this->current_scene)) {
        return;
    }

    __isp_cfg_printf("scene %d\r\n", scene);
    __this->current_scene = scene;
////by frank by jieli 221123 doc add
 /*此处添加场景切换刷新模式*/

extern int ispt_params_flush();
extern int ispt_params_set_smooth_step(int step);
 ispt_params_set_smooth_step(1);

////end
    set_osd_cur_scene(scene);
    flush_isp_cfg(__this->server, scene);
}

////by frank 20221216 add for special mode
int user_flush_isp_cfg(u8 parm)
{

   if (__this->mode == parm) {
        return -EINVAL;
    }

    if ((parm == USER_ISP_MODE_SPECIAL) && (__this->mode == USER_ISP_MODE_NORMAL)) {
        printf("scancode mode ---------------\n");
        stop_update_isp_scenes();
        delay_ms(200);
        ////by frank by jieli 221123 doc add
        /*此处添加场景切换刷新模式*/

        extern int ispt_params_flush();
        extern int ispt_params_set_smooth_step(int step);
        ispt_params_set_smooth_step(1);

        ////end
        flush_isp_cfg(__this->server, SCENE_SCANCODE);
        __this->mode = USER_ISP_MODE_SPECIAL;

    } else {
        flush_isp_cfg(__this->server, __this->current_scene);
        start_update_isp_scenes(__this->server);
        __this->mode = USER_ISP_MODE_NORMAL;
    }

    return 0;
}
static void isp_scene_switch_timer(void *arg)
{

    isp_scene_switch(arg, 0);
}
int stop_update_isp_scenes(void)
{
    if (__this->timer) {
        sys_timer_del(__this->timer);
        __this->timer = 0;
    }

    if ((__this->state == USER_CFG_STATE_UNINIT) ||
        (__this->state == USER_CFG_STATE_INIT) ||
        (__this->state == USER_CFG_STATE_STOP)) {
        return 0;
    }

    __this->state = USER_CFG_STATE_STOP;
////start
#ifdef FILL_LIGHT_ENABLE
    fill_light_off();
#endif
////end
    return 0;
}

/*
 *开始使能更新isp效果配置
 *
 */
int start_update_isp_scenes(void *arg)
{
    if (!arg) {
        return -EINVAL;
    }

    if ((__this->state != USER_CFG_STATE_STOP) &&
        (__this->state != USER_CFG_STATE_INIT) &&
        (__this->state != USER_CFG_STATE_UNINIT)) {
        if (__this->server == arg) {
            return 0;
        }
        stop_update_isp_scenes();
    }

    __this->server = arg;


    if (__this->state == USER_CFG_STATE_UNINIT) {
        user_isp_cfg_init();
        isp_scene_switch(arg, 1);
    } else if (__this->state == USER_CFG_STATE_INIT) {
        isp_scene_switch(arg, 1);
        isp_scene_switch(arg, 0);
    } else {
        if (__this->mode == USER_ISP_MODE_SPECIAL) {
            isp_scene_switch(arg, 1);
            __this->mode = USER_ISP_MODE_NORMAL;
        } else {
            isp_scene_switch(arg, 0);
        }
    }

    if (!__this->timer) {
        ////     printf("\n 11111----start_update_isp_scenes -\r\n");
        __this->timer = sys_timer_add(arg, isp_scene_switch_timer, 200);
    }
    return 0;
}


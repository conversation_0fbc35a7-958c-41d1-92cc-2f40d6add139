#include "app_config.h"
#include "usb/device/usb_stack.h"
#include "usb/device/msd.h"
#include "usb_common_def.h"
#include "video.h"
#include "usb_audio.h"
#include "adc.h"
#include "key_driver.h"
#include "cpu.h"
#include "sleep.h"


#define TEST_NONE
/* #define TEST_SD_FILE */
/* #define TEST_AUDIO_FILE */
/* #define TEST_AUDIO_DC */

/* #define TEST_UART_CMD */



u8 miccount =0;
static u8 dac_volume = 0xff;
static void app_key_process(u8 event, u8 value)
{
    printf("key=%d, event=%d\n", value, event);
    switch (value) {
    case KEY_OK:
        if (event == MSG_KEY_CLICK) {
#if (defined TEST_AUDIO_FILE || defined TEST_AUDIO_DC)
            dac_set_volume(0);			//设置DAC增益0~255（共16324级，内部换算）
            printf("volume=0\n");
#endif
#ifdef TEST_SD_FILE
            void sdcard_write_test();
            sdcard_write_test();
#endif
        } else if (event == MSG_KEY_LONG) {
#ifdef TEST_NONE
            /* void sleep_test(); */
            /* sleep_test(); */
#endif
        }
        break;
    case KEY_UP:
        if (event == MSG_KEY_CLICK || event == MSG_KEY_HOLD) {
#if (defined TEST_AUDIO_FILE || defined TEST_AUDIO_DC)
            dac_set_volume(++dac_volume);			//设置DAC增益0~255（共16324级，内部换算）
            printf("volume=%d\n", dac_volume);
#endif
#ifdef TEST_NONE
#endif
        }
        break;
    case KEY_DOWN:
        if (event == MSG_KEY_CLICK || event == MSG_KEY_HOLD) {
#if (defined TEST_AUDIO_FILE || defined TEST_AUDIO_DC)
            dac_set_volume(--dac_volume);			//设置DAC增益0~255（共16324级，内部换算）
            printf("volume=%d\n", dac_volume);
#endif
#ifdef TEST_SD_FILE
            void sdcard_speed_test();
            sdcard_speed_test();
#endif
#ifdef TEST_NONE
#endif
        }
        break;
    case KEY_MODE:
        if (event == MSG_KEY_CLICK) {
#ifdef TEST_AUDIO_FILE
            void audio_file_switch(u8 dir);
            audio_file_switch(0);
#endif
#ifdef TEST_SD_FILE
            void sdcard_read_test();
            sdcard_read_test();
#endif
#ifdef TEST_NONE
#endif
        }
        break;
    case KEY_MENU:
        if (event == MSG_KEY_CLICK) {
#ifdef TEST_AUDIO_FILE
            void audio_file_switch(u8 dir);
            audio_file_switch(1);
#endif
#ifdef TEST_NONE
#endif
        }
        break;
    }
}

/** @brief app层处理消息函数
 *  @param[in]  p : 系统消息指针
 *  @return  None
 */
static void app_msg_process(void *p)
{
    struct sys_msg *msg = (struct sys_msg *)p;
    void (*func)(void *priv);
    void *data;
    struct uvc_video_data *uvc_data;
    void usb_slave_active_monitor(void *arg);
    if (msg->type == SYS_MSG_EVENT) {
        switch (msg->event) {
        case MSG_TIMER_RUN: //定时任务
            func = (void *)msg->value;
            func(msg->arg);
            break;
        case MSG_500MS:
            /*
            if (get_parking_state()) {
                //补光灯开
                set_board_led_light_io(1);
            } else {
                //补光灯关
                set_board_led_light_io(0);
            }
            */

#if TCFG_PC_ENABLE
            usb_slave_active_monitor(NULL);
#endif
            break;
        case MSG_1S:
            miccount ++;
            if(miccount>3)
                miccount =3;
          ////  printf("usb_speed =%dKbps, pll_nr %d\n", video_get_speed(), HUSB_PLL_NR);
         ////  malloc_stats();
#ifdef TEST_AUDIO_FILE
            {
                static u8 audio_test_init = 0;
                if (audio_test_init == 0) {
                    audio_test_init = 1;
                    void audio_file_test_init();
                    audio_file_test_init();
                }
            }
#endif
            break;
        case MSG_ISP0_TASK: {
#ifdef ISP0_EN
            extern void isp_task();
            isp_task();
#endif
        }
        break;
        case MSG_ISP0_EXCEPTION: {
            printf("isp0 fps is too low.\n");
        }
        break;
        case MSG_USB_TASK:
#if TCFG_USB_SLAVE_ENABLE
            usb_routine();
#endif
            break;
        case MSG_USB_AUTO_TRIM:
            /* start_dynamic_lrc_trim(); */
            break;
        case MSG_UVC_OPEN:
#if TCFG_USB_SLAVE_UVC_ENABLE
            if (msg) {
                uvc_data = (struct uvc_video_data *)msg->value;
                if (uvc_data) {
                ////    printf("MSG_UVC_OPEN in\n");
                 //// u32 tim2 = jiffies_to_msecs(get_jiffies());
                    video_open("camera0", uvc_data->idx,  uvc_data->fmt,  uvc_data->frame_id,  uvc_data->fps,  uvc_data->width,  uvc_data->height);
                 //// u32 tim3 = jiffies_to_msecs(get_jiffies());
                 ////   printf("--------main uvc open :MSG_UVC_OPEN out:tim2:%d;tim3:%d\n",tim2,tim3);
                }
            }
#endif
            break;
        case MSG_UVC_CLOSE:
            printf("MSG_UVC_CLOSE close\n");
#if TCFG_USB_SLAVE_UVC_ENABLE
            extern int uvc_video_close(int idx);
            uvc_video_close(0);
#endif
            break;

        case MSG_UVC_BULK_SEND: {
            /* printf("MSG_UVC_BULK_SEND \n"); */
            extern void uvc_bulk_send();
            uvc_bulk_send();
        }
        break;
        case MSG_UVC_AE_CONTROL: {
#ifdef ISP0_EN
         ////   printf("ae contorl exp time :%d\n", msg->value);
            void isp_ae_set_exptime(u32 exp_us, u32 gain);
            isp_ae_set_exptime(msg->value, 4096);
#endif
        }
        break;
        case MSG_KEY_CLICK:
        case MSG_KEY_LONG:
        case MSG_KEY_HOLD:
        case MSG_KEY_UP:
            app_key_process(msg->event, msg->value);
            break;

        default:
            break;
        }
    }
}
void c_main(void)
{
    struct sys_msg *msg = NULL;

    setup_arch();  /* 系统配置 */

    board_init();  /* 板级配置 */

#ifdef TEST_AUDIO_DC
    void audio_directly_connected_test();
    audio_directly_connected_test();
#endif

#ifdef USE_FLASH_CFG_EN
#include "e2bApi.h"
    OpenE2b();
#endif

#ifdef TEST_UART_CMD
    u8 user_check_upgrade(u32 jlfs_err);
    user_check_upgrade(0);
#endif

    while (1) {
        wdt_clr();
        msg = get_sys_msg(); //获取消息队列
#ifdef TEST_AUDIO_FILE
        void audio_file_read();
        audio_file_read();
#endif

#ifdef CONFIG_NLPFIX_ENABLE
        void nlpfix_process();
        nlpfix_process();
#endif

        if (msg) {
            switch (msg->type) {
            case SYS_MSG_EVENT:
                app_msg_process(msg);
                break;
            case SYS_DEVICE_EVENT: {
                if (!strcmp((const char *)msg->arg, "usb_audio")) {
#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
                    usb_audio_process(msg);
#endif
                } else {
                    extern  void device_event_change(void *arg, u8 event, int value);
                    device_event_change(msg->arg, msg->event, msg->value);
                }
            }
            default:
                break;
            }
        }

    }

}

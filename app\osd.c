#include "osd.h"
#include "dv20.h"
#include "imc.h"
#include "app_config.h"
#include <stdbool.h>

/*****************************************************************************************/
//osd总的窗口宽度，必须是32的整数倍，例如宏OSD_TOTAL_W和imc_set_osd_font_config函数里面的osd->width

// 由于这次OSD必须要做32bit的置换，所以如果单个字符的像素行宽osd_font_width, 刚好是64bit的整数倍,
// 就可以实现每次更新只更新变化的字符的动作，从而大大优化运算时间

/*****************************************************************************************/

/*************************** osd 显示字符的字模 ********************************/
/* 使用PC2LCD2021.exe工具，字体选择times new roman,字宽32，字高16生成 */
#ifdef CONFIG_ENABLE_OSD
static u8 osd_str_matrix[] ALIGNE(4) = {
    // 0
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xE0,0x03,0x00,0x00,0x38,0x0E,0x00,
    0x00,0x1C,0x1C,0x00,0x00,0x0E,0x38,0x00,0x00,0x0E,0x38,0x00,0x00,0x0E,0x38,0x00,
    0x00,0x0E,0x38,0x00,0x00,0x0E,0x38,0x00,0x00,0x1C,0x1C,0x00,0x00,0x38,0x0E,0x00,
    0x00,0xE0,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    // 1
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xC0,0x01,0x00,0x00,0xF8,0x01,0x00,
    0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,
    0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,0x00,0xC0,0x01,0x00,
    0x00,0xF8,0x0F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    // 2
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF0,0x07,0x00,0x00,0x0C,0x1E,0x00,
    0x00,0x02,0x38,0x00,0x00,0x00,0x38,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x0C,0x00,
    0x00,0x00,0x06,0x00,0x00,0x80,0x01,0x00,0x00,0x40,0x00,0x00,0x00,0x30,0x60,0x00,
    0x00,0xFC,0x3F,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    // 3
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0xF8,0x03,0x00,0x00,0x04,0x0E,0x00,
    0x00,0x00,0x0C,0x00,0x00,0x00,0x04,0x00,0x00,0x00,0x03,0x00,0x00,0xE0,0x0F,0x00,
    0x00,0x00,0x1E,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x18,0x00,0x00,0x00,0x0C,0x00,
    0x00,0xFE,0x03,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
    // 4
    0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x0E,0x00,0x00,0x00,0x0F,0x00,
    0x00,0x80,0x0E,0x00,0x00,0x40,0x0E,0x00,0x00,0x30,0x0E,0x00,0x00,0x08,0x0E,0x00,
    0x00,0x04,0x0E,0x00,0x00,0xFE,0x7F,0x00,0x00,0x00,0x0E,0x00,0x00,0x00,0x0E,0x00,
    0x00,0x00,0x0E,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,0x00,
};

static bool imc_initialized = false;
#define STRING_NUM    (2)  //这里填osd_str_format字符串的长度

#define OSD_C_FONT_W  (32)  //汉字宽度
#define OSD_C_FONT_H  (32)  //汉字高度
#define OSD_A_FONT_W  (16)  //ascii字符宽度
#define OSD_A_FONT_H  (32)  //ascii字符高度
#define OSD_TOTAL_W   (STRING_NUM * OSD_C_FONT_W)
u8 osd_buf_base[OSD_TOTAL_W * OSD_A_FONT_H / 8] ALIGNE(64);

static struct imc_ch_reg ch0_reg = {
    .scale = (struct scale_reg *) &imc_ch0_com_con,
    .dma = (struct dma_reg *) &imc_ch0_dma_con,
    .osd = (struct osd_reg *) &imc_ch0_osd_con,
};

static void imc_osd_open(u16 x_start, u16 y_start)
{
    struct imc_ch_reg *reg = &ch0_reg;
    /* printf("OSD_TOTAL_W:%d \n",OSD_TOTAL_W); */
    /* printf("STRING_NUM:%d \n",STRING_NUM); */
    u16 x_end = x_start + OSD_TOTAL_W;
    u16 y_end = y_start + OSD_A_FONT_H;
    reg->osd->color0 = 0xe20095;
    reg->osd->con |= IMC_OSD_1BIT_MODE;

    reg->osd->osd0_h_cfg = (x_start << 16) | x_end;
    reg->osd->osd0_v_cfg = (y_start << 16) | y_end;
    reg->osd->osd0_base = (u32)osd_buf_base;

    /*****************/
    reg->scale->com_con |= BIT(5); //osd err ie
#if 0
    printf("\n=====================osd reg list : \n");
    printf("con : 0x%x\n", reg->osd->con);
    printf("color0 : 0x%x, color1 : 0x%x, color2 : 0x%x, color3 : 0x%x\n", reg->osd->color0,
           reg->osd->color1, reg->osd->color2, reg->osd->color3);
    printf("osd0 h cfg : 0x%x, v cfg : 0x%x, base : 0x%x\n", reg->osd->osd0_h_cfg, reg->osd->osd0_v_cfg, reg->osd->osd0_base);
    printf("osd1 h cfg : 0x%x, v cfg : 0x%x, base : 0x%x\n", reg->osd->osd1_h_cfg, reg->osd->osd1_v_cfg, reg->osd->osd1_base);
    printf("====================================\n");
#endif
}
#endif

void set_osd_cur_scene(u8 scene_idx)
{
#ifdef CONFIG_ENABLE_OSD
    if (!imc_initialized)
    {
        imc_osd_open(32, 32);
        imc_initialized = true;
    }

    memset(osd_buf_base, 0x0, sizeof(osd_buf_base));

    u16 font_width  = OSD_A_FONT_W;
    u16 font_height = OSD_A_FONT_H;
    u8 osd_array_format = 2;
    u8 bit_byte = 1;
    u16 osd_font_width;
    u16 osd_width = OSD_TOTAL_W;
    u16 osd_total_width;
    u8 *osd_buf;
    u8 char_idx = scene_idx;
    u16 char_offset = char_idx * 64;
    u8 *font_matrix = osd_str_matrix + char_offset;
    u32 tmp;
    u32 *xxx;

    //osd buf config
    font_width = OSD_A_FONT_W;
    osd_font_width = font_width / 8 * osd_array_format * bit_byte;  /* 计算单个字符宽度 */
    for (u16 y = 0; y < font_height / osd_array_format; y++) {
        osd_total_width = (osd_width / 8) * osd_array_format * bit_byte;  /* 计算总字符宽度 */

        osd_buf = osd_buf_base + osd_total_width * y;
        memcpy(osd_buf, font_matrix, osd_font_width);
        font_matrix += osd_font_width;
    }

    /* 特殊处理,勿动 */
    xxx = (u32 *)osd_buf_base;
    for (u32 i = 0; i < sizeof(osd_buf_base) / 8; i++) {
        tmp = *(xxx + i * 2);
        *(xxx + i * 2) = *(xxx + i * 2 + 1);
        *(xxx + i * 2 + 1) = tmp;
    }
#endif
}

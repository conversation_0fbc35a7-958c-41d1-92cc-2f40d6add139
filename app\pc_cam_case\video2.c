#include "video.h"
#include "jpeg_encoder.h"
#include "jpeg_abr.h"
#include "yuv_recorder.h"
#include "gpio.h"
#include "app_config.h"
#include "isp_customize.h"
#include "isp_scenes.h"
#include "osd.h"
#include "delay.h"
#include "clock.h"
#include "cpu.h"
#include "lbuf.h"
#include "usb_config.h"

#ifdef PC_CAM_CASE

//iso mode
#if UVC_ISO_MODE

#if defined CONFIG_NLPFIX_ENABLE || defined CONFIG_UVC_FOR_1080P_ENABLE

struct video_fh {
    u8 isp_debug: 1;
    u8 sub_isp: 1;
    u8 format: 1;
    u8 delay_out_frames;
    u8 *video_buf;
    struct lbuff_head *lbuf;
    struct frame_buf_t *frame_ptr;
    struct frame_buf_t *jpeg_bits;
    int video_cbuf_size;
    int frame_end_byte;
    u8 change_fps;
    void *camera;

    u32 speed;
    u32 data_len;
    u32 time_stamp;
    u8 time_stamp_overflow;

    volatile u8 frame_state;
    /* volatile u8 one_frame; */
};
struct frame_buf_t {
    unsigned int len;
    unsigned int magic;
    void *priv;
    unsigned char data[0];
};

enum {
    FRAME_INIT_STATE = 0,
    FRAME_GAP_STATE, //帧间隔
    FRAME_END_STATE, //帧尾
};
struct video_fh _video ;


#ifdef CONFIG_NLPFIX_ENABLE
#define TAR_WIDTH   (1280)
#define TAR_HEIGHT   (720)
#define VIDEO_BUF_SIZE  (10*1024)////20230519 (16*1024)
#else
#define TAR_WIDTH   (1920)
#define TAR_HEIGHT   (1080)
#define VIDEO_BUF_SIZE  (14*1024)
#endif
#define LINE_N   (18)

#define JPEG_BIT_SIZE   (960)

#define VIDEO_SPEED_CALC_TIM (200)
#define VIDEO_SPEED_CALC_FLITER (90)

#define __this   (&_video)

static u8 video_alloc_buf[(JPEG_BIT_SIZE + 4 + 12)  + (TAR_WIDTH * LINE_N * 2) + VIDEO_BUF_SIZE] sec(.encode_buf) ALIGNE(64);


static void alloc_video_buf(u16 width, u16 height)
{
    int buf_offset = 0;
    u8 *video_yuv_buf = video_alloc_buf;
    buf_offset += width * LINE_N * 2;
    memset(video_yuv_buf, 0, width * LINE_N * 2);
    /* buf_offset += JPEG_BIT_SIZE + 4; */
   __this->video_buf = &video_alloc_buf[buf_offset];
    __this->video_cbuf_size = sizeof(video_alloc_buf) - buf_offset;
////    printf("video buf size = %dB\n", __this->video_cbuf_size);

}


void video_calc_speed(int len)
{
    u32 tim = jiffies_to_msecs(get_jiffies());
    __this->data_len += len;

    if (__this->time_stamp_overflow) {
        if (tim >= __this->time_stamp) {
            return;
        } else {
            __this->time_stamp_overflow = 0;
        }
    }

    if (time_after(tim, __this->time_stamp + VIDEO_SPEED_CALC_TIM - 1)) {
        u32 video_speed = __this->data_len / 128  * 1000 / (tim - __this->time_stamp);
        __this->speed = __this->speed * VIDEO_SPEED_CALC_FLITER / 100 + video_speed * (100 - VIDEO_SPEED_CALC_FLITER) / 100;
        if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
            __this->time_stamp_overflow = 1;
        }

        __this->time_stamp = tim;
        __this->data_len = 0;
    }
}

u32 video_get_speed()
{
    return __this->speed;
}


void set_isp_debug_state(u8 state)
{
    __this->isp_debug = state;
}

int isp_get_ae_awb_stc_buf_flag(void)
{
    return 1;
}
int __attribute__((weak)) set_camera_freq(u32 frame_freq)
{
    return 0;
}

unsigned int video_get_bufsize()
{
    return 0;
}

int video_put_buf(void *buf, int len, u32 arg);
int video_open(const char *camera_name, int idx, int fmt, int frame_id, int fps, u16 width, u16 height)
{
    struct camera_device_info camera_info = {0};
    struct jpeg_attr jattr = {0};


    eva_clk_init();
    eva_xbus_init();
    while (__this->frame_state == FRAME_GAP_STATE);
    JL_JPG->CON2 |= BIT(4); //line done 在kstart后才开始计数
    video_close();

    __this->camera = NULL;

    __this->data_len = 0;
    __this->time_stamp = jiffies_to_msecs(get_jiffies());
    if (__this->time_stamp + VIDEO_SPEED_CALC_TIM < VIDEO_SPEED_CALC_TIM) {
        __this->time_stamp_overflow = 1;
    }

    // set_board_ldo_power_init(); //开启电源

    /** camera_open **/
    const char *cam_name = get_board_camera_name();
    /* __this->camera = dev_open(cam_name, 0); */
    __this->camera = dev_open(cam_name, (void *)&camera0_data);
    if (!__this->camera) {
        puts(">>> video open faild!\n");
        return 0;
    }
    if (cam_name[strlen(cam_name) - 1] == '0') {
        //isp0
#ifdef ISP0_EN
        __this->sub_isp = sub_isp_open();
#endif
   ////     printf("__this->change_fps=%d fps=%d\n", __this->change_fps, fps);
        if (__this->change_fps != fps) {
            set_camera_freq(fps);
            __this->change_fps = fps;
        }
    }

    dev_ioctl(__this->camera, CAMERA_GET_SENSOR_INFO, (u32)&camera_info);

   //// printf("ccamera handle:%x, %d,%d\n", __this->camera, camera_info.width, camera_info.height);
   //// printf("vvideo open idx:%d, fmt:%d, frame_id:%d fps:%d tar:%d x %d\n", idx, fmt, frame_id, fps, width, height);
    if (cam_name[strlen(cam_name) - 1] == '0') {
        // isp0
        // load isp config
        if (!__this->isp_debug) {
            struct isp_generic_cfg cfg;
            struct ispt_customize_cfg cus_cfg;

            cfg.id = ISP_GET_LV;
            int err = dev_ioctl((void *)__this->camera, ISP_IOCTL_GET_GENERIC_CFG, (unsigned int)&cfg);

            load_default_camera_config(cfg.lv, (void *)&cus_cfg);
            if (!err) {
                dev_ioctl((void *)__this->camera, ISP_IOCTL_SET_CUSTOMIZE_CFG, (unsigned int)&cus_cfg);
                start_update_isp_scenes((void *)__this->camera);
            }
        }
    }

    alloc_video_buf(width, height);

    u8 *out_buffer = video_alloc_buf;

    /** imc open **/
    struct imc_iattr attr = {0};
    if (cam_name[strlen(cam_name) - 1] == '0') {
        attr.src = IMC_SRC_ISP0;
    } else {
        attr.src = IMC_SRC_ISP1;
    }
    attr.src_w = camera_info.width;
    attr.src_h = camera_info.height;
    attr.tar_w = width;
    attr.tar_h = (height == 1080) ? 1088 : height;
#ifdef CONFIG_USE_YUV420
    attr.yuvformat = IMC_YUV420;
#else
    attr.yuvformat = IMC_YUV422;
#endif

    attr.imc_buf_mode = IMC_ROUND_BUFF;
    attr.imc_int_cfg = IMC_16LINE;
    attr.crop_x = 0;
    attr.crop_y = 0;
    attr.crop_width = attr.src_w;
    attr.crop_height = attr.src_h;
    if (camera_info.width == 1280 && camera_info.height == 720) {
#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT) || defined (CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)
        int crop_x_l = 350; // H63P SU7PRO 左边有严重条纹和偏色（上下角偏色不一致），需要多裁剪一部分,客户三彪可以接受损失FOV多裁剪一些
        int crop_x_r = 150;
#elif defined(CONFIG_PRODUCT_SU7PRO_2ND) || defined (CONFIG_PRODUCT_SU7PRO_AUDIO_16K)
        int crop_x_l = 200; // H63P SU7PRO 左边有严重条纹和偏色（上下角偏色不一致），需要多裁剪一部分,客户悟空接受一点点的暗角，少裁剪一些
        int crop_x_r = 100;
#elif defined(CONFIG_PRODUCT_YU7_DEFAULT)
        int crop_x_l = 200; // YU7 SC1346 lense shading是均匀的，对称裁剪
        int crop_x_r = 200;
#elif defined(CONFIG_PRODUCT_YU7L_DEFAULT) || defined(CONFIG_PRODUCT_YU7L_XISHANG)
        int crop_x_l = 0; // YU7L C2595
        int crop_x_r = 0;
#elif defined(CONFIG_PRODUCT_ZB01)
        int crop_x_l = 0; // ZB01 OV02B
        int crop_x_r = 0;
#else
      #error "handle crop params for new product"
#endif
#if !FLIP
        int tmp_crop = crop_x_l;
        crop_x_l = crop_x_r;
        crop_x_r = tmp_crop;
#endif
        int dst_w = camera_info.width - (crop_x_l + crop_x_r);
        int dst_h = (int)(height / (float)width * dst_w);
        int crop_y = (camera_info.height - dst_h) / 2;
        if (crop_y <= 0)
            crop_y = 0;
        attr.crop_x = crop_x_l;
        attr.crop_y = crop_y;
        attr.crop_width = attr.src_w-(crop_x_l + crop_x_r);
        attr.crop_height = attr.src_h-2*crop_y;
    }
    attr.dma_cnt = LINE_N;
    attr.out_buf = out_buffer;
// #if FLIP
    attr.mirror = 0; //水平镜像
// #else
//     attr.mirror = 1; //水平镜像
// #endif
    if (fmt == 1) {
        //yuv格式输出,imc配置yuv422
#ifdef CONFIG_USE_YUV420
        attr.yuvformat = IMC_YUV420;
#else
        attr.yuvformat = IMC_YUV422;
#endif
    }

    void *imc = imc_open(&attr);
    if (!imc) {
        puts(">>> imc open faild!\n");
        return 0;
    }
    imc_ch0_dma_con |= BIT(4);
    /* printf("imc==="); */
    /* printf_buf(imc, 64); */
    if (fmt == 0) {
        __this->lbuf = NULL;
        ////puts("lbuf init\n");
        __this->lbuf = lbuf_init(__this->video_buf, __this->video_cbuf_size, 4, sizeof(struct frame_buf_t));
        __this->frame_ptr = NULL;
       //// puts("lbuf init out\n");

        //jpeg config
        jattr.jpeg_clock = 240; //96M
#ifdef FPGA
        jattr.jpeg_clock = 96; //96M
#endif
        jattr.width = width;
        jattr.height = height;
        jattr.height = (height == 1080) ? 1088 : height;
        jattr.enc_line = LINE_N;
        jattr.yuv_fmt = attr.yuvformat;
        jattr.fps = fps;
        jattr.in_buffer = out_buffer;

        __this->jpeg_bits = lbuf_alloc(__this->lbuf, JPEG_BIT_SIZE + 4 + 12);
        jattr.bits_buffer = (u8 *)(__this->jpeg_bits->data + 12);
        jattr.bits_size = JPEG_BIT_SIZE;
        jattr.bits_buffer_size = __this->video_cbuf_size;
        if (!usb_full_speed_mode()) {
            //USB2.0
            jattr.tar_Kbps = 20000;
            jattr.usb_mode = 0;
            __this->speed = jattr.tar_Kbps;
            jattr.high_speed_th = 30000;         //usb2.0速度阈值(速率小于阈值则是异常的usb2.0主机)
            jattr.abnormal_limit_kbps = 15000;   //异常情况下限制码率
        } else {
#if defined CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256
            jattr.tar_Kbps = 800;
#else
            jattr.tar_Kbps = 5000;
#endif
            jattr.usb_mode = 1;
            __this->speed = 4000;//jattr.tar_Kbps;
        }
        jattr.set_fps_en = 0; //动态帧率设置开关
        jattr.abr_en = 1;
        jattr.abr_mode = 1;
        jattr.dri_en = 0;
        jattr.jpeg_dri_mcu_cnt = 4; //jpeg分段位流mcu配置
        jattr.jpeg_output_buf = video_put_buf;
        jattr.jpeg_get_usb_speed = video_get_speed;
        jattr.jpeg_get_usb_bufsize = video_get_bufsize;
        jattr.jpeg_set_fps = NULL;

#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
        int jpeg_ex_encoder_start(struct jpeg_attr * jattr);
        jpeg_ex_encoder_start(&jattr);
#else
        //使用maskrom jpeg驱动
        jpeg_encoder_start(&jattr);
#endif
    } else {
        __this->lbuf = NULL;
      ////  puts("lbuf init\n");
        __this->lbuf = lbuf_init(__this->video_buf, __this->video_cbuf_size, 4, sizeof(struct frame_buf_t));
        __this->frame_ptr = NULL;
      ////  puts("lbuf init out\n");

        //yuv recorder config
        struct yuv_recorder_attr yuv_attr = {0};
        u8 *y = out_buffer;
        u8 *u = out_buffer + width * LINE_N;
        u8 *v = u + width * LINE_N / 2;
        yuv_attr.src_line_num = LINE_N;
        yuv_attr.y_start_addr = y;
        yuv_attr.u_start_addr = u;
        yuv_attr.v_start_addr = v;
        yuv_attr.image_width = width;
        yuv_attr.image_height = height;
        yuv_attr.des_line_num = (width > 640) ? 1 : 2;
        yuv_attr.src_y_stride = width;
        yuv_attr.src_u_stride = width / 2;
        yuv_attr.src_v_stride = width / 2;
        yuv_attr.des_yuv_stride = width * 2;
#ifdef CONFIG_YUYV_ENABLE
        __this->jpeg_bits = lbuf_alloc(__this->lbuf, width * yuv_attr.des_line_num * 2  + 12);
        printf("jpeg_bits=%x\n", __this->jpeg_bits->data);
        __this->video_cbuf_size = 0;
        yuv_attr.des_start_addr = (u8 *)__this->jpeg_bits->data;
#endif
        yuv_attr.yuv_output_buf = video_put_buf;
        imc_ch0_dma_con &= ~BIT(12); //取消imc 16对齐
        yuyv_recorder_open(&yuv_attr);

        yuyv_recorder_start();
    }
    __this->frame_end_byte = 0;
    __this->frame_state = 0;
    __this->format = fmt;
    //imc kick start
    imc_kstart();

    /* __this->one_frame = 0; */
    static u8 first_open = 0;
    if (first_open == 0) {
        __this->delay_out_frames = 1; /* 第一次开摄像头收敛慢,丢掉前面几帧 */
        first_open = 1;
    }

    return 0;

}
void video_close()
{
    if (__this->camera) {
        // disable pa power
        {
            extern void audio_spk_close_cb();
            audio_spk_close_cb();
        }
        puts("video close\n");
        stop_update_isp_scenes();
        dev_close(__this->camera);
        __this->camera = NULL;

        __this->speed = 0;
        __this->data_len = 0;
        __this->time_stamp = 0;
        __this->time_stamp_overflow = 0;
        __this->frame_state = 0;

        imc_close();
        if (__this->sub_isp) {
#ifdef ISP0_EN
            sub_isp_close();
#endif
            __this->sub_isp = 0;
        }

        if (__this->format == 0) {
#ifdef CONFIG_USE_JPEG_DRIVER_EN    //使用外部jpeg编码驱动
            int jpeg_ex_encoder_stop(void);
            jpeg_ex_encoder_stop();
#else
            // jpeg close
            while (JL_JPG->CON2 & BIT(11)); //等待jpeg总线空闲
            JL_JPG->CON0 = 0;
#endif
            if (__this->lbuf) {
                lbuf_clear(__this->lbuf);
                __this->lbuf = NULL;
            }
        } else if (__this->format) {
            yuyv_recorder_stop();;
            if (__this->lbuf) {
                if (__this->jpeg_bits) {
                    lbuf_free(__this->lbuf, __this->jpeg_bits);
                    __this->jpeg_bits = NULL;
                }
                __this->lbuf = NULL;
            }

        }
        __this->format = 0;

    }
}


int video_put_buf(void *buf, int len, u32 arg)
{
    struct frame_buf_t *alloc_buf = NULL;

    if (!__this->camera) {
        return 0;
    }
    if (__this->format) {
        //yuv
        __this->video_cbuf_size = len;
        __this->frame_end_byte = arg;
        return 0;
    }
    if (__this->lbuf) {
        __this->jpeg_bits->len = len;
        __this->jpeg_bits->magic = arg;
        lbuf_push(__this->lbuf, __this->jpeg_bits, 1);
        /* printf("\nput = %d %x %d %d\n",len ,__this->jpeg_bits->data,__this->jpeg_bits->len ,__this->jpeg_bits->magic); */
        alloc_buf = lbuf_alloc(__this->lbuf, JPEG_BIT_SIZE + 4 + 12);
        if (!alloc_buf) {
            putchar('M');
            return 0;
        }
        __this->jpeg_bits = alloc_buf;
        __this->jpeg_bits->magic = 0;
        jpeg_ex_set_baddr(__this->jpeg_bits->data + 12, JPEG_BIT_SIZE, arg);
    }


    /* printf("\nput = %d %x %d %d\n",len ,alloc_buf,alloc_buf->len ,alloc_buf->magic); */
    return len;
}

int video_get_buf2(void **buf, int len, u32 *arg)
{
    int need_len = len;
    int _len = 0;
    u8 *tx_addr = NULL;

    video_calc_speed(need_len);

    if (__this->format) {
#ifdef CONFIG_YUYV_ENABLE
        //yuyv
        if (__this->lbuf) {
            if (__this->video_cbuf_size) {
                len = (__this->video_cbuf_size < len) ? __this->video_cbuf_size : len;
                tx_addr = usb_get_ep_buffer(0, USB_CLASS_VIDEO, USB_DIR_IN);
                dma_memcpy(&tx_addr[0x0c], __this->jpeg_bits->data, len);
                *buf = tx_addr;
                _len = len + 12;
                __this->video_cbuf_size -= len;
                if (__this->video_cbuf_size <= 0) {
                    __this->video_cbuf_size = 0;
                    yuyv_recorder_start();
                    if (__this->frame_end_byte) {
                        *arg = 1;
                        __this->frame_end_byte = 0;
                    }
                }
                return _len;
            }
        }
        return 0;
#endif
    }
    if (__this->lbuf) {
        if (__this->frame_ptr) {
            if (__this->frame_ptr->len == 0) {
                //发完了数据
                lbuf_free(__this->lbuf, __this->frame_ptr);
                __this->frame_ptr = NULL;
            }
        }
        if (!__this->frame_ptr) {
            __this->frame_ptr = lbuf_pop(__this->lbuf, 1);
        }
        if (__this->frame_ptr) {
            *buf = (void *)__this->frame_ptr->data;
            _len = __this->frame_ptr->len + 12;
            __this->frame_ptr->len = 0;
            *arg = __this->frame_ptr->magic;
            /* printf("get =%x %d %d\n",__this->frame_ptr->data,_len , *arg); */
        }
        __this->frame_state = *arg ? FRAME_END_STATE : FRAME_GAP_STATE;
    }

    return _len;


}

int video_get_buf(void *buf, int len, u32 *arg)
{
    ////printf("\npc cam case 1080p [ debug ]--func=%s line=%d\n", __func__, __LINE__);
    return 0;
}
void uvc_bulk_send()
{
}
void *get_video_device()
{
    return (void *)__this->camera;
}
#endif

#endif
#endif

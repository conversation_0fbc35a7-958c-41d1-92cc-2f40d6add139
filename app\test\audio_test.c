#include "app_config.h"
#include "audio.h"
#include "fs.h"


/* #define TEST_AUDIO_FILE */
/* #define TEST_AUDIO_DC */

#ifdef TEST_AUDIO_DC
static u16 adc_buf[512];
/* AUDIO AD和DA直连测试函数 */
void audio_directly_connected_test()
{
    struct audio_dev dev;
    audio_init(128);

    dev.sample_rate = ADC_SMP_48000;	//采样率，填枚举值
    dev.volume = 0x3f;		//音量，0~255
    dev.adc_type = ADC_TYPE_SINGLE;	//ADC输入模式
    dev.buf = (u8 *)adc_buf;		//音频buf，ADC/DAC共用的时候可以不另外编写handler，adc数据直通到dac
    dev.buf_size = 512;	//音频buf采样点数，每帧长度为此大小的一半
    adc_open(&dev);

    dev.volume = 0xff;		//音量，0~255
    dev.sample_rate = DAC_SMP_48000;	//采样率，填枚举值
    dev.adc_type = 0;	//ADC输入模式
    dac_open(&dev);
}
#endif


#ifdef TEST_AUDIO_FILE
#define DAC_BUF_LEN 4096
#define AUDIO_FILE_MAX 2

static u16 audio_buf[DAC_BUF_LEN];

static u8 file_buf[DAC_BUF_LEN * 4];
static FILE *fp = NULL;
static int file_idx = 2;
static u8 file_flag = 0;
static u8 file_need_read = 0;
static u32 file_rlen = 0;

void audio_file_switch(u8 dir)
{
    char filename[128];
    if (fp) {
        fclose(fp);
    }

    if (dir) {
        if (--file_idx < 0) {
            file_idx = AUDIO_FILE_MAX;
        }
    } else {
        if (++file_idx > AUDIO_FILE_MAX) {
            file_idx = 0;
        }
    }

    sprintf(filename, SD0_ROOT_PATH"a%d.wav", file_idx);

    fp = fopen(filename, "r");

    if (!fp) {
        printf("open file err\n");
        memset(file_buf, 0, sizeof(file_buf));
    } else {
        printf("open file success\n");
        fread(fp, file_buf, sizeof(file_buf));
    }

    file_flag = 0;
    file_need_read = 0;
    file_rlen = 0;
}

void audio_file_read()
{
    u32 len = sizeof(file_buf) / 2;
    u32 rlen = 0;
    if (fp) {
        if (file_need_read) {
            file_need_read = 0;
            if (file_flag < 2) {
                rlen = fread(fp, file_buf + len * file_flag, len);
                file_flag = !file_flag;
                if (rlen < len) {
                    memset(file_buf + len * file_flag + rlen, 0, len - rlen);
                    file_flag += 2;
                }
            } else {
                file_flag -= 2;
                printf("play end\n");
                memset(file_buf + len * file_flag, 0, len);
                fclose(fp);
                fp = NULL;
            }
        }
    }
}

void audio_file_test_init()
{
    struct audio_dev dev;
    audio_init(128);

    dev.volume = 0xff;		//音量，0~255
    dev.sample_rate = DAC_SMP_44100;	//采样率，填枚举值
    dev.adc_type = 0;	//ADC输入模式
    dev.buf = (u8 *)audio_buf;		//音频buf，ADC/DAC共用的时候可以不另外编写handler，adc数据直通到dac
    dev.buf_size = DAC_BUF_LEN;	//音频buf采样点数，每帧长度为此大小的一半
    dac_open(&dev);

    audio_file_switch(0);
}


/* AUDIO DAC中断 */
void dac_handler(u8 *buf, u32 len)
{
    u32 rlen = file_rlen + len;

    if (rlen < sizeof(file_buf)) {
        memcpy(buf, file_buf + file_rlen, len);
        if ((file_rlen < sizeof(file_buf) / 2) && (rlen >= sizeof(file_buf) / 2)) {
            file_need_read = 1;
        }
    } else {
        rlen -= sizeof(file_buf);
        memcpy(buf, file_buf + file_rlen, len - rlen);
        memcpy(buf + len - rlen, file_buf, rlen);
        file_need_read = 1;
    }
    file_rlen = rlen;

}
#endif

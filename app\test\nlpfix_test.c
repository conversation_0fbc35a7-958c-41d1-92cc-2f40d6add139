#include "SplittingFilter.h"
#include "EchoSuppressLib.h"
#include "jiffies.h"
#include "my_malloc.h"
#include "app_config.h"
#include "fs.h"
#include "audio.h"


#ifdef CONFIG_NLPFIX_ENABLE

#ifndef Use_NLP
#define Use_NLP 1
#endif

/* #define FILE_TEST */


#define SAMPLERATE 8000

/***********************************NLP 参数配置**********************************************/
const float AggressFactor = -5.f;      /* 回声前级的动态压制,越小越强,设置范围-5.0 - -1.0 */
const float MiniSuppress = 9.f;        /* 回声后级的静态压制,越大越强,设置范围0 - 10.0f */
const float OverSuppressThr = 0.05f;  //0.05f;   /* 进入二级压制阈值(0 - 1) */
const float OverSuppress = 6.f;        /* 二级压制系数(1.0f - 8.0f),,越大声音消除效果越强 */
////by frank 20230228 降低回声压制，加大会导致喇叭声音变异
/*********************************************************************************************/

static const int M = 128;
static const int B = M / 64; //2
static const int bz = M / B;  //64
static const int Batch = 2;
static const int Batchnlp = 1;
#if (MIC_AUDIO_RATE == 16000)
static const int frame_size = 256;
#endif
#if (MIC_AUDIO_RATE == 8000)
static const int frame_size = Batch * bz;
#endif
static const int fbuf_size = frame_size * sizeof(short);


static char nlpfixinited = 0;
static int *nlpfixrunbuf = NULL;
static int *nlpfixtempbuf = NULL;
/* static int *spfiltrunbuf = NULL; */
static int spfiltrunbuf[396/4];

static u8 *near = NULL;
static u8 *near_hband = NULL;
static u8 *near_lband = NULL;
static u8 *far = NULL;
static u8 *nlpout = NULL;
static u8 *nlpout_lband = NULL;

static u32 far_timestamp = 0;
static u8 busy = 0;
u8 dac_closed = 0; ////by frank for global symbol

#ifdef FILE_TEST
FILE *fnear;
FILE *ffar;
FILE *fout;
int fflen;
#endif

int (*nlpfix_output)(u8 *buf, int len);

void nlpfix_nearbuf(u8 *buf, int len)
{
    int ret = 0;
    if (!nlpfixinited) {
        return;
    }

#if Use_NLP
    if (busy == 0) {
        busy = 1;
        memcpy(near, buf, len);
        ret = dac_get_data(far, -adc_get_data_len(), len);
      ////by frank   dac_closed = (ret < 0) ? 1 : 0;
    }
#endif
}

void nlpfix_outbuf(u8 *buf, int len)
{
    if (nlpfix_output) {
        nlpfix_output(buf, len);
    }
    busy = 0;
}

int nlpfix_init(int(*outputfun)(u8 *buf, int len))
{
    if (nlpfixinited) {
        return 0;
    }

   //// printf("*********** nlp_Init ************\n");

# if Use_NLP
    int D = 128 * Batchnlp;

    int nlpfixrunbuf_size = EchoSuppress_QueryBufSize();
    int nlpfixtempbuf_size = EchoSuppress_QueryTempBufSize();

   //// printf("process size:%d\n", D);
   //// printf("echosup bufsize:%d\n",	nlpfixrunbuf_size);
   //// printf("echosup tmpbufsize:%d\n", nlpfixtempbuf_size);

    nlpfixrunbuf = (int *)malloc(sizeof(int) * (nlpfixrunbuf_size / sizeof(int)));
    nlpfixtempbuf = (int *)malloc(sizeof(int) * (nlpfixtempbuf_size / sizeof(int)));
    if (!nlpfixrunbuf || !nlpfixtempbuf) {
        printf("nlp run buffer malloc err\n");
        goto err;
    }

    near = malloc(fbuf_size);
    far = malloc(fbuf_size);
    nlpout = malloc(fbuf_size);
#if (MIC_AUDIO_RATE == 16000)
    nlpout_lband = malloc(fbuf_size / 2);
    near_hband = malloc(fbuf_size / 2);
    near_lband = malloc(fbuf_size / 2);
#endif
    if (!near || !far || !nlpout) {
        printf("nlp io buffer malloc err\n");
        goto err;
    }

    memset(nlpfixrunbuf, 0, nlpfixrunbuf_size);
    memset(nlpfixtempbuf, 0, nlpfixtempbuf_size);

    EchoSuppress_Initialize(nlpfixrunbuf,
                            AggressFactor,
                            MiniSuppress,
                            OverSuppressThr,
                            OverSuppress);

    int spfiltrunbuf_size = SplittingFilter_QueryBufSize(1);
    int spfilttempbuf_size = SplittingFilter_QueryTempBufSize(D);

   //// printf("process size:%d\n", D);
 ////   printf("spfilt bufsize:%d\n",	spfiltrunbuf_size);
   //// printf("spfilt tmpbufsize:%d\n", spfilttempbuf_size);

    /* spfiltrunbuf = (int *)malloc(sizeof(int) * (spfiltrunbuf_size / sizeof(int))); */
    memset(spfiltrunbuf, 0, spfiltrunbuf_size);
    SplittingFilter_Init(spfiltrunbuf, 1);

#endif

    far_timestamp = 0;

    nlpfix_output = outputfun;

    nlpfixinited = 1;



    ////printf("*********** nlp_Init end  ************\n");
    return 0;

err:
    /* if (spfiltrunbuf) { */
    /* free(spfiltrunbuf); */
    /* spfiltrunbuf = NULL; */
    /* } */
    if (nlpfixrunbuf) {
        free(nlpfixrunbuf);
        nlpfixrunbuf = NULL;
    }
    if (nlpfixtempbuf) {
        free(nlpfixtempbuf);
        nlpfixtempbuf = NULL;
    }
    if (near) {
        free(near);
        near = NULL;
    }
    if (near_hband) {
        free(near_hband);
        near_hband = NULL;
    }
    if (near_lband) {
        free(near_lband);
        near_lband = NULL;
    }
    if (far) {
        free(far);
        far = NULL;
    }
    if (nlpout) {
        free(nlpout);
        nlpout = NULL;
    }
    if (nlpout_lband) {
        free(nlpout_lband);
        nlpout_lband = NULL;
    }
    return -1;
}

void nlpfix_close()
{
    nlpfixinited = 0;
#if Use_NLP
    /* if (spfiltrunbuf) { */
    /* free(spfiltrunbuf); */
    /* spfiltrunbuf = NULL; */
    /* } */
    if (nlpfixrunbuf) {
        free(nlpfixrunbuf);
        nlpfixrunbuf = NULL;
    }
    if (nlpfixtempbuf) {
        free(nlpfixtempbuf);
        nlpfixtempbuf = NULL;
    }
    if (near) {
        free(near);
        near = NULL;
    }
    if (near_hband) {
        free(near_hband);
        near_hband = NULL;
    }
    if (near_lband) {
        free(near_lband);
        near_lband = NULL;
    }
    if (far) {
        free(far);
        far = NULL;
    }
    if (nlpout) {
        free(nlpout);
        nlpout = NULL;
    }
    if (nlpout_lband) {
        free(nlpout_lband);
        nlpout_lband = NULL;
    }
#endif
}

void nlpfix_process()
{
    if (nlpfixinited) {
        if (busy == 1) {
#if Use_NLP
            if (dac_closed) {
                //dac close状态,直接输出near mic数据,不执行算法
                nlpfix_outbuf(near, fbuf_size);
                return;
            }
#if (MIC_AUDIO_RATE == 16000)
            SplittingFilter_Analyse(spfiltrunbuf, nlpfixtempbuf, (short *)near, (short *)near_hband, (short *)near_lband, frame_size);
            EchoSuppress_Process_Run(nlpfixrunbuf,
                                     nlpfixtempbuf,
                                     (short *)far,
                                     (short *)near_lband,
                                     (short *)near_lband,
                                     (short *)nlpout_lband,
                                     (short *)near_hband,
                                     (short *)near_hband,
                                     frame_size / 2);
            /* memset(near_hband,0,frame_size/2*sizeof(short)); */
            SplittingFilter_Synthesize(spfiltrunbuf, nlpfixtempbuf, (short *)near_hband, (short *)nlpout_lband, (short *)nlpout, frame_size / 2);
#endif
#if (MIC_AUDIO_RATE == 8000)
            EchoSuppress_Process_Run(nlpfixrunbuf,
                                     nlpfixtempbuf,
                                     (short *)far,
                                     (short *)near,
                                     (short *)near,
                                     (short *)nlpout,
                                     NULL,
                                     NULL,
                                     frame_size);
#endif
           /*  memcpy(nlpout, far, fbuf_size);*/
           /* memset(nlpout,0,fbuf_size);*/
#else
            memcpy(nlpout, near, fbuf_size);
#endif
#ifdef FILE_TEST
            if (fflen < 30 * 1024) {
                if (!fnear) {
                    fnear = fopen(SD0_ROOT_PATH"near.pcm", "w+");
                }

                if (!ffar) {
                    ffar = fopen(SD0_ROOT_PATH"far.pcm", "w+");
                }

                /* if(!fout){ */
                /* fout = fopen(SD0_ROOT_PATH"out.pcm", "w+"); */
                /* } */

                fflen += fbuf_size;
            } else if (fflen < 100 * 1024) {
                if (fnear) {
                    fwrite(fnear, near, fbuf_size);
                }

                if (ffar) {
                    fwrite(ffar, far, fbuf_size);
                }

                /* if(fout){ */
                /* fwrite(fout, nlpout, fbuf_size); */
                /* } */

                fflen += fbuf_size;
            } else {
                if (fnear) {
                    fclose(fnear);
                    fnear = NULL;
                }
                if (ffar) {
                    fclose(ffar);
                    ffar = NULL;
                }
                /* if(fout){ */
                /* fclose(fout);	 */
                /* fout = NULL; */
                /* } */
                printf("nlp file test end\n");
            }
#endif
            nlpfix_outbuf(nlpout, fbuf_size);
            /* nlpfix_outbuf(near, fbuf_size); */
            /* nlpfix_outbuf(far, fbuf_size); */
        }
    }
}


#endif

void gpio_TestIO2(int value)
{
    /* JL_PORTA->DIR &= ~BIT(3); */
    /* if (value) { */
    /* JL_PORTA->OUT |= BIT(3); */
    /* } else { */
    /* JL_PORTA->OUT &= ~BIT(3); */
    /* } */

}

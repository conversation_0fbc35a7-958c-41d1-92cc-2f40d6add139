
#include "fs.h"
#include "printf.h"
#include "app_config.h"
#include "jiffies.h"

/* #define TEST_SD_FILE */

#ifdef TEST_SD_FILE

void sdcard_write_test()
{
    u8 wbuf[] = "hello world!\nsdcard_test\n";

    FILE *fp = fopen(SD0_ROOT_PATH"TEST.txt", "w+");
    if (fp) {
        printf("sdcard test write:\n %s\n", wbuf);
        fwrite(fp, wbuf, sizeof(wbuf));
        fclose(fp);
    } else {
        printf("sdcard test faild: fopen err\n");
    }
}

void sdcard_read_test()
{
    u8 rbuf[512];
    FILE *fp = fopen(SD0_ROOT_PATH"TEST.txt", "w+");
    if (fp) {
        fread(fp, rbuf, sizeof(rbuf));
        printf("sdcard test read:\n %s\n", rbuf);
        fclose(fp);
    } else {
        printf("sdcard test faild: fopen err\n");
    }

}

#define TEST_LEN 32*1024	//单包长度
#define TEST_CNT 2000	//每文件包数
#define TEST_NUM 10		//文件数

void sdcard_speed_test()
{
    char filename[128];
    FILE *fp = NULL;


    printf("sdcard speed test begin\n");

    printf("prepare data\n");
    u8 *test_data = malloc(TEST_LEN);
    if (!test_data) {
        printf("malloc err\n");
        goto end;
    }
    for (int i = 0; i < TEST_LEN / 4; i++) {
        ((u32 *)test_data)[i] = i;
    }

    printf("speed test start\n");
    u32 time_start = jiffies_to_msecs(get_jiffies());
    u32 time_stamp;
    u32 speed;

    for (int i = 0; i < TEST_NUM; i++) {
        time_stamp = jiffies_to_msecs(get_jiffies());
        sprintf(filename, "%sSPEED%02d.TXT", SD0_ROOT_PATH, i);
        printf("write file %s start\n", filename);
        fp = fopen(filename, "w+");
        if (!fp) {
            printf("sdcard test faild: fopen err\n");
            break;
        }
        for (int j = 0; j < TEST_CNT; j++) {
            /* putchar('w'); */
            wdt_clr();
            fwrite(fp, test_data, TEST_LEN);
        }
        fclose(fp);
        speed = TEST_LEN * TEST_CNT / (jiffies_to_msecs(get_jiffies()) - time_stamp) * 1000 / 1024;
        printf("write file end, speed = %d KB/S\n", speed);
        fp = NULL;
    }

end:
    if (test_data) {
        free(test_data);
    }
    printf("sdcard speed test end\n");

}


#endif

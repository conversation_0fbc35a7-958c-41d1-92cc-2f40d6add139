
#include "printf.h"
#include "app_config.h"
#include "jiffies.h"
#include "sleep.h"

void sleep_test(void)
{
    puts("sleep_test\n");
    // PA6 ,唤醒输入源8 , 上升沿唤醒，开启过滤
    set_wakeup_port(WAKEUP_PORT0_INPUT_CHANNEL8, 0, IO_PORTA_06, 0, WAKEUP_EDGE_UP, WAKEUP_FLEN_ENABLE);
    //进入睡眠,等待唤醒源唤醒 (睡眠模式功耗在700uA左右)
    enter_sleep_mode();

    //唤醒后退出睡眠
    exit_sleep_mode();

    /** 以下为恢复操作,根据应用逐个恢复**/
#if UART_DEBUG
    void uart_debug_init();
    uart_debug_init();
#endif
    //重新挂载isp0
    void set_isp0_abnormal();
    set_isp0_abnormal();
    //重新初始化usb从机模式
    void usb_slave_start();
    usb_slave_start();
}

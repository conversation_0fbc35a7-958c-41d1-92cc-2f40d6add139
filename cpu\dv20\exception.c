
#include "printf.h"
#include "csfr.h"
#include "uart.h"
#include "clock.h"
#include "hwi.h"
#include "irq.h"

#define _DSP_PC_LIML0			q32small(0)->LIM_PC0_L
#define _DSP_PC_LIMH0			q32small(0)->LIM_PC0_H
#define _DSP_PC_LIML1			q32small(0)->LIM_PC1_L
#define _DSP_PC_LIMH1			q32small(0)->LIM_PC1_H
#define _DSP_PC_LIML2			q32small(0)->LIM_PC2_L
#define _DSP_PC_LIMH2			q32small(0)->LIM_PC2_H
#define _EMU_CON				q32small(0)->EMU_CON
#define _EMU_MSG				q32small(0)->EMU_MSG
#define _EMU_SSP_H              q32small(0)->EMU_SSP_H
#define _EMU_SSP_L              q32small(0)->EMU_SSP_L
#define _EMU_USP_H              q32small(0)->EMU_USP_H
#define _EMU_USP_L              q32small(0)->EMU_USP_L
#define _ETM_CON                q32small(0)->ETM_CON
#define _ESU_CON                q32small(0)->ESU_CON

#define EMU_MISALIGN_EN     (1 << 0)
#define EMU_ILLEGAL_EN      (1 << 1)
#define EMU_DIV0_EN         (1 << 2)
#define EMU_SP_OV_EN        (1 << 3)
#define EMU_PC_LIMIT_EN     (1 << 4)
#define EMU_WP0_ERR_EN      (1 << 8)
#define EMU_CACHE_ERR_EN    (1 << 30)
#define EMU_SYS_ERR_EN      (1 << 31)

static char *const emu_msg[32] = {
    "sys excption",		//31
    "cache excption",		//30
    "reserved",		//29
    "reserved",		//28

    "reserved",		//27
    "reserved",		//26
    "reserved",		//25
    "reserved",		//24

    "reserved",		//23
    "reserved",		//22
    "reserved",		//21
    "reserved",		//20

    "reserved",	//19
    "reserved",		//18
    "reserved",	//17
    "reserved",	//16

    "reserved",		//15
    "reserved",		//14
    "reserved",		//13
    "reserved",		//12

    "reserved",		//11
    "reserved",		//10
    "reserved",		//9
    "etm check point 0 err ",		//8

    "reserved",		//7
    "reserved",		//6
    "reserved",		//5
    "pc_limit",		//4

    "stack overflow err",		//3
    "div0_err",		//2
    "illegal_err",	//1
    "misalign_err",	//0
};

/*--------------------------------------------------
                 PC越界检测设置
--------------------------------------------------*/
void pc_rang_limit0(void *low_addr, void *high_addr)
{
    _DSP_PC_LIML0 = (u32)low_addr;
    _DSP_PC_LIMH0 = (u32)high_addr;
}

void pc_rang_limit1(void *low_addr, void *high_addr)
{
    _DSP_PC_LIML1 = (u32)low_addr;
    _DSP_PC_LIMH1 = (u32)high_addr;
}

void pc_rang_limit2(void *low_addr, void *high_addr)
{
    _DSP_PC_LIML2 = (u32)low_addr;
    _DSP_PC_LIMH2 = (u32)high_addr;
}

static void trace_call_stack(void)
{
    puts(" trace: ");
    printf("%x --> %x --> %x --> %\n", q32small(0)->ETM_PC3, q32small(0)->ETM_PC2, q32small(0)->ETM_PC1, q32small(0)->ETM_PC0);


}

SET_INTERRUPT
void exception_isr(void)
{
    int i = 0;
    u32 rets, reti;
    u32 sp, ssp, usp;
    __asm__ volatile("%0 = rets" : "=r"(rets));
    __asm__ volatile("%0 = reti" : "=r"(reti));
    __asm__ volatile("%0 = sp" : "=r"(sp));
    __asm__ volatile("%0 = ssp" : "=r"(ssp));
    __asm__ volatile("%0 = usp" : "=r"(usp));

    for (i = 0; i < 32; i++) {
        if (_EMU_MSG & BIT(i)) {
            printf("\r\n[0-CPU] emu err msg : %x ", _EMU_MSG);
            printf(emu_msg[31 - i]);
        }
    }
    trace_call_stack();
    printf("\n%s() \nrets: %x reti: %x sp:%x ssp:%x usp:%x\n\n", __func__,
           rets, reti,
           sp, ssp, usp);


    while (1);
}
static void emu_stack_limit_set(u8 mode, u32 limit_l, u32 limit_h)
{
    if (mode) {
        _EMU_SSP_H = limit_h;
        _EMU_SSP_L = limit_l;
        printf("SSP_H : 0x%x\n", _EMU_SSP_H);
        printf("SSP_L : 0x%x\n", _EMU_SSP_L);
    } else {
        _EMU_USP_H = limit_h;
        _EMU_USP_L = limit_l;
        printf("USP_H : 0x%x\n", _EMU_USP_H);
        printf("USP_L : 0x%x\n", _EMU_USP_L);
    }
}
extern const u8 _stack_begin[];
extern const u8 _stack_end[];

void exception_irq_hookfun(u32 *sp)
{
    u32 reti = sp[16];
    u32 rete = sp[17];
    u32 retx = sp[18];
    u32 rets = sp[19];
    u32 psr  = sp[20];
    u32 icfg = sp[21];
    u32 usp  = sp[22];
    u32 ssp  = sp[23];

    printf("\n\n********  %s()  *******\n\n", __func__);
    printf("reti = ");
    put_u32hex(reti);
    printf("rete = ");
    put_u32hex(rete);
    printf("retx = ");
    put_u32hex(retx);
    printf("rets = ");
    put_u32hex(rets);
    printf("psr  = ");
    put_u32hex(psr);
    printf("icfg = ");
    put_u32hex(icfg);
    printf("usp  = ");
    put_u32hex(usp);
    printf("ssp  = ");
    put_u32hex(ssp);

    trace_call_stack();

    u32 i = 0;
    //内核
    for (i = 0; i < 32; i++) {
        if (_EMU_MSG & BIT(i)) {
            printf("\n[0-CPU  ] emu err msg : 0x%x  ", _EMU_MSG);
            printf(emu_msg[31 - i]);
        }
    }

    printf("\n\n");
    for (i = 0; i < 16; ++i) {
        printf("r%d = ", i);
        put_u32hex(sp[i]);
    }

    while (1);
}
void exception_init(void)
{
    /* u32 usp = 0; */
    /* __asm__ volatile("%0 = usp" : "=r"(usp)); */
    /* printf("usp======0x%x\n", usp); */
    /* __asm__ volatile("%0 = sp" : "=r"(usp)); */
    /* printf("sp======0x%x\n", usp); */
    /* _EMU_CON = EMU_MISALIGN_EN | EMU_ILLEGAL_EN | EMU_DIV0_EN | EMU_PC_LIMIT_EN | EMU_SYS_ERR_EN | EMU_SP_OV_EN; */
    /* _ETM_CON = BIT(0);// pc trace en */
    /* emu_stack_limit_set(0, (u32)&_stack_begin[0], (u32)&_stack_end[0]); */
    /* request_irq(IRQ_EXCEPTION_IDX, 7, exception_isr, 0); */
    void register_exception_irq_hook(void *exp_hook);
    register_exception_irq_hook((void *)exception_irq_hookfun);
}
__attribute__((noinline)) static int div0_test(int c)
{
    int a;
    int b = 3;
    a = c;
    b = b - c;
    printf(" %d / %d = %d", a, b, a / b);
    return a;
}
__attribute__((noinline))
void debug_test()
{
    /* void pc_rang_limit0(void *low_addr, void *high_addr); */
    /* pc_rang_limit0(test_buf, test_buf + 1); */
    /* _DSP_PC_LIMH0 = 0x12c4; */
    /* _DSP_PC_LIML0 = 0x12c0; */

    /* printf("low addr=0x%x\n",_DSP_PC_LIML0 ); */
    /* printf("high addr=0x%x\n",_DSP_PC_LIMH0 ); */
    /* test_buf[0] = 0x55; */
    /* printf(" EMU CON = %x", _EMU_CON); */
#if 0 //栈溢出测试
    /* u32 usp = 0; */
    /* __asm__ volatile("%0 = usp" : "=r"(usp)); */
    /* printf("usp=0x%x\n",usp ); */
    //
    volatile u8 buf[6 * 1024];
    /* __asm__ volatile("%0 = sp" : "=r"(sp)); */
    /* asm("sp = 0x2000"); */
    /* printf("sp=0x%x\n",sp ); */
    /* printf("\nbuf:%x\n",buf); */
    /* memset(buf,0x5a,sizeof(buf)); */
    for (int i = 0; i < 6 * 1024; i++) {
        buf[i] = 0x5a;
    }
    asm("trigger");
    /* printf_buf(buf,6*1024); */
#endif
#if 0 //除0异常
    //div 0
    div0_test(1);
    div0_test(3);  //div0
#endif
#if 0
//非法指令异常
    u8 data[] = {0x5A, 0x55, 0xAB, 0xCD};
    typedef void (*FUN)(void);
    FUN fun = (FUN)0x10000;
    /* printf("instruction @ 0x%x\n", data); */
    /* FUN fun = (FUN)data; */
    memcpy(fun, data, sizeof(data));
    fun();
#endif

    //=========================================//
    //               非对齐访问                //
    //=========================================//
#if 0 //misalign test
    //miss align
    volatile u32 *p = (volatile u32 *)0x10001;
    //编译器会通过按byte写的方式编译为对齐
    *p = 0x12345678;
    int v = 0x12345678;
    printf("%x %x  ", p, *p); //编译器会通过按byte读的方式编译为对齐
    asm("trigger ");

#if 1 //misalign write test
    __asm__ volatile("[%0]= %1"
                     ::"r"(p), "r"(v));
#endif /* #if 0 //misalign write test */

#if 1 //misalign read test
    __asm__ volatile("%0 = [%1]"
                     : "=r"(v)
                     : "r"(p));
    printf("mis aline %x\n", *p);
#endif /* #if 0 //misalign read test */

#endif /* #if 1 //misalign test */

}

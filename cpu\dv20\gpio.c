#include "gpio.h"
#include "string.h"
#include "printf.h"
#ifndef EINVAL
#define EINVAL      22  /* Invalid argument */
#endif
#define     PORTR_HD(gpio,value)    -EINVAL
#define     PORTR_HD0(gpio,value)    -EINVAL
#define     PORTR_DIR(gpio,value)    -EINVAL
#define     PORTR_PD(gpio,value)  -EINVAL
#define     PORTR_PU(gpio,value)  -EINVAL
#define     PORTR_DIE(gpio,value) -EINVAL
#define     PORTR_OUT(gpio,value) -EINVAL
#define     PORTR_IN(gpio)  -EINVAL
#define     PORTR_DIEH(gpio,value)    -EINVAL

#define     CONFIG_GPIO_SPIN_LOCK 0

#if CONFIG_GPIO_SPIN_LOCK
#else
#define spin_lock(lock)
#define spin_unlock(lock)
#endif
// *INDENT-ON*
const u32 gpio_regs[] = {
    (u32) JL_PORTA,
    (u32) JL_PORTB,
    (u32)(-1),
    (u32) JL_PORTD,
};

#if ENABLE_USB_IO
__attribute__((always_inline_when_const_args))
u32 usb_set_direction(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio + 2);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio + 2);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_output(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_set_pull_up(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio + 4);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio + 4);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_set_pull_down(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio + 6);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio + 6);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_set_die(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio + 8);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio + 8);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_set_dieh(u32 gpio, u32 value)
{
    gpio -= USB_IO_OFFSET;
    if (value) {
        JL_USB_IO->CON0 |= BIT(gpio + 10);
    } else {
        JL_USB_IO->CON0 &= ~BIT(gpio + 10);
    }
    return 0;
}
__attribute__((always_inline_when_const_args))
u32 usb_read(u32 gpio)
{
    u32 v;
    gpio -= USB_IO_OFFSET;
    v = JL_USB->CON0 & (BIT(17 - gpio));
    return !!v;
}
__attribute__((always_inline_when_const_args))
void usb_iomode(u32 enable)
{
    if (enable) {
        JL_USB->CON0 = 0;//USB_PHY_OFF
        JL_USB_IO->CON0 |= BIT(12) | BIT(14);
    } else {
        JL_USB_IO->CON0 &= ~(BIT(12) | BIT(14));
    }
}
#endif


__attribute__((always_inline_when_const_args))
u32 get_gpio(const char *p)
{
    u32 port;
    if (p[0] == 'P') {
        if (p[1] >= 'A' && p[1] <= 'E') {
            port = p[1] - 'A';
            port *= IO_GROUP_NUM;
        } else {
            return -EINVAL;
        }

        int mask = (p[2] - '0') * 10 + (p[3] - '0');
        if (mask < 16) {
            port += mask;
            return port;
        }
    }
#if ENABLE_USB_IO
    if (memcmp(p, "USBDP", strlen("USBDP")) == 0) {
        return  IO_PORT_DP;
    }
    if (memcmp(p, "USBDM", strlen("USBDM")) == 0) {
        return  IO_PORT_DM;
    }
#endif
    return -EINVAL;
}
//===================================================//
//  Crossbar API
//===================================================//
static const u32 gpio_out_regs[] = {
    (u32) &(JL_OMAP->PA0_OUT),
    (u32) &(JL_OMAP->PB0_OUT),
    (u32)(0),
    (u32) &(JL_OMAP->PD0_OUT),
};

static const u32 gpio_in_regs[] = {
    (u32) PA0_IN,
    (u32) PB0_IN,
    (u32)(-1),
    (u32) PD0_IN,
};

u32 *gpio2crossbar_outreg(u32 gpio)
{
    u32 *reg = NULL;

    /* if (gpio == IO_PORT_DP) { */
    /* return (u32 *)(&(JL_OMAP->USBDP_OUT)); */
    /* } */
    /* if (gpio == IO_PORT_DM) { */
    /* return (u32 *)(&(JL_OMAP->USBDM_OUT)); */
    /* } */

    if (gpio > IO_MAX_NUM) {
        return NULL;
    }
    u32 group_index = gpio / IO_GROUP_NUM;
    u32 port_index = gpio % IO_GROUP_NUM;

    reg = (u32 *)gpio_out_regs[group_index];

    return reg + port_index;
}


u32 gpio2crossbar_inport(u32 gpio)
{
    /* if (gpio == IO_PORT_DP) { */
    /* return USBDP_IN; */
    /* } */
    /* if (gpio == IO_PORT_DM) { */
    /* return USBDM_IN; */
    /* } */

    if (gpio > IO_MAX_NUM) {
        return -1;
    }
    u32 group_index = gpio_in_regs[gpio / IO_GROUP_NUM];
    u32 port_index = gpio % IO_GROUP_NUM;

    return group_index + port_index;
}


__attribute__((always_inline_when_const_args))
u32 gpio_set_direction(u32 gpio, u32 dir)
{
    u32 mask;
    struct gpio_reg *g;
#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_iomode(1);
            usb_set_direction(gpio - IO_MAX_NUM, dir);
        } else {
            PORTR_DIR(gpio - IO_MAX_NUM, dir);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);

    spin_lock(&lock);
    if (dir) {
        g->dir |= mask;
    } else {
        g->dir &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_direction_output(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_set_direction(gpio - IO_MAX_NUM, 0);
            usb_output(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_DIR(gpio - IO_MAX_NUM, 0);
            PORTR_OUT(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    g->dir &= ~mask;
    if (value == 1) {
        g->out |= mask;
    } else if (value == 0) {
        g->out &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_direction_input(u32 gpio)
{
    u32 mask;
    struct gpio_reg *g;
#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_iomode(1);
            usb_set_direction(gpio - IO_MAX_NUM, dir);
        } else {
            PORTR_DIR(gpio - IO_MAX_NUM, dir);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);

    spin_lock(&lock);
    g->dir |= mask;
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_write(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_output(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_OUT(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value == 1) {
        g->out |= mask;
    } else if (value == 0) {
        g->out &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_read(u32 gpio)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            return usb_read(gpio - IO_MAX_NUM);
        }
        return PORTR_IN(gpio - IO_MAX_NUM);
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);

    spin_lock(&lock);
    u32 v = !!(g->in & mask);
    spin_unlock(&lock);

    return v;
}
__attribute__((always_inline_when_const_args))
u32 gpio_in(u32 gpio)
{
    u32 mask;
    struct gpio_reg *g;
    u32 v = 0;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            return usb_read(GPIOUSB + 0) | (usb_read(GPIOUSB + 1) << 1);
        }
        for (int i = 0; i < 3; i++) {
            v = (!!PORTR_IN(GPIOR + i)) << i;
        }
        return v;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);

    spin_lock(&lock);
    v = g->in;
    spin_unlock(&lock);

    return v;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_pull_up(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_set_pull_up(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_PU(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->pu |= mask;
    } else {
        g->pu &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_pull_down(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_set_pull_down(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_PD(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->pd |= mask;
    } else {
        g->pd &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_hd0(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            return -EINVAL;
        } else {
            PORTR_HD0(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->hd0 |= mask;
    } else {
        g->hd0 &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_hd(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            return -EINVAL;
        } else {
            PORTR_HD(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->hd |= mask;
    } else {
        g->hd &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_die(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_set_die(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_DIE(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->die |= mask;
    } else {
        g->die &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_set_dieh(u32 gpio, u32 value)
{
    u32 mask;
    struct gpio_reg *g;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            usb_set_dieh(gpio - IO_MAX_NUM, value);
        } else {
            PORTR_DIEH(gpio - IO_MAX_NUM, value);
        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    mask = __gpio_mask(gpio);
    spin_lock(&lock);
    if (value) {
        g->dieh |= mask;
    } else {
        g->dieh &= ~mask;
    }
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_die(u32 gpio, u32 start, u32 len, u32 dat, enum gpio_op_mode op)
{
    struct gpio_reg *g;

    start = start > gpio ? start - gpio : start;
#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            for (int i = start ; i < len ; i++) {
                if (dat & BIT(i)) {
                    usb_set_die(GPIOUSB + i, 1);
                } else {
                    usb_set_die(GPIOUSB + i, 0);
                }
            }
        } else {
            for (int i = start ; i < len ; i++) {
                if (dat & BIT(i)) {
                    PORTR_DIE(GPIOR + i, 1);
                } else {
                    PORTR_DIE(GPIOR + i, 0);
                }
            }

        }
        return 0;
    }
#endif

    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    spin_lock(&lock);
    u32 sfr = g->die;
    switch (op) {
    case GPIO_SET:
        SFR(sfr, start, len, dat);
        break;
    case GPIO_AND:
        sfr &= (dat << start);
        break;
    case GPIO_OR:
        sfr |= (dat << start);
        break;
    case GPIO_XOR:
        sfr ^= (dat << start);
        break;
    }
    g->die = sfr;
    spin_unlock(&lock);

    return 0;
}
__attribute__((always_inline_when_const_args))
u32 gpio_dieh(u32 gpio, u32 start, u32 len, u32 dat, enum gpio_op_mode op)
{
    struct gpio_reg *g;

    start = start > gpio ? start - gpio : start;

#if ENABLE_USB_IO
    if (gpio >= IO_MAX_NUM) {
        if (gpio >= IO_PORT_DP) {
            for (int i = start ; i < len ; i++) {
                if (dat & BIT(i)) {
                    usb_set_dieh(GPIOUSB + i, 1);
                } else {
                    usb_set_dieh(GPIOUSB + i, 0);
                }
            }
        } else {
            for (int i = start ; i < len ; i++) {
                if (dat & BIT(i)) {
                    PORTR_DIEH(GPIOR + i, 1);
                } else {
                    PORTR_DIEH(GPIOR + i, 0);
                }
            }

        }
        return 0;
    }
#endif


    g = gpio2reg(gpio);
#if defined(__UBOOT)
    if (!g) {
        return -EINVAL;
    }
#endif

    spin_lock(&lock);
    u32 sfr = g->dieh;
    switch (op) {
    case GPIO_SET:
        SFR(sfr, start, len, dat);
        break;
    case GPIO_AND:
        sfr &= (dat << start);
        break;
    case GPIO_OR:
        sfr |= (dat << start);
        break;
    case GPIO_XOR:
        sfr ^= (dat << start);
        break;
    }
    g->dieh = sfr;
    spin_unlock(&lock);

    return 0;
}
u32 io_test[] =  {
    /* IO_PORTA_00, */
    IO_PORTA_01,
    IO_PORTA_02,
    IO_PORTA_03,
    IO_PORTA_04,
    IO_PORTA_05,
    IO_PORTA_06,
    IO_PORTA_07,
    IO_PORTA_08,
    IO_PORTA_09,
    IO_PORTA_10,

    /* IO_PORTB_00, */
    IO_PORTB_01,
    IO_PORTB_02,

    /* IO_PORTC_03, */
    /* IO_PORTC_04, */
    /* IO_PORTC_05, */
    /* IO_PORTC_06, */
    /* IO_PORTC_07, */
};
void gpio_test()
{
    /* asm("trigger");                              */
    for (u8 i = 0; i < sizeof(io_test) / sizeof(io_test[0]); i++) {
        /* gpio_set_direction(io_test[i], 1); */
        gpio_direction_input(io_test[i]);
        gpio_set_pull_up(io_test[i], 1);
        if (!gpio_read(io_test[i])) {
            printf(">> %d :%d\n", io_test[i], gpio_read(io_test[i]));
        }
    }
    /* delay_1ms(50); */
    /* } */

    /* gpio_set_direction(get_gpio("PA03"),1);      */
    /* gpio_direction_output(get_gpio("PA03"),0);   */
    /* gpio_set_pull_up(get_gpio("PA03"),0);        */
    /* asm("trigger");                              */
    /* gpio_direction_output(get_gpio("USBDP"),1);  */
    /* asm("trigger");                              */
    /* gpio_direction_output(IO_PORT_DP,1);         */
    /* asm("trigger");                              */
    /* gpio_out(GPIOA,1,3,3);                       */
    /* gpio_set_pull_up(IO_PORTA_00,1);             */
    /* gpio_set_pu(GPIOA,0,3,3,GPIO_AND);           */
    /* gpio_set_pull_down(get_gpio("PB01"),1);      */
    /* gpio_set_pd(GPIOB,0,3,2,GPIO_XOR);           */
    /* gpio_set_hd0(IO_PORTA_00,1);                 */
    /* gpio_set_hd(IO_PORTA_00,1);                  */
    /* gpio_set_die(IO_PORTA_00,0);                 */
    /* gpio_set_dieh(IO_PORTA_00,0);                */
    /* gpio_die(GPIOA,0,4,0xf,GPIO_XOR);            */
    /* gpio_dieh(GPIOA,0,4,0xf,GPIO_AND);           */
    /* gpio_read(IO_PORTA_01);                      */
    /* gpio_dir(get_gpio("PA03"),4,1,1,GPIO_OR);    */
    /* asm("trigger");                              */
    /* gpio_set_pull_up(IO_PORT_DM,1);              */
    /* asm("trigger");                              */
    /* const u32 port = get_gpio("USBDP");          */
    /* gpio_set_pull_up(get_gpio("USBDP"),0);       */
    /* gpio_set_pull_up(port,1);                    */
    /* gpio_direction_output(get_gpio("USBDP"),1);  */
}

#include "video.h"
#include "jpeg_encoder.h"
#include "jpeg_abr.h"
#include "yuv_recorder.h"
#include "gpio.h"
#include "app_config.h"
#include "isp_customize.h"
#include "isp_scenes.h"
#include "osd.h"
#include "delay.h"
#include "clock.h"
#include "cpu.h"

static void *imc0 = NULL;
static volatile u16 frame_done;
SET_INTERRUPT
static void __imc_isr_handler()
{
    if (!imc0) {
        puts("imc handler null!!!\n");
        return ;
    }
    struct imc_ch_reg *reg = (struct imc_ch_reg *)imc0;
    if (reg->scale->com_con & BIT(9)) {
        putchar('L');
        reg->scale->com_con |= BIT(8);
    }
    if (reg->scale->com_con & BIT(11)) {
        /* printf("frame done\n"); */
        putchar('F');
        reg->scale->com_con |= BIT(10);
        frame_done = 1;
        reg->scale->com_con &= ~BIT(0); //imc close
    }
    if (reg->scale->com_con & BIT(13)) {
        printf("imc bandwidth err\n");
        reg->scale->com_con |= BIT(12);
    }
    if (reg->scale->com_con & BIT(15)) {
        printf("imc line error\n");
        reg->scale->com_con |= BIT(14);
    }
    if (reg->scale->com_con & BIT(17)) {
        printf("imc osd error\n");
        reg->scale->com_con |= BIT(16);
    }

}

int video_capture_image(u8 *out_buffer, u16 width, u16 height, u16 x, u16 y)
{
    struct camera_device_info camera_info = {0};

    /* const char *camera_name = "cam0";  //isp0 */
    const char *camera_name = "cam1"; //isp1

    eva_clk_init();
    eva_xbus_init();
    // set_board_ldo_power_init(); //开启电源

    void *camera = dev_open(camera_name, 0);

    dev_ioctl(camera, CAMERA_GET_SENSOR_INFO, (u32)&camera_info);

    printf("camera handle:%x, %d,%d tar: %d x %d\n", camera, camera_info.width, camera_info.height, width, height);
    /** imc open **/
    struct imc_iattr attr = {0};
    if (camera_name[strlen(camera_name) - 1] == '0') {
        attr.src = IMC_SRC_ISP0;
    } else {
        attr.src = IMC_SRC_ISP1;
    }
    attr.src_w = camera_info.width;
    attr.src_h = camera_info.height;
    attr.tar_w = width;
    attr.tar_h = height;
    attr.yuvformat = IMC_YUV420;
    attr.imc_buf_mode = IMC_ROUND_BUFF;
    attr.imc_int_cfg = IMC_32LINE;
    attr.crop_x = x;
    attr.crop_y = y;
    attr.crop_width = width;
    attr.crop_height = height;
    attr.dma_cnt = height; //32;
    attr.out_buf = out_buffer;
   //// printf("out buf %x\n", attr.out_buf);
    attr.mirror = 0; //水平镜像
    imc0 = imc_open(&attr);
    request_irq(IRQ_IMC_IDX, 0, __imc_isr_handler, 0);
    if (!imc0) {
        puts(">>> imc open faild!\n");
        return -1;
    }

    frame_done = 0;
    //imc kick start
    imc_kstart();

    /*
     *  等待imc 帧中断
     * */
    while (1) {
        if (frame_done == 1) {
            break;
        }
    }


    return 0;
}




#if 0
// 调用Demo
#define IMAGE_WIDTH   (640)
#define IMAGE_HEIGHT  (32)
static u8 outbuffer[IMAGE_WIDTH * IMAGE_HEIGHT * 2] sec(.encode_buf) ALIGNE(64); //定义imc buffer 32行 yuv422
void image_test_demo(void)
{
    int video_capture_image(u8 * out_buffer, u16 width, u16 height, u16 x, u16 y);
    static u32 x = 0;
    static u32 y = IMAGE_HEIGHT;
    video_capture_image(outbuffer, IMAGE_WIDTH, IMAGE_HEIGHT, x, y);
    y += IMAGE_HEIGHT;

    //test
    if (y > 480) {
        y = IMAGE_HEIGHT;
    }
}

#endif




#include "ldo.h"


void avdd18_ctl(u8 lev, u8 avdd18en)
{
    if (lev > 7) {
        lev = 7;
    }

    if (avdd18en) {
        JL_PORTB->PU   &= ~BIT(1);
        JL_PORTB->PD   &= ~BIT(1);
        JL_PORTB->DIEH &= ~BIT(1);
        JL_PORTB->DIE  &= ~BIT(1);
        JL_PORTB->DIR  |= BIT(1);
        SFR(JL_PWR->LDO_CON, 11, 3, lev);
        JL_PWR->LDO_CON |= BIT(2);
    } else {
        JL_PWR->LDO_CON &= ~BIT(2);
    }


}

void avdd28_ctl(u8 lev, u8 avdd28en)
{
    if (lev > 7) {
        lev = 7;
    }

    if (avdd28en) {
        JL_PORTB->PU   &= ~BIT(0);
        JL_PORTB->PD   &= ~BIT(0);
        JL_PORTB->DIEH &= ~BIT(0);
        JL_PORTB->DIE  &= ~BIT(0);
        JL_PORTB->DIR  |= BIT(0);
        SFR(JL_PWR->LDO_CON, 14, 3, lev);
        JL_PWR->LDO_CON |= BIT(3);
    } else {
        JL_PWR->LDO_CON &= ~BIT(3);
    }


}

void dvdd_ctl(u8 lev)
{
    if (lev > 7) {
        lev = 7;
    }

    SFR(JL_PWR->LDO_CON, 19, 3, lev);
}

void vbg_ctl(u8 lev)
{
    if (lev > 15) {
        lev = 15;
    }

    SFR(JL_PWR->LDO_CON, 4, 4, lev);
}

void iovdd_ctl(u8 lev)
{
    if (lev > 3) {
        lev = 3;
    }

    SFR(JL_PWR->LDO_CON, 9, 2, lev);
}

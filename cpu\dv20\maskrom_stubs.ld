  hwi_init = ABSOLUTE(0x10fc58);
  ENABLE_INT = ABSOLUTE(0x10fc5c);
  DISABLE_INT = ABSOLUTE(0x10fc60);
  HWI_Install = ABSOLUTE(0x10fc64);
  sys_clk_init = ABSOLUTE(0x10fc68);
  memmem = ABSOLUTE(0x10fc6c);
  memcpy = ABSOLUTE(0x10fc70);
  memmove = ABSOLUTE(0x10fc74);
  memcmp = ABSOLUTE(0x10fc78);
  memset = ABSOLUTE(0x10fc7c);
  strcmp = ABSOLUTE(0x10fc80);
  strcpy = ABSOLUTE(0x10fc84);
  strlen = ABSOLUTE(0x10fc88);
  strncmp = ABSOLUTE(0x10fc8c);
  strstr = ABSOLUTE(0x10fc90);
  strchr = ABSOLUTE(0x10fc94);
  dma_memcpy = ABSOLUTE(0x10fc98);
  IcuInitial = ABSOLUTE(0x10fc9c);
  wdt_clr = ABSOLUTE(0x10fca0);
  chip_reset = ABSOLUTE(0x10fca4);
  chip_crc16 = ABSOLUTE(0x10fca8);
  CrcDecode = ABSOLUTE(0x10fcac);
  flash_poweron = ABSOLUTE(0x10fcb0);
  doe = ABSOLUTE(0x10fcb4);
  LZ4_setStreamDecode = ABSOLUTE(0x10fcb8);
  LZ4_decompress_safe_continue = ABSOLUTE(0x10fcbc);
  LZ4_decompress_generic = ABSOLUTE(0x10fcc0);
  register_exception_irq_hook = ABSOLUTE(0x10fcc4);
  _devices_init = ABSOLUTE(0x10fcc8);
  _dev_open = ABSOLUTE(0x10fccc);
  dev_read = ABSOLUTE(0x10fcd0);
  dev_write = ABSOLUTE(0x10fcd4);
  dev_ioctl = ABSOLUTE(0x10fcd8);
  dev_close = ABSOLUTE(0x10fcdc);
  dev_bulk_read = ABSOLUTE(0x10fce0);
  dev_bulk_write = ABSOLUTE(0x10fce4);
  cbuf_init = ABSOLUTE(0x10fce8);
  cbuf_read = ABSOLUTE(0x10fcec);
  cbuf_write = ABSOLUTE(0x10fcf0);
  cbuf_is_write_able = ABSOLUTE(0x10fcf4);
  cbuf_write_alloc = ABSOLUTE(0x10fcf8);
  cbuf_write_updata = ABSOLUTE(0x10fcfc);
  cbuf_read_alloc = ABSOLUTE(0x10fd00);
  cbuf_read_updata = ABSOLUTE(0x10fd04);
  cbuf_clear = ABSOLUTE(0x10fd08);
  cbuf_get_writeptr = ABSOLUTE(0x10fd0c);
  cbuf_get_data_size = ABSOLUTE(0x10fd10);
  cbuf_get_readptr = ABSOLUTE(0x10fd14);
  msg_pool_init = ABSOLUTE(0x10fd18);
  sys_msg_post = ABSOLUTE(0x10fd1c);
  sys_event_clear = ABSOLUTE(0x10fd20);
  get_sys_msg = ABSOLUTE(0x10fd24);
  sys_tick_init = ABSOLUTE(0x10fd28);
  sys_timer_add = ABSOLUTE(0x10fd2c);
  sys_timeout_add = ABSOLUTE(0x10fd30);
  sys_timer_del = ABSOLUTE(0x10fd34);
  sys_timer_schedule = ABSOLUTE(0x10fd38);
  hw_iic_dev_ops = ABSOLUTE(0x10fd3c);
  jiffies = ABSOLUTE(0x10fd40);
  isp1_mount = ABSOLUTE(0x10fd44);
  isp1_ioctrl = ABSOLUTE(0x10fd48);
  isp_io_init = ABSOLUTE(0x10fd4c);
  isp_io_streamon = ABSOLUTE(0x10fd50);
  isp_io_streamoff = ABSOLUTE(0x10fd54);
  isp_io_set_size = ABSOLUTE(0x10fd58);
  sensor_init = ABSOLUTE(0x10fd5c);
  sensor_set_size_fps = ABSOLUTE(0x10fd60);
  sensor_get_ae_params = ABSOLUTE(0x10fd64);
  sensor_get_awb_params = ABSOLUTE(0x10fd68);
  sensor_get_iq_params = ABSOLUTE(0x10fd6c);
  sensor_power_ctrl = ABSOLUTE(0x10fd70);
  imc_open = ABSOLUTE(0x10fd74);
  imc_kstart = ABSOLUTE(0x10fd78);
  imc_close = ABSOLUTE(0x10fd7c);
  eva_clk_init = ABSOLUTE(0x10fd80);
  eva_xbus_init = ABSOLUTE(0x10fd84);
  jpeg_encoder_start = ABSOLUTE(0x10fd88);
  jpeg_encoder_stop = ABSOLUTE(0x10fd8c);
  jpeg_encoder_restart = ABSOLUTE(0x10fd90);
  parking_detect_init = ABSOLUTE(0x10fd94);
  get_parking_detect_value = ABSOLUTE(0x10fd98);
  get_parking_state = ABSOLUTE(0x10fd9c);
  usb_config = ABSOLUTE(0x10fda0);
  usb_var_init = ABSOLUTE(0x10fda4);
  usb_control_transfer = ABSOLUTE(0x10fda8);
  usb_device_mode = ABSOLUTE(0x10fdac);
  usb_device_init = ABSOLUTE(0x10fdb0);
  usb_device_set_class = ABSOLUTE(0x10fdb4);
  usb_g_set_intr_hander = ABSOLUTE(0x10fdb8);
  usb_set_interface_hander = ABSOLUTE(0x10fdbc);
  usb_add_desc_config = ABSOLUTE(0x10fdc0);
  usb_set_reset_hander = ABSOLUTE(0x10fdc4);
  usb_reset_interface = ABSOLUTE(0x10fdc8);
  usb_set_setup_recv = ABSOLUTE(0x10fdcc);
  usb_set_setup_hook = ABSOLUTE(0x10fdd0);
  usb_setup_init = ABSOLUTE(0x10fdd4);
  usb_setup_release = ABSOLUTE(0x10fdd8);
  usb_set_data_payload = ABSOLUTE(0x10fddc);
  usb_set_setup_phase = ABSOLUTE(0x10fde0);
  usb_get_setup_buffer = ABSOLUTE(0x10fde4);
  usb_release = ABSOLUTE(0x10fde8);
  usb_get_ep_buffer = ABSOLUTE(0x10fdec);
  uvc_register = ABSOLUTE(0x10fdf0);
  usb_start = ABSOLUTE(0x10fdf4);
  usb_stop = ABSOLUTE(0x10fdf8);
  usb_routine = ABSOLUTE(0x10fdfc);
  uvc_set_camera_info = ABSOLUTE(0x10fe00);
  msd_register_disk = ABSOLUTE(0x10fe04);
  msd_unregister_disk = ABSOLUTE(0x10fe08);
  isp_tool_init = ABSOLUTE(0x10fe0c);
  isp_tool_parser = ABSOLUTE(0x10fe10);
  at24xx_init = ABSOLUTE(0x10fe14);
  at24xx_buffer_read = ABSOLUTE(0x10fe18);
  run_rom_code = ABSOLUTE(0x10fe1c);
  maskrom_init = ABSOLUTE(0x10fe24);
  clk_get = ABSOLUTE(0x10fe28);
  print = ABSOLUTE(0x10fe2c);
  printf = ABSOLUTE(0x10fe30);
  puts = ABSOLUTE(0x10fe34);

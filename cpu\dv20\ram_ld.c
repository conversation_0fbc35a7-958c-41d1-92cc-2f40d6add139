// *INDENT-OFF*

#include "app_config.h"

//4是起始地址0x000004占的内存
RAM_LIMIT_L     = 0x0004;
RAM_LIMIT_H     = 0x20000;

ISR_SIZE        = 0x200;
ISR_BASE        = RAM_LIMIT_H - ISR_SIZE;

USB_UPGRAGE_INFO_SIZE = 0x10;
USB_UPGRAGE_INFO_ADDR = RAM_LIMIT_H - USB_UPGRAGE_INFO_SIZE;



RAM_START_ADDR  = 0x13ac;
RAM_END_ADDR    = 0x1f2c0;

MEMORY
{
    flash(rx)             : ORIGIN = 0x1e00120,			    LENGTH = 256K

    ram(rx)             : ORIGIN = RAM_START_ADDR ,	        LENGTH = RAM_END_ADDR - RAM_START_ADDR
    ram1(rx)            : ORIGIN = 0x94c ,	        LENGTH = 0x13ac - 0x94c

    irq_table(rw)       : ORIGIN = ISR_BASE,                LENGTH = ISR_SIZE
}
#include "maskrom_stubs.ld"
ENTRY(_uboot_start);
SECTIONS
{
    . = ORIGIN(irq_table);
    .irq_table ALIGN(4):
    {

    } > irq_table

    . = ORIGIN(flash);
    .text :SUBALIGN(4)
    {
		/*text*/
        . = ALIGN(4);
        *startup.o(.start*)
        *(.text*)

        . = ALIGN(4);
        *(.rodata.str*)
        *(.rodata*)

        . = (. + 4) / 4 * 4  ;//在startup.S中load data段是每次32位操作的，末尾必须对齐
    } > flash

    . = ORIGIN(ram1);
    .ram1_bss ALIGN(4):
    {
        *(.ram1_bss)

    } > ram1

    . = ORIGIN(ram);
    .data :SUBALIGN(4)
    {
        *(.volatile_ram_code)
#ifdef CONFIG_NLPFIX_ENABLE
        *(.fft_ram1_code)
        *(.fft_ram2_code)
        *(.fft_ram3_code)
        *(.fft_code)
#endif

        *(.data*)

        . = ALIGN(32);
        PROVIDE(device_node_begin = .);
        *(.device)
        PROVIDE(device_node_end = .);

        PROVIDE(vfs_ops_begin = .);
		*(.vfs_operations)
        PROVIDE(vfs_ops_end = .);

        camera_dev_begin = .;
        PROVIDE(camera_dev_begin = .);
        *(.camera_dev)
        camera_dev_end = .;
        PROVIDE(camera_dev_end = .);

        key_driver_begin = .;
        PROVIDE(key_driver_begin = .);
        *(.key_driver)
        key_driver_end = .;
        PROVIDE(key_driver_end = .);

        . = (. + 4) / 4 * 4  ;//在startup.S中load data段是每次32位操作的，末尾必须对齐
    } > ram

    .bss (NOLOAD) :
    {
        . = ALIGN(4);
        PROVIDE(_stack_begin = .);
        *(.stack)
        PROVIDE(_stack_end = .);
        . = (. + 4) / 4 * 4  ;
		*(.bss)

        . = ALIGN(64);
        *(.usb_ep0)
        *(.usb_msd_dma)
        *(.usb_iso_dma)
        *(.usb_config_var)
        *(.mass_storage)
        *(.encode_buf)
        . = ALIGN(4);
        . = (. + 4) / 4 * 4  ;//在startup.S中load data段是每次32位操作的，末尾必须对齐
    } > ram

    .bss_noinit (NOLOAD) :
    {
        *(.bss_noinit)

        . = (. + 4) / 4 * 4  ;
    } > ram

    .heap_buf ALIGN(32):
    {
        PROVIDE(_free_start = .);
        . = LENGTH(ram) + ORIGIN(ram) - 1;
        PROVIDE(_free_end = .);
    } > ram



    _IRQ_MEM_ADDR = ADDR(.irq_table);
    _USB_UPGRAGE_INFO_ADDR  = USB_UPGRAGE_INFO_ADDR;

    _text_size = SIZEOF(.text);

    _data_begin = ADDR(.data ) ;
    _data_size  = SIZEOF(.data);

    _data_lvma = ADDR(.text) + _text_size ;

    _bss_begin = ADDR(.bss ) ;
    _bss_size  = SIZEOF(.bss);

}

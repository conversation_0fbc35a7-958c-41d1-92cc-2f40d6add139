/*****************************************************************************************************************

(C) Copyright 2003 - Analog Devices, Inc.  All rights reserved.

File Name:		startup.asm

Date Modified:	2/26/03		LB		Rev 0.2

Software:       GNU DCC 4.1+

Hardware:		CM-BF533

Purpose:		Generic Startup file

************************************************************************************************/
#include "core.h"
.section .stack, "a"
         .space 0x800
         .global _stack
_stack:
        .space 0x800
        .global _ustack
_ustack:

.section .start;

    .extern _bss_begin
    .extern _bss_size
    .global _uboot_start
    .type _uboot_start,@function

_uboot_start:

	call boot_info_init 

    r0 = 0
    r1 = 0
    r2 = 0
    r3 = 0
    r4 = 0
    r5 = 0
    r6 = 0
    r7 = 0
    r8 = 0
    r9 = 0
    r10 = 0
    r11 = 0
    r12 = 0
    r13 = 0
    r14 = 0
    r15 = 0
    psr = r0
    rete = r0
    retx = r0
    rets = r0
    reti = r0

    r1 = r0;
    r4 = user_mode
    reti = r4
    rti

user_mode:
    ssp = _stack
    usp =  _ustack
    sp = usp 

    ; call pll_init

//bss_init
    r0 = 0x0
    r1 = _bss_begin
    r2 = _bss_size
    r2 >>= 2
    goto .L_bss_cmp
.L_bss_clr:
    [r1++] = r0
    r2 -= 1
.L_bss_cmp:
    if(r2 > 0) goto .L_bss_clr

//data_init
    r0 = _data_lvma
    r1 = _data_begin
    r2 = _data_size
    r2 >>= 2
    goto .L_data_cmp
.L_data_copy:
    r3 = [r0++];
    [r1++] = r3
    r2 -= 1
.L_data_cmp:
    if(r2 > 0) goto .L_data_copy

    goto c_main



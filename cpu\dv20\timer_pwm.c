
#include "typedef.h"
#include "hw_timer.h"
#include "jiffies.h"
#include "clock.h"
#include "irq.h"
#include "list.h"
#include "printf.h"
#include "app_msg.h"

/**
 * @param JL_TIMERx : JL_TIMER0/1/2/3
 * @param pwm_io : JL_PORTA_01, JL_PORTB_02,,,等等，支持任意普通IO
 * @param fre : 频率，单位Hz
 * @param duty : 初始占空比，0~10000对应0~100%
 */
void timer_pwm_init(JL_TIMER_TypeDef *JL_TIMERx, u32 pwm_io, u32 fre, u32 duty, u8 inv)
{
    volatile unsigned int *omap_port ;
    u8 tmr_num;
    const u32 TIMERx_table[4] = {
        (u32)JL_TIMER0,
        (u32)JL_TIMER1,
        (u32)JL_TIMER2,
        (u32)JL_TIMER3,
    };

    for (tmr_num = 0; tmr_num < 4; tmr_num ++) {
        if ((u32)JL_TIMERx == TIMERx_table[tmr_num]) {
            break;
        }
    }
    u32 timer_clk = 48000000;

    /* gpio_set_fun_output_port(pwm_io, FO_TMR0_PWM + tmr_num, 0, 1); */

    gpio_set_direction(pwm_io, 0);
    omap_port = gpio2crossbar_outreg(pwm_io);
    *omap_port = FO_GP_OCH7;// outputchannel7
    SFR(JL_IOMC->CON1, 7 * 4, 4, 8 + tmr_num);

    gpio_set_die(pwm_io, 1);
    gpio_set_pull_up(pwm_io, 0);
    gpio_set_pull_down(pwm_io, 0);
    //初始化timer
    JL_TIMERx->CON = 0;
    JL_TIMERx->CON |= (0b0100 << 10);				//时钟源选择pll_48m
    JL_TIMERx->CON |= (0b0001 << 4);				//时钟源再4分频
    JL_TIMERx->CNT = 0;								//清计数值
    JL_TIMERx->PRD = timer_clk / (4 * fre);			//设置周期
    //设置初始占空比
    JL_TIMERx->PWM = (JL_TIMERx->PRD * duty) / 10000;	//0~10000对应0~100%

    if (inv) {
        JL_TIMERx->CON |= BIT(9);
    }
    JL_TIMERx->CON |= BIT(8) | (0b01 << 0); 			//计数模式
}

/**
 * @param JL_TIMERx : JL_TIMER0/1/2/3/4/5
 * @param duty : 占空比，0~10000对应0~100%
 */
void set_timer_pwm_duty(JL_TIMER_TypeDef *JL_TIMERx, u32 duty)
{
    JL_TIMERx->PWM = (JL_TIMERx->PRD * duty) / 10000;	//0~10000对应0~100%
}


/********************************* 以下SDK的参考示例 ****************************/

void timer_pwm_test(void)
{
    printf("*********** timer pwm test *************\n");

    /* timer_pwm_init(JL_TIMER2, IO_PORTB_09, 1000, 5000, 1); //1KHz 50% */
    timer_pwm_init(JL_TIMER2, IO_PORTB_09, 100000, 5000, 0);//100KHz 75%
}


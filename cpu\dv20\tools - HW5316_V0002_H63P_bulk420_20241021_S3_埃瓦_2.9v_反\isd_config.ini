﻿#####################################################
#
#	配置数据按照 长度+配置名字+数据的方式存储
#
#####################################################

[EXTRA_CFG_PARAM]
#FLASH_SIZE=1M;
NEW_FLASH_FS=YES;
CHIP_NAME=AC531N;//8
ENTRY=0x1E00120;//程序入口地址
COMPACT_SETTING = YES;
PID=UVC;//长度16byte,示例：芯片封装_应用方向_方案名称
VID=0.01;	
SDK_VERSION=DV20_V1.0;//jenkins脚本生成

RESERVED_OPT=0;
NEED_RESERVED_4K=NO;

#DOWNLOAD_MODEL=SERIAL;
DOWNLOAD_MODEL=usb;
SERIAL_DEVICE_NAME=JlVirtualJtagSerial;
SERIAL_BARD_RATE=1000000;
SERIAL_CMD_OPT=11;
SERIAL_CMD_RATE=100; [n*10000]
SERIAL_CMD_RES=0;
SERIAL_INIT_BAUD_RATE=9600;
LOADER_BAUD_RATE=1000000;
LOADER_ASK_BAUD_RATE=1000000;
BEFORE_LOADER_WAIT_TIME=100;
SERIAL_SEND_KEY=yes;

#####################################################    匹配的芯片版本，请勿随意修改    ##################################################
[CHIP_VERSION]
SUPPORTED_LIST=B,C,D



#####################################################    UBOOT配置项，请勿随意调整顺序    ##################################################
[SYS_CFG_PARAM]
#data_width[0 1 2 3 4] 3的时候uboot自动识别2或者4线
#clk [0-255]
#mode:
#	  0 RD_OUTPUT,		 1 cmd 		 1 addr 
#	  1 RD_I/O,   		 1 cmd 		 x addr
#	  2 RD_I/O_CONTINUE] no_send_cmd x add
#port:
#	  0  优先选A端口  CS:PD3  CLK:PD0  D0:PD1  D1:PD2  D2:PB7  D3:PD5
#	  1  优先选B端口  CS:PA13 CLK:PD0  D0:PD1  D1:PA14 D2:PA15 D3:PD5
SPI=2_1_0_0;	#data_clk_mode_port;
#OSC=btosc;
#OSC_FREQ=12MHz; #[24MHz 12MHz]
SYS_CLK=120MHz;	#[64MHz 120MHz 192MHz]
#UTTX=PA01;//uboot串口tx
UTBD=1000000;//uboot串口波特率
#UTRX=PB01;串口升级[PB00 PB05 PA05]

#ID_写保护参数
FLASH_WP={\
5e3212_100c0 \
ef4016_10038 \
eb6012_1002c \
854411_c \
};


EX_EEPROM=AT24C016_01_PB07_PB08;//eeprom_type, iic_buad, iic_scl, iic_sda

#############################################################################################################################################

[FW_ADDITIONAL]
#FILE_LIST = (file=ota.bin:type=100:encrypt=FALSE),(file=script.ver),(file=efuse.bin:type=0xEE)
FILE_LIST = (file=script.ver),(file=efuse.bin:type=0xEE)

########flash空间使用配置区域###############################################
#PDCTNAME:    产品名，对应此代码，用于标识产品，升级时可以选择匹配产品名
#BOOT_FIRST:  1=代码更新后，提示APP是第一次启动；0=代码更新后，不提示
#UPVR_CTL：   0：不允许高版本升级低版本   1：允许高版本升级低版本
#XXXX_ADR:    区域起始地址	AUTO：由工具自动分配起始地址
#XXXX_LEN:    区域长度		CODE_LEN：代码长度
#XXXX_OPT:    区域操作属性
#
#
#
#操作符说明  OPT:
#	0:  下载代码时擦除指定区域
#	1:  下载代码时不操作指定区域
#	2:  下载代码时给指定区域加上保护
############################################################################
[RESERVED_CONFIG]
BTIF_ADR=AUTO;
BTIF_LEN=0x1000;
BTIF_OPT=1;


PRCT_ADR=0;
PRCT_LEN=CODE_LEN;
PRCT_OPT=2;

VM_ADR=0;
VM_LEN=8K;
VM_OPT=1;

########## 支持此固件的工具的最低版本，请勿修改 ##############
########## 支持此固件的工具的最低版本，请勿修改 ##############
[TOOL_CONFIG]
1TO2_MIN_VER  = 2.24.0
1TO8_MIN_VER  = 3.0.34
BTBOX_MIN_VER = 1.2.4.f


[BURNER_CONFIG]
SIZE=32;

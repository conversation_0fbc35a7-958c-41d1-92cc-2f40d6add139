#include "printf.h"
#include "uart.h"
#include "clock.h"
#include "gpio.h"
#include "delay.h"
#include "irq.h"
#include "wdt.h"


#define UART_RX_USE_DMA         1


#if UART_RX_USE_DMA
#define UART_RBUF_SIZE (32)
static u8 uart_rbuf[UART_RBUF_SIZE] __attribute__((aligned(32)));
static u32 uart_raddr = (u32)uart_rbuf;
#define UART1_RBUF_SIZE (32)
static u8 uart1_rbuf[UART1_RBUF_SIZE] __attribute__((aligned(32)));
static u32 uart1_raddr = (u32)uart_rbuf;
#endif

void (*uart_irq_handler_hook)(u8 *rbuf, u32 len) = NULL;
void (*uart1_irq_handler_hook)(u8 *rbuf, u32 len) = NULL;

void uart_set_rx_handler_hook(JL_UART_TypeDef *utx, void *uart_irq_hook)
{
    if (utx == JL_UART0) {
        uart_irq_handler_hook = uart_irq_hook;
    } else if (utx == JL_UART1) {
        uart1_irq_handler_hook = uart_irq_hook;
    }
}

static void _uart_irq_handler(JL_UART_TypeDef *utx)
{
    int uart_rlen = 1;
    u8 temp;
    u8 *rx_buf;
    u32 *rx_addr;
    u32 rx_len;
    void (*rx_hook)(u8 * rbuf, u32 len) = NULL;

    if (utx == JL_UART0) {
        rx_buf = uart_rbuf;
        rx_len = UART_RBUF_SIZE;
        rx_addr = &uart_raddr;
        rx_hook = uart_irq_handler_hook;
    } else if (utx == JL_UART1) {
        rx_buf = uart1_rbuf;
        rx_len = UART1_RBUF_SIZE;
        rx_addr = &uart1_raddr;
        rx_hook = uart1_irq_handler_hook;
    }

#if UART_RX_USE_DMA
    if ((utx->CON0 & BIT(14)) || (utx->CON0 & BIT(11))) {
        /* putchar('R'); */
        utx->CON0 |= BIT(7);
        utx->CON0 |= BIT(10);
        utx->CON0 |= BIT(12);
        __asm__ volatile("csync");
        __asm__ volatile("csync");
        uart_rlen = utx->HRXCNT;
        /* if(rlen >= 16){//处理数据 */
        /* }else{ */

#if 0
        if (*rx_addr + uart_rlen > tx_buf + rx_len) {
            u32 rlen = rx_buf + rx_len - *rx_addr;
            if (rx_hook) {
                rx_hook(*rx_addr, rlen);
            }
            uart_rlen -= rlen;
            *rx_addr = rx_buf;
        }

        if (rx_hook) {
            rx_hook(*rx_addr, uart_rlen);
        }
        *rx_addr += uart_rlen;
#else
        if (rx_hook) {
            rx_hook(rx_buf, uart_rlen);
        }
        utx->RXSADR  = (u32)rx_buf;
        utx->RXEADR  = (u32)(rx_buf + rx_len);
        utx->RXCNT = rx_len;
#endif
        /* }  */
    }
#else
    /* if ((utx->CON0 & BIT(14))) { */
    if ((utx->CON0 & BIT(14)) || (utx->CON0 & BIT(11))) {
        utx->CON0 |= BIT(7);
        utx->CON0 |= BIT(10);
        utx->CON0 |= BIT(12);
        __asm__ volatile("csync");
        __asm__ volatile("csync");
        temp = utx->BUF;
        if (rx_hook) {
            rx_hook(&temp, 1);
        }
    }
#endif
}

SET_INTERRUPT
static void uart_irq_handler()
{
    _uart_irq_handler(JL_UART0);
}
SET_INTERRUPT
static void uart1_irq_handler()
{
    _uart_irq_handler(JL_UART1);
}

void uart_tx_init(JL_UART_TypeDef *utx, const char *tx_pin, u32 baud, u8 ref_clk)
{
    volatile unsigned int *omap_port ;

    if ((baud) && (tx_pin && *tx_pin)) {
        u32 io = get_gpio(tx_pin);

        omap_port = gpio2crossbar_outreg(io);

        if (utx == JL_UART1) {
            *omap_port = FO_UART1_TX;
        } else {
            *omap_port = FO_UART0_TX;
        }

        gpio_set_direction(io, 0);
        gpio_set_die(io, 1);
        utx->CON0 = BIT(13) | BIT(12) | BIT(10);
        /* clk_out_to_io(IO_PORTA_02, 1, CLK_OUT1, RC_CLK); */
        SFR(JL_CLOCK->CLK_CON2, 2, 2, ref_clk);

        utx->CON0 &= ~BIT(0);
        utx->CON0 |= BIT(13) | BIT(12) | BIT(10);
        if (ref_clk) {
            //rc
            utx->BAUD = (clk_get("rc") / baud) / 4 - 1;
        } else {
            utx->BAUD = (clk_get("uart") / baud) / 4 - 1;
        }
        utx->CON0 |= BIT(13) | BIT(12) | BIT(10) | BIT(0);
    }
}

void uart_rx_init(JL_UART_TypeDef *utx, const char *rx_pin, u32 baud)
{
    u32 rx_io = get_gpio(rx_pin);
    u8 *rx_buf;
    u32 rx_len;
    u32 *rx_addr;

    if (rx_io < IO_PORT_MAX) {
        //crossbar
        gpio_set_direction(rx_io, 1);
        gpio_set_pull_up(rx_io, 1);
        gpio_set_pull_down(rx_io, 0);
        gpio_set_die(rx_io, 1);
        if (utx == JL_UART0) {
            JL_IMAP->FI_UART0_RX = gpio2crossbar_inport(rx_io);
            request_irq(IRQ_UART0_IDX, 0, uart_irq_handler, 0);
            rx_buf = uart_rbuf;
            rx_len = UART_RBUF_SIZE;
            rx_addr = &uart_raddr;
        } else if (utx == JL_UART1) {
            JL_IMAP->FI_UART1_RX = gpio2crossbar_inport(rx_io);
            request_irq(IRQ_UART1_IDX, 0, uart1_irq_handler, 0);
            rx_buf = uart1_rbuf;
            rx_len = UART1_RBUF_SIZE;
            rx_addr = &uart1_raddr;
        } else {
            while (1);
        }
        utx->BAUD = (clk_get("uart") / baud) / 4 - 1;
#if UART_RX_USE_DMA
        memset(rx_buf, 0, rx_len);
        *rx_addr = (u32)rx_buf;
        utx->RXSADR  = (u32)rx_buf;
        utx->RXEADR  = (u32)(rx_buf + rx_len);
        utx->RXCNT = rx_len;
        utx->OTCNT = clk_get("uart") / 1000;
        utx->CON0 |= BIT(5);   //OT中断
        utx->CON0 |= BIT(6);   //启动DMA
#else
        utx->CON0 &= ~BIT(6);   //不启动DMA
#endif
        utx->CON0 |= BIT(1);   //RB30 add RXEN
        utx->CON0 |= BIT(13) | BIT(12) | BIT(10) | BIT(3);
    }

}


void ut_tx(JL_UART_TypeDef *utx, int a)
{
    if (utx->CON0 & BIT(0)) {
        disable_int();
        utx->BUF = a;
        __asm__ volatile("csync");
        while ((utx->CON0 & BIT(15)) == 0);
        utx->CON0 |= BIT(13);
        enable_int();
        /* __asm__ volatile("trigger"); */
    }
}














#define     DEBUG_UART  JL_UART1
/* #define     DEBUG_UART  JL_UART0 */



#if UART_DEBUG
__attribute__((always_inline_when_const_args))
void putchar(int a)
{
    ut_tx(DEBUG_UART, a);
}
#endif

void debug_uart_init(const char *tx_pin, u32 baud, u8 ref_clk)
{
    uart_tx_init(DEBUG_UART, tx_pin, baud, ref_clk);
}

__attribute__((always_inline_when_const_args))
void debug_uart_close()
{
    volatile unsigned int *omap_port ;

    if (DEBUG_UART->CON0 & BIT(0)) {
        DEBUG_UART->CON0 = 0;
        DEBUG_UART->CON1 = 0;
        if (DEBUG_UART == JL_UART1) {
            *omap_port = FO_UART1_TX;
        } else {
            *omap_port = FO_UART0_TX;
        }
        *omap_port = 0;
    }
}

















#ifndef _I2CCOMMAPI_H
#define  _I2CCOMMAPI_H

#include "typedef.h"

typedef u8(*IICCommW)(void *iic, u16 regAddr, u8 regVal);
typedef u8(*IICCommR)(void *iic, u16 regAddr, u8 *regVal);

extern u32 IICWRCMD;
extern u32 IICRDCMD;
unsigned char IICCommWBB(void *iic, unsigned short regID, unsigned char regDat);   //byte+byte write
unsigned char IICCommRBB(void *iic, unsigned short regID, unsigned char *regDat);  //byte+byte read
unsigned char IICCommWWB(void *iic, unsigned short regID, unsigned char regDat);   //word+byte write
unsigned char IICCommRWB(void *iic, unsigned short regID, unsigned char *regDat);  //word+byte read

#endif

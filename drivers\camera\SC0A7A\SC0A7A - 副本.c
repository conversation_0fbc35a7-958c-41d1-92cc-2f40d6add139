
#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "SC0A7A.h"
#include "isp_alg.h"

static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *SC0A7A_mipi_get_ae_params();
extern void *SC0A7A_mipi_get_awb_params();
extern void *SC0A7A_mipi_get_iq_params();
extern void SC0A7A_mipi_ae_ev_init(u32 fps);


#define LINE_LENGTH_CLK     0x4e2//4500  //2250
#define FRAME_LENGTH      0x208//0x546// 720  //1440
#define PCLK  66

static u32 cur_again = -1;
static u32 cur_dgain = -1;
static u32 cur_expline = -1;
static u32 cur_line_length_clk = -1;

static u32 line_length_clk = LINE_LENGTH_CLK;
static u32 uframelen = 720; ////520; // PPK ADD ON 20221024
static void *iic = NULL;

#define WRCMD 0x60
#define RDCMD 0x61



typedef struct {
    u16 addr;
    u8 value;
} sensor_ini_regs_t;

sensor_ini_regs_t SC0A7A_mipi_ini_regs[] = {
////by frank  add 10bits mipi 1 lane mode
0x0100,0x00,
0x36e9,0x80,
0x37f9,0x80,
0x3018,0x1a,
0x301f,0x67,
0x30b8,0x44,
0x3200,0x00,
0x3201,0x00,
0x3202,0x01,
0x3203,0x2c,
0x3204,0x07,
0x3205,0x87,
0x3206,0x03,
0x3207,0x13,
0x3208,0x02,
0x3209,0x80,
0x320a,0x01,
0x320b,0xe0,

0x320c,0x09,//hts=2500
0x320d,0xc4,
0x320e,0x03,//20fps  vts=780
0x320f,0x0c,

0x3210,0x02,
0x3211,0x84,
0x3212,0x00,
0x3213,0x04,
//0x3250,0x40,
0x3253,0x0c,
0x3281,0x80,
0x3301,0x06,
0x3302,0x12,
0x3306,0x80,
0x3309,0x60,
0x330a,0x00,
0x330b,0xe0,
0x330d,0x20,
0x3314,0x15,
0x331e,0x41,
0x331f,0x51,
0x3320,0x0a,
0x3326,0x0e,
0x3333,0x10,
0x3334,0x40,
0x335d,0x60,
0x335e,0x06,
0x335f,0x08,
0x3364,0x56,
0x337a,0x06,
0x337b,0x0e,
0x337c,0x02,
0x337d,0x0a,
0x3390,0x03,
0x3391,0x0f,
0x3392,0x1f,
0x3393,0x06,
0x3394,0x06,
0x3395,0x06,
0x3396,0x48,
0x3397,0x4b,
0x3398,0x5f,
0x3399,0x06,
0x339a,0x06,
0x339b,0x92,
0x339c,0x92,
0x33a2,0x04,
0x33a3,0x0a,
0x33ad,0x1c,
0x33af,0x40,
0x33b1,0x80,
0x33b3,0x20,
0x349f,0x02,
0x34a6,0x48,
0x34a7,0x4b,
0x34a8,0x20,
0x34a9,0x20,
0x34f8,0x5f,
0x34f9,0x10,
0x3616,0xac,
0x3630,0xc0,
0x3631,0x86,
0x3632,0x26,
0x3633,0x32,
0x3637,0x29,
0x363a,0x84,
0x363b,0x04,
0x363c,0x08,
0x3641,0x3a,
0x364f,0x39,
0x3670,0xce,
0x3674,0xc0,
0x3675,0xc0,
0x3676,0xc0,
0x3677,0x86,
0x3678,0x8b,
0x3679,0x8c,
0x367c,0x4b,
0x367d,0x5f,
0x367e,0x4b,
0x367f,0x5f,
0x3690,0x62,
0x3691,0x63,
0x3692,0x63,
0x3699,0x86,
0x369a,0x92,
0x369b,0xa4,
0x369c,0x48,
0x369d,0x4b,
0x36a2,0x4b,
0x36a3,0x4f,
0x36ea,0x0d,
0x36eb,0x0c,
0x36ec,0x1c,
0x36ed,0x28,
0x370f,0x01,
0x3721,0x6c,
0x3722,0x09,
0x3724,0x41,
0x3725,0xc4,
0x37b0,0x09,
0x37b1,0x09,
0x37b2,0x09,
0x37b3,0x48,
0x37b4,0x5f,
0x37fa,0x0d,
0x37fb,0x35,
0x37fc,0x11,
0x37fd,0x37,
0x3900,0x19,
0x3901,0x02,
0x3905,0xb8,
0x391b,0x82,
0x391c,0x00,
0x391f,0x04,
0x3933,0x81,
0x3934,0x4c,
0x393f,0xff,
0x3940,0x73,
0x3942,0x01,
0x3943,0x4d,
0x3946,0x20,
0x3957,0x86,
0x3e01,0x40,
0x3e02,0x60,
0x3e28,0xc4,
0x440e,0x02,
0x4501,0xc0,
0x4509,0x14,
0x450d,0x11,
0x4518,0x00,
0x451b,0x0a,
//0x4800,0x44,
0x4819,0x05,
0x481b,0x03,
0x481d,0x0b,
0x481f,0x02,
0x4821,0x08,
0x4823,0x03,
0x4825,0x02,
0x4827,0x03,
0x4829,0x04,
0x501c,0x00,
0x501d,0x60,
0x501e,0x00,
0x501f,0x40,
0x5799,0x06,
0x5ae0,0xfe,
0x5ae1,0x40,
0x5ae2,0x38,
0x5ae3,0x30,
0x5ae4,0x28,
0x5ae5,0x38,
0x5ae6,0x30,
0x5ae7,0x28,
0x5ae8,0x3f,
0x5ae9,0x34,
0x5aea,0x2c,
0x5aeb,0x3f,
0x5aec,0x34,
0x5aed,0x2c,
0x5aee,0xfe,
0x5aef,0x40,
0x5af4,0x38,
0x5af5,0x30,
0x5af6,0x28,
0x5af7,0x38,
0x5af8,0x30,
0x5af9,0x28,
0x5afa,0x3f,
0x5afb,0x34,
0x5afc,0x2c,
0x5afd,0x3f,
0x5afe,0x34,
0x5aff,0x2c,
0x36e9,0x20,
0x37f9,0x20,
0x0100,0x01

};


unsigned char wr_SC0A7A_reg(u16 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID & 0xff)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }

__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    if (ret == 0)
        printf("wreg iic fail\n");
    return ret;

}

unsigned char rd_SC0A7A_reg(u16 regID, unsigned char *regDat)
{
    u8 ret = 1;
    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __rend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID & 0xff)) {
        ret = 0;
        goto __rend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(1000);

    dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat);

__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    if (ret == 0)
        printf("rreg iic fail\n");
    return ret;
}


/*************************************************************************************************
    sensor api
*************************************************************************************************/

void SC0A7A_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u8 v;


    SC0A7A_mipi_set_output_size(width, height, frame_freq);

    for (i = 0; i < sizeof(SC0A7A_mipi_ini_regs) / sizeof(sensor_ini_regs_t); i++) {
        wr_SC0A7A_reg(SC0A7A_mipi_ini_regs[i].addr, SC0A7A_mipi_ini_regs[i].value);
    }
    SC0A7A_mipi_ae_ev_init(*frame_freq);

    cur_again = -1;
    cur_dgain = -1;
    cur_expline = -1;
    cur_line_length_clk = -1;
    #if 0
    if (*frame_freq == 25) {
        wr_SC0A7A_reg(0x320e, 0x06);
        wr_SC0A7A_reg(0x320f, 0x60);
        uframelen = FRAME_LENGTH25;
    } else {
        uframelen = FRAME_LENGTH30;
    }
   // *format = SEN_IN_FORMAT_BGGR;
   #endif // 0

    return;
}


s32 SC0A7A_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 SC0A7A_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

s32 SC0A7A_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i ;

    for (i = 0; i < 3; i++) { //
        rd_SC0A7A_reg(0x3107, &pid);
        rd_SC0A7A_reg(0x3108, &ver);
    }

    printf("Sensor ID:%02x %02x\n", pid, ver);

    if (pid != 0xcb || ver != 0x1c) {
        puts("\n not SC0A7A-----\n");
        return -1;
    }
   //// puts("----hello SC0A7A_mipi-----\n");
    return 0;
}

void SC0A7A_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    /*printf("pwdn_gpio=%d\n", pwdn_gpio);*/
    gpio_direction_output(pwdn_gpio, 1);
    gpio_direction_output(reset_gpio, 1);
    delay(20000);
    gpio_direction_output(reset_gpio, 0);
    delay(20000);
    gpio_direction_output(reset_gpio, 1);
    //gpio_direction_output(pwdn_gpio, 0);
    delay(20000);
}


static u8 cur_sensor_type = 0xff;

s32 SC0A7A_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{

    puts("\n\n SC0A7A_mipi_check \n\n");
    if (!iic) {
        printf("isp_dev value: %d\n", isp_dev);
        if (isp_dev == ISP_DEV_0) {
            iic = dev_open("iic0", &_hw_iic);
            printf("iic value: %d\n", iic);
        }
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
    // printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    SC0A7A_mipi_reset(isp_dev);

    if (0 != SC0A7A_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

void resetStatic();
s32 SC0A7A_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
   //// puts("\n\n SC0A7D_mipi_init22 \n\n");

    SC0A7A_mipi_config_SENSOR(width, height, format, frame_freq);

    return 0;
}


static void set_again(u32 again)
{
    if (cur_again == again) {
        return;
    }
    cur_again  = again;
    wr_SC0A7A_reg(0x3e08, ((again >> 8) & 0xff));
    wr_SC0A7A_reg(0x3e09, again & 0xff);
    return;
}

static void set_dgain(u32 dgain, u32 gain)
{

    if (cur_dgain == dgain) {
        return;
    }
    cur_dgain  = dgain;
    wr_SC0A7A_reg(0x3e06, ((dgain >> 8) & 0xff));
    wr_SC0A7A_reg(0x3e07, ((dgain >> 0) & 0xff));
/*
//    if (gain < 2) {
//        wr_SC0A7A_reg(0x363c, 0x0e);
//    }else{
//         wr_SC0A7A_reg(0x363c, 0x07);
//    }

*/
/*
  u8 pid = 0x00;


    rd_SC0A7A_reg(0x3040, &pid);
    if(pid ==0x40){
       if (gain < 2) {
        wr_SC0A7A_reg(0x363c, 0x0e);
       }else{
        wr_SC0A7A_reg(0x363c, 0x07);
       }
    }else if(pid ==0x41)
    {
       if (gain < 2) {
        wr_SC0A7A_reg(0x363c, 0x0f);
       }else{
        wr_SC0A7A_reg(0x363c, 0x07);
       }
    }else{
      wr_SC0A7A_reg(0x363c, 0x07);
    }
*/
}

 // PPK ADD ON 20221024
static u32 convertagain2cfg(u32 val)
{
    u32 course = 3;
    u32 finegain;

    while (val >= 2048) {
        course *= 2;
        course++;
        val /= 2;
    }
    val -= 1024;
    finegain = 0x40 + val/16;
    return (course << 8) + finegain;
}

static u32 convertdgain2cfg(u32 val)
{
    u32 course = 0;
    u32 finegain;

    while (val >= 2048) {
        course *= 2;
        course ++;
        val /= 2;
    }
    val -= 1024;
    finegain = 0x80 + val / 8;
    return (course<< 8) + finegain;
}



#if 0
//q10
static void calc_gain(u32 gain, u8 *again, u16 *dgain)
{
    u32 ag;
    *again = 0;
    *dgain = gain * 0x10 / (1 << 10);
}
#else


#define MAX_AGAIN (15.875*1024)
#define MAX_DGAIN (31.75*1024)
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    int i;
    u32 reg0, reg1, reg2;
    u32 dgain, again;
    u32 again_h, again_l,dgain_h,dgain_l;

    if (gain < 1024) {
        gain = 1024;
    }

    if (gain > 16 * 1024) {
        gain = 16 * 1024;
    }

    if(gain>MAX_AGAIN){
      again=MAX_AGAIN;
      dgain = (gain*1024) / MAX_AGAIN;
    }else{
      again=gain;
      dgain=1024;
    }
if(again>=16*1024){
      again_h=0x2f;
      again_l=(again-(16*1024))*1000/125/1024+0x40;
   }
else if(again>=8*1024){
      again_h=0x1f;
	  again_l=(again-(8*1024))*1000/125/1024+0x40;
   }
  else if(again>=4*1024){
      again_h=0x0f;
      again_l=(again-(4*1024))*1000/62/1024+0x40;
   }
  else if(again>=2*1024){
      again_h=0x07;
      again_l=(again-(2*1024))*1000/31/1024+0x40;
   }else{
      again_h=0x03;
      again_l=(again-(1*1024))*1000/15/1024+0x40;
   }

  if(dgain>=16*1024){
      dgain_h=0x0f;
      dgain_l=(dgain-(16*1024))*1000/125/1024+0x80;
   }
  else if(dgain>=8*1024){
      dgain_h=0x07;
      dgain_l=(dgain-(8*1024))*1000/62/1024+0x80;
   }
  else if(dgain>=4*1024){
      dgain_h=0x03;
      dgain_l=(dgain-(4*1024))*1000/31/1024+0x80;
   }
   else if(dgain>=2*1024){
      dgain_h=0x01;
      dgain_l=(dgain-(2*1024))*1000/15/1024+0x80;
   }else{
      dgain_h=0x00;
      dgain_l=(dgain-(1*1024))*1000/7/1024+0x80;
   }


    *_again = convertagain2cfg(again);
    *_dgain = convertdgain2cfg(dgain);

   // printf("\n.....gain = %d ag = 0x%x; dg= 0x%x\n ",gain,*_again,*_dgain);
}
#endif

static void set_shutter(u32 texp)
{

    if (cur_expline == texp) {
        return;
    }
    cur_expline  = texp;

    wr_SC0A7A_reg(0x3e00, (texp >> 12) & 0xff);
    wr_SC0A7A_reg(0x3e01, (texp >> 4) & 0xff);
    wr_SC0A7A_reg(0x3e02, (texp << 4) & 0xf0);
}


u32 SC0A7A_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;
    u32 fLen;
    static bLow = 0;


    fLen = uframelen;

    texp = exp_time_us * PCLK * 2 / LINE_LENGTH_CLK;



    if (texp < 3) {
        texp = 3;
    }
    if (texp > 2*fLen  - 10) {
        texp = 2*fLen  - 10;
    }
    texp_align = (texp) * LINE_LENGTH_CLK / (PCLK * 2);

    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << 10) / texp_align;
        //printf("ratio = %d\n",ratio);
    } else {
        ratio = (1 << 10);
    }

    shutter->ae_exp_line =  texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;

   //// printf("exp_time_us=%d, texp=%d, gain=%d->%d\n", exp_time_us, texp, gain,shutter->ae_gain);
    return 0;
}

#define i2c_write wr_SC0A7A_reg
static void set_logic(u32 gain)
{
    return ;


}
u32 SC0A7A_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    #if 0
    u32 again, dgain;

    calc_gain((shutter->ae_gain), &again, &dgain);

    set_shutter(shutter->ae_exp_line);
    set_again(again);
    set_dgain(dgain, shutter->ae_gain);
   ////  set_logic(shutter->ae_gain);
   //// printf("xzpin sc0a7d---------------------------------------->\n");

    u8 pid = 0x00;
	//time logic new
	wr_SC0A7A_reg(0x3812, 0x00);//grp start
    rd_SC0A7A_reg(0x3040, &pid);
    if(pid == 0x40) {
       if (shutter->ae_gain < 2048) {
            wr_SC0A7A_reg(0x363c, 0x0e);
       } else {
            wr_SC0A7A_reg(0x363c, 0x07);
       }
    } else if (pid == 0x41) {
       if (shutter->ae_gain < 2048) {
            wr_SC0A7A_reg(0x363c, 0x0f);
       } else {
            wr_SC0A7A_reg(0x363c, 0x07);
       }
    } else {
        wr_SC0A7A_reg(0x363c, 0x07);
    }

	//dpc logic new
	if((shutter->ae_gain) >= 15.875*1024)
		{
			wr_SC0A7A_reg(0x5799, 0x07);
		}
	else if ((shutter->ae_gain) < 10*1024)
		{
			wr_SC0A7A_reg(0x5799, 0x00);
		}
	wr_SC0A7A_reg(0x3812, 0x30);//grp end

   //// printf("xzpin shutter->ae_gain = %d, shutter->ae_gain / 1024 = %d, again = %d, dgain = %d\n",
    ////        shutter->ae_gain, shutter->ae_gain / 1024, again, dgain);
    #endif
    return 0;
}
//

void SC0A7A_mipi_sleep()
{


}

void SC0A7A_mipi_wakeup()
{


}

void SC0A7A_mipi_wr_reg(u16 addr, u16 val)
{
    printf("update reg%x with %x\n", addr, val);
    wr_SC0A7A_reg((u16)addr, (u8)val);
}
u16 SC0A7A_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_SC0A7A_reg((u16)addr, &val);
    return val;
}

REGISTER_CAMERA(SC0A7A_mipi) = {
    .logo 				= 	"SC0A7A",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			= 	SEN_IN_FORMAT_BGGR,
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_CSI2,
    .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
    .fps         		= 	20,

    .sen_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},
    .isp_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H },

    .cap_fps         		= 	20,
    .sen_cap_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},
    .isp_cap_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},

    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	SC0A7A_mipi_check,
        .init 		        = 	SC0A7A_mipi_init,
        .set_size_fps 		=	SC0A7A_mipi_set_output_size,
        .power_ctrl         =   SC0A7A_mipi_power_ctl,

        .get_ae_params  	=	SC0A7A_mipi_get_ae_params,
        .get_awb_params 	=	SC0A7A_mipi_get_awb_params,
        .get_iq_params 	    =	SC0A7A_mipi_get_iq_params,

        .sleep 		        =	SC0A7A_mipi_sleep,
        .wakeup 		    =	SC0A7A_mipi_wakeup,
        .write_reg 		    =	SC0A7A_mipi_wr_reg,
        .read_reg 		    =	SC0A7A_mipi_rd_reg,

    }
};





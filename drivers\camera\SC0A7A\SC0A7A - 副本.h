
#ifndef __SC0A7A_MIPI_H__
#define __SC0A7A_MIPI_H__

#include "typedef.h"

#define SC0A7A_MIPI_OUTPUT_W    640
#define SC0A7A_MIPI_OUTPUT_H    480

#define SC0A7A_FPS_VARIABLE    0


s32 SC0A7A_mipi_set_output_size(u16 *width, u16 *height, u8 *freq);
s32 SC0A7A_mipi_power_ctl(u8 isp_dev, u8 is_work);

//s32 GC1004_check(u8 isp_dev);
s32 SC0A7A_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);


void SC0A7A_mipi_sleep();
void SC0A7A_mipi_wakeup();
void SC0A7A_mipi_W_Reg(u16 addr, u16 val);
u16 SC0A7A_mipi_R_Reg(u16 addr);


#endif






#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "SC0A7A.h"
#include "isp_alg.h"

static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *SC0A7A_mipi_get_ae_params();
extern void *SC0A7A_mipi_get_awb_params();
extern void *SC0A7A_mipi_get_iq_params();
extern void SC0A7A_mipi_ae_ev_init(u32 fps);


#define INPUT_CLK  24
#define PCLK  468/10

#define LINE_LENGTH_CLK       4000
#define FRAME_LENGTH30      520//VTS
#define FRAME_LENGTH25      624
#define FRAME_LENGTH27      578
#define ROW_TIME_NS           64103    //1*10^9 * LINE_LENGTH_CLK/ (PCLK*10^6)
//#define MAX_LONG_EXP_LINE 	  ((FRAME_LENGTH - 4 )*2)

static u32 cur_again = -1;
static u32 cur_dgain = -1;
static u32 cur_expline = -1;
static u32 cur_line_length_clk = -1;

static u32 line_length_clk = LINE_LENGTH_CLK;

static void *iic = NULL;
static u32 uframelen = FRAME_LENGTH25;
#define WRCMD 0x60
#define RDCMD 0x61



typedef struct {
    u16 addr;
    u8 value;
} sensor_ini_regs_t;

sensor_ini_regs_t SC0A7A_mipi_ini_regs[] = {
//cleaned_0x6f_SC0A7A_MIPI_24MInput_468Mbps_1lane_10bit_640x480_30fps
0x0103,0x01,
0x0100,0x00,
0x36e9,0x80,
0x36f9,0x80,
0x3018,0x12,
0x3019,0x0e,
0x301f,0x6f,
0x3200,0x02,
0x3201,0x80,
0x3202,0x01,
0x3203,0x2c,
0x3204,0x05,
0x3205,0x07,
0x3206,0x03,
0x3207,0x13,
0x3208,0x02,
0x3209,0x80,
0x320a,0x01,
0x320b,0xe0,
0x320c,0x05,//HTS=1500X2=3000
0x320d,0xdc,
0x320e,0x02,//30FPS VTS=520
0x320f,0x08,
0x3210,0x00,
0x3211,0x04,
0x3212,0x00,
0x3213,0x04,
0x3243,0x01,
0x3248,0x02,
0x3249,0x09,
0x3253,0x08,
0x3271,0x0a,
0x3301,0x20,
0x3304,0x40,
0x3306,0x32,
0x330b,0x88,
0x330f,0x02,
0x331e,0x39,
0x3333,0x10,
0x3621,0xe8,
0x3622,0x16,
0x3637,0x1b,
0x363a,0x1f,
0x363b,0xc6,
0x363c,0x0e,
0x3670,0x0a,
0x3674,0x82,
0x3675,0x76,
0x3676,0x78,
0x367c,0x48,
0x367d,0x58,
0x3690,0x34,
0x3691,0x33,
0x3692,0x44,
0x369c,0x40,
0x369d,0x48,
0x36ea,0x33,
0x36eb,0x1d,
0x36ec,0x0c,
0x36ed,0x04,
0x36fa,0xf3,
0x36fb,0x00,
0x36fc,0x11,
0x36fd,0x34,
0x3901,0x02,
0x3904,0x04,
0x3908,0x41,
0x391d,0x14,
0x391f,0x18,
0x3e00,0x00,
0x3e01,0x40,
0x3e02,0x80,
0x3e16,0x00,
0x3e17,0x80,
0x3f09,0x48,
0x4800,0x44,
0x4819,0x06,
0x481b,0x04,
0x481d,0x0d,
0x481f,0x03,
0x4821,0x09,
0x4823,0x03,
0x4825,0x03,
0x4827,0x03,
0x4829,0x05,
0x5787,0x10,
0x5788,0x06,
0x578a,0x10,
0x578b,0x06,
0x5790,0x10,
0x5791,0x10,
0x5792,0x00,
0x5793,0x10,
0x5794,0x10,
0x5795,0x00,
0x5799,0x00,
0x57c7,0x10,
0x57c8,0x06,
0x57ca,0x10,
0x57cb,0x06,
0x57d1,0x10,
0x57d4,0x10,
0x57d9,0x00,
0x59e0,0x60,
0x59e1,0x08,
0x59e2,0x3f,
0x59e3,0x18,
0x59e4,0x18,
0x59e5,0x3f,
0x59e6,0x06,
0x59e7,0x02,
0x59e8,0x38,
0x59e9,0x10,
0x59ea,0x0c,
0x59eb,0x10,
0x59ec,0x04,
0x59ed,0x02,
0x59ee,0xa0,
0x59ef,0x08,
0x59f4,0x18,
0x59f5,0x10,
0x59f6,0x0c,
0x59f7,0x10,
0x59f8,0x06,
0x59f9,0x02,
0x59fa,0x18,
0x59fb,0x10,
0x59fc,0x0c,
0x59fd,0x10,
0x59fe,0x04,
0x59ff,0x02,
0x36e9,0x27,
0x36f9,0x38,
0x320e,0x02,       ////FRAME_LENGTH25      1440
0x320f,0x70,
0x0100,0x01,

};


unsigned char wr_SC0A7A_reg(u16 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID & 0xff)) {
        ret = 0;
        goto __wend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }

__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    if (ret == 0)
        printf("wreg iic fail\n");
    return ret;

}

unsigned char rd_SC0A7A_reg(u16 regID, unsigned char *regDat)
{
    u8 ret = 1;
    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __rend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID & 0xff)) {
        ret = 0;
        goto __rend;
    }

    delay(100);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(1000);

    dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat);

__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    if (ret == 0)
        printf("rreg iic fail\n");
    return ret;
}


/*************************************************************************************************
    sensor api
*************************************************************************************************/

void SC0A7A_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u8 v;

    u8 frame = 25;
    SC0A7A_mipi_set_output_size(width, height, &frame/*frame_freq*/);

    for (i = 0; i < sizeof(SC0A7A_mipi_ini_regs) / sizeof(sensor_ini_regs_t); i++) {
        wr_SC0A7A_reg(SC0A7A_mipi_ini_regs[i].addr, SC0A7A_mipi_ini_regs[i].value);
    }
    SC0A7A_mipi_ae_ev_init(frame/*frame_freq*/);

    cur_again = -1;
    cur_dgain = -1;
    cur_expline = -1;
    cur_line_length_clk = -1;

 	if (*frame_freq == 25)
    {
        wr_SC0A7A_reg(0x320e,0x02);//       FRAME_LENGTH25      1440
        wr_SC0A7A_reg(0x320f,0x70);//
        uframelen = FRAME_LENGTH25;
    }
    else if (*frame_freq == 27)
    {
		wr_SC0A7A_reg(0x320e,0x02);
		wr_SC0A7A_reg(0x320f,0x42);//		FRAME_LENGTH27      1334
		uframelen = FRAME_LENGTH27;
    }
    else
        uframelen = FRAME_LENGTH25;


       *format = SEN_IN_FORMAT_BGGR;

    return;
}


s32 SC0A7A_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 SC0A7A_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

s32 SC0A7A_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i ;

    for (i = 0; i < 3; i++) { //
        rd_SC0A7A_reg(0x3107, &pid);
        rd_SC0A7A_reg(0x3108, &ver);
    }

    printf("Sensor ID:%02x %02x\n", pid, ver);

    if (pid != 0xcb || ver != 0x1c) {
        puts("\n not SC0A7A-----\n");
        return -1;
    }
   //// puts("----hello SC0A7A_mipi-----\n");
    return 0;
}

void SC0A7A_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    /*printf("pwdn_gpio=%d\n", pwdn_gpio);*/
    gpio_direction_output(pwdn_gpio, 1);
    gpio_direction_output(reset_gpio, 1);
    delay(20000);
    gpio_direction_output(reset_gpio, 0);
    delay(20000);
    gpio_direction_output(reset_gpio, 1);
    //gpio_direction_output(pwdn_gpio, 0);
    delay(20000);
}


static u8 cur_sensor_type = 0xff;

s32 SC0A7A_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{

    puts("\n\n SC0A7A_mipi_check \n\n");
    if (!iic) {
        printf("isp_dev value: %d\n", isp_dev);
        if (isp_dev == ISP_DEV_0) {
            iic = dev_open("iic0", &_hw_iic);
            printf("iic value: %d\n", iic);
        }
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
    // printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    SC0A7A_mipi_reset(isp_dev);

    if (0 != SC0A7A_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

void resetStatic();
s32 SC0A7A_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
   //// puts("\n\n SC0A7D_mipi_init22 \n\n");

    SC0A7A_mipi_config_SENSOR(width, height, format, frame_freq);

    return 0;
}


static void set_again(u32 again)
{
    if (cur_again == again) {
        return;
    }
    cur_again  = again;
    wr_SC0A7A_reg(0x3e08, ((again >> 8) & 0xff));
    wr_SC0A7A_reg(0x3e09, again & 0xff);
    return;
}

static void set_dgain(u32 dgain, u32 gain)
{

    if (cur_dgain == dgain) {
        //return;  //////
    }
    cur_dgain  = dgain;
    wr_SC0A7A_reg(0x3e06, ((dgain >> 8) & 0xff));
    wr_SC0A7A_reg(0x3e07, ((dgain >> 0) & 0xff));
/*
//    if (gain < 2) {
//        wr_SC0A7A_reg(0x363c, 0x0e);
//    }else{
//         wr_SC0A7A_reg(0x363c, 0x07);
//    }

*/
/*
  u8 pid = 0x00;


    rd_SC0A7A_reg(0x3040, &pid);
    if(pid ==0x40){
       if (gain < 2) {
        wr_SC0A7A_reg(0x363c, 0x0e);
       }else{
        wr_SC0A7A_reg(0x363c, 0x07);
       }
    }else if(pid ==0x41)
    {
       if (gain < 2) {
        wr_SC0A7A_reg(0x363c, 0x0f);
       }else{
        wr_SC0A7A_reg(0x363c, 0x07);
       }
    }else{
      wr_SC0A7A_reg(0x363c, 0x07);
    }
*/
}

 // PPK ADD ON 20221024
static u32 convertagain2cfg(u32 val)
{
    u32 anagain = 3;
    u32 finegain;

    while (val >= 2048) {

        anagain*=2;
        anagain++;
        val /= 2;
    }
    val -= 1024;
    finegain = 0x40 + val/16;
    return (anagain<< 8) + finegain;
}

static u32 convertdgain2cfg(u32 val)
{
    u32 diggain = 0;
    u32 finegain;

    while(val >= 2048)
    {
        diggain*=2;
        diggain++;
        val /= 2;
    }
    val -= 1024;
    finegain = 0x80 + val / 8;
    return (diggain<< 8) + finegain;
}



#if 0
//q10
static void calc_gain(u32 gain, u8 *again, u16 *dgain)
{
    u32 ag;
    *again = 0;
    *dgain = gain * 0x10 / (1 << 10);
}
#else


#define MAX_AGAIN (53*1024+512) //15.875*3.4=53.975
#define MAX_DGAIN (31*1024)
#define MIN_AGAIN (1024)
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    int i;
    u32 reg0, reg1, reg2;
    u32 dgain, again;
	u32 dgain_cal, again_cal;
	u32 gain_0x3e08,gain_0x3e06;

    if (gain < 1024) {
        gain = 1024;
    }

    if(gain > 180*1024) gain = 180*1024;


    if (gain>MAX_AGAIN)
    {
        again = MAX_AGAIN;
        dgain = gain * 1024 /MAX_AGAIN;
        if (gain > MAX_DGAIN)
            gain = MAX_DGAIN;
    }
    else
    {
        again = gain;
        dgain = 1024;
    }
    const u8 a3e08[] = {0x03,0x07,0x23,0x27,0x2f,0x3f};
    gain_0x3e08 = 0x00;
    while(again >= 2048)
    {
        again/=2;
        gain_0x3e08++;
    }
    again_cal = again/16;
    *_again = (a3e08[gain_0x3e08]<<8) + again_cal;

    gain_0x3e06 = 0x00;
    while(dgain >= 2048)
    {
        dgain/=2;
        gain_0x3e06*=2;
        gain_0x3e06++;
    }
    dgain_cal = dgain>>=3;
    *_dgain = (gain_0x3e06<<8) +dgain_cal;
}
#endif

static void set_shutter(u32 texp)
{

    if (cur_expline == texp) {
        return;
    }
    cur_expline  = texp;

    wr_SC0A7A_reg(0x3e00, (texp >> 12) & 0xff);
    wr_SC0A7A_reg(0x3e01, (texp >> 4) & 0xff);
    wr_SC0A7A_reg(0x3e02, (texp << 4) & 0xf0);
}


u32 SC0A7A_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;
    u32 fLen;
    static u8 bLow = 0;


    fLen = uframelen;

    texp = exp_time_us * PCLK * 2 / LINE_LENGTH_CLK;

    if (texp < 4)
    {
        texp = 4;
    }
    if (texp > fLen * 2 - 8)
    {
        texp = fLen * 2 - 8;
    }
    texp_align = (texp) * LINE_LENGTH_CLK / (PCLK * 2);

    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << 10) / texp_align;
        //printf("ratio = %d\n",ratio);
    } else {
        ratio = (1 << 10);
    }

    shutter->ae_exp_line =  texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;

   //// printf("exp_time_us=%d, texp=%d, gain=%d->%d\n", exp_time_us, texp, gain,shutter->ae_gain);
    return 0;
}

static void set_logic(u32 gain)
{


}
u32 SC0A7A_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    static int framecount=0;
    u32 again, dgain;
    u8 r1,r2,r3,v;
    u16 r;
	static u8 upflag = 0xff;

    calc_gain((shutter->ae_gain), &again, &dgain);
    set_shutter(shutter->ae_exp_line);
    set_again(again);
    set_dgain(dgain,shutter->ae_gain);

    return 0;
}
//

void SC0A7A_mipi_sleep()
{


}

void SC0A7A_mipi_wakeup()
{


}

void SC0A7A_mipi_wr_reg(u16 addr, u16 val)
{
    printf("update reg%x with %x\n", addr, val);
    wr_SC0A7A_reg((u16)addr, (u8)val);
}
u16 SC0A7A_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_SC0A7A_reg((u16)addr, &val);
    return val;
}

REGISTER_CAMERA(SC0A7A_mipi) = {
    .logo 				= 	"SC0A7A",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			= 	SEN_IN_FORMAT_BGGR,
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_CSI2,
    .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
    .fps         		= 	20,

    .sen_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},
    .isp_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H },

    .cap_fps         		= 	20,
    .sen_cap_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},
    .isp_cap_size 			= 	{SC0A7A_MIPI_OUTPUT_W, SC0A7A_MIPI_OUTPUT_H},

    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	SC0A7A_mipi_check,
        .init 		        = 	SC0A7A_mipi_init,
        .set_size_fps 		=	SC0A7A_mipi_set_output_size,
        .power_ctrl         =   SC0A7A_mipi_power_ctl,

        .get_ae_params  	=	SC0A7A_mipi_get_ae_params,
        .get_awb_params 	=	SC0A7A_mipi_get_awb_params,
        .get_iq_params 	    =	SC0A7A_mipi_get_iq_params,

        .sleep 		        =	SC0A7A_mipi_sleep,
        .wakeup 		    =	SC0A7A_mipi_wakeup,
        .write_reg 		    =	SC0A7A_mipi_wr_reg,
        .read_reg 		    =	SC0A7A_mipi_rd_reg,

    }
};





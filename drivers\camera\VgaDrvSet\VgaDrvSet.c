﻿

/*******************************************************************************************
 File Name: VgaDrvSet.c

 Version: 1.00

*******************************************************************************************/
#include "app_config.h"
#include "iic.h"
#include "isp_dev.h"
#include "Vgadrvset.h"
#include "gpio.h"
#include "delay.h"

#define VgaDrvSet_DEVP_INPUT_W 640
#define VgaDrvSet_DEVP_INPUT_H	480
#define VgaDrvSet_DEVP_OUPUT_W 640
#define VgaDrvSet_DEVP_OUPUT_H	480

static void *iic = NULL;
static int VgaDrvSet_reset_io[2] = {-1, -1};
static int VgaDrvSet_power_io[2] = {-1, -1};

static u32 sensorIndex = 0;
static IICCommW wrVgaDrvSetReg = IICCommWBB;
static IICCommR rdVgaDrvSetReg = IICCommRBB;

void VgaDrvSet_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    int ret;

    if (CommSensorList[sensorIndex].IICType) {
        u16 addr, val;
        u32 regIndex = 0;
        do {
            addr = CommSensorList[sensorIndex].sensorinitList[regIndex];
            addr <<= 8;
            addr += CommSensorList[sensorIndex].sensorinitList[regIndex + 1];
            val = CommSensorList[sensorIndex].sensorinitList[regIndex + 2];
            if ((addr == 0xffff) && (val == 0xff)) {
                break;
            } else {
                wrVgaDrvSetReg(iic, addr, val);
                printf("%x %x\n", addr, val);
                regIndex += 3;
            }
        } while (1);
    } else {
        u8 addr, val;
        u32 regIndex = 0;
        do {
            addr = CommSensorList[sensorIndex].sensorinitList[regIndex];
            val = CommSensorList[sensorIndex].sensorinitList[regIndex + 1];
            if ((addr == 0xff) && (val == 0xff)) {
                break;
            } else {
                wrVgaDrvSetReg(iic, addr, val);
                printf("%x %x\n", addr, val);
                regIndex += 2;
            }
        } while (1);
    }
    *format = SEN_IN_FORMAT_UYVY;
}

static s32 VgaDrvSet_set_output_size(u16 *width, u16 *height, u8 *freq)
{
    printf(">>>>> VgaDrvSet_set_output_size [%d] freq_fps=%d\n", sensorIndex, *freq);
    //TODO
    return 0;
}

static s32 VgaDrvSet_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

static s32 VgaDrvSet_ID_check(void)
{
    u8 pid = 0x00;
    u8 destpid = 0xff;

    destpid = CommSensorList[sensorIndex].SensorID;
    rdVgaDrvSetReg(iic, CommSensorList[sensorIndex].IDAddr, &pid);
    for (u32 i = 0; i < 3; i++) {
        printf("VgaDrvSet %d Sensor ID : 0x%x dest:0x%x\n", sensorIndex, pid, destpid);
        if (pid == destpid) {
            break;
        }
        delay(40000);
        rdVgaDrvSetReg(iic, CommSensorList[sensorIndex].IDAddr, &pid);
    }

    if (pid != destpid) {
        return -1;
    }

    return 0;
}


static void VgaDrvSet_reset(u8 isp_dev)
{
    u32 res_io;
    u32 powd_io;
    int rstdef = 0, rstnml = 1, pwdndef = 0, pwdnnml = 1;
    const u32 delayNum[3] = {2, 4, 6};
    int rstdelayIndex = 1, pwdndelayIndex = 1;
    u8 id = 0;

    puts("VgaDrvSet reset\n");

    //sensor 的rst pwdn 电平

    if (CommSensorList[sensorIndex].rstStatus == 0x00) {
        rstdef = 1;
        rstnml = 0;
    }
    if (CommSensorList[sensorIndex].pwdnStatus == 0x00) {
        pwdndef = 1;
        pwdnnml = 0;
    }

    //rstdelayIndex =1，pwdndelayIndex =1; 从efuse中读取pwdn rst 等待稳定延时

    IICWRCMD = CommSensorList[sensorIndex].IICWRCMD;
    IICRDCMD = CommSensorList[sensorIndex].IICRDCMD;

    if (CommSensorList[sensorIndex].IICType) {
        wrVgaDrvSetReg = IICCommWWB;
        rdVgaDrvSetReg = IICCommRWB;
    } else {
        wrVgaDrvSetReg = IICCommWBB;
        rdVgaDrvSetReg = IICCommRBB;
    }

    //efuse中读取set iic baud:[4] IICBaud: 0~3对应100K~400K


    if (isp_dev == ISP_DEV_0) {
        res_io = VgaDrvSet_reset_io[0];
        powd_io = VgaDrvSet_power_io[0];
    } else {
        res_io = VgaDrvSet_reset_io[1];
        powd_io = VgaDrvSet_power_io[1];
    }
    printf("pwdn:%x,rst:%x\n", powd_io, res_io);
    gpio_direction_output(powd_io, pwdndef);
    mdelay(4);
    gpio_direction_output(res_io, rstdef);
    mdelay(4);
    gpio_direction_output(res_io, rstnml);
    delay(4);
    gpio_direction_output(powd_io, pwdnnml);
    mdelay(4);
}

static s32 VgaDrvSet_check(u8 isp_dev, u32 _reset_gpio, u32 _power_gpio)
{
    if (!iic) {
        if (isp_dev == ISP_DEV_1) {
            iic = dev_open("iic0", &_hw_iic);
            /* iic = dev_open("swiic0", &_sw_iic); */
        }
        VgaDrvSet_reset_io[isp_dev] = _reset_gpio;
        VgaDrvSet_power_io[isp_dev] = _power_gpio;
    }
    if (iic == NULL) {
        printf("VgaDrvSet iic open err!!!\n\n");
        return -1;
    }

    sensorIndex = 0; //默认从0开始，若从efuse中读取有指定编号，则直接从该编号开始

    //从efuse中读取sensorIndex
    for (; sensorIndex < sensorListLen; sensorIndex++) {
        VgaDrvSet_reset(isp_dev);

        if (0 != VgaDrvSet_ID_check()) {
            printf("-------not VgaDrvSet------\n\n");
        } else {
            break;
        }
    }
    if (sensorIndex == sensorListLen) {
        return -1;
    }
    printf("-------hello VgaDrvSet------\n\n");
    return 0;
}

static s32 VgaDrvSet_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\n VgaDrvSet_init \n\n");

    /* if (0 != VgaDrvSet_check(isp_dev, 0, 0)) { */
    /* return -1; */
    /* } */
    VgaDrvSet_config_SENSOR(width, height, format, frame_freq);

    return 0;
}

void VgaDrvSet_dvp_wr_reg(u16 addr, u16 val)
{
    printf("update reg%x with %x\n", addr, val);
    wrVgaDrvSetReg(iic, addr, (u8)val);
}
u16 VgaDrvSet_dvp_rd_reg(u16 addr)
{
    u8 val;
    rdVgaDrvSetReg(iic, addr, &val);
    return val;
}
REGISTER_CAMERA(VgaDrvSet) = {
    .logo 				= 	"VgaDrv",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			= 	SEN_IN_FORMAT_UYVY,
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_PARALLEL,
    .mbus_config        =   SEN_MBUS_DATA_WIDTH_8B  | SEN_MBUS_HSYNC_ACTIVE_HIGH | SEN_MBUS_PCLK_SAMPLE_FALLING,
    .fps         		= 	25,
    .sen_size 			= 	{VgaDrvSet_DEVP_INPUT_W, VgaDrvSet_DEVP_INPUT_H},
    .isp_size 			= 	{VgaDrvSet_DEVP_OUPUT_W, VgaDrvSet_DEVP_OUPUT_H},
    .cap_fps         	= 	30,
    .sen_cap_size 		= 	{VgaDrvSet_DEVP_INPUT_W, VgaDrvSet_DEVP_INPUT_H},
    .isp_cap_size 		= 	{VgaDrvSet_DEVP_OUPUT_W, VgaDrvSet_DEVP_OUPUT_H},


    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	VgaDrvSet_check,
        .init 		        = 	VgaDrvSet_init,
        .set_size_fps 		=	VgaDrvSet_set_output_size,
        .power_ctrl         =   VgaDrvSet_power_ctl,


        .sleep 		        =	NULL,
        .wakeup 		    =	NULL,
        .write_reg 		    =	VgaDrvSet_dvp_wr_reg,
        .read_reg 		    =	VgaDrvSet_dvp_rd_reg,
    }
};


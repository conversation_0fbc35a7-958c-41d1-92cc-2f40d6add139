
#ifndef _VgaDRVSET_H
#define  _VgaDRVSET_H

#include "../IICCommApi/I2cCommApi.h"

typedef struct {
    u8 pwdnStatus;
    u8 rstStatus;
    u8 IICWRCMD;
    u8 IICRDCMD;
    u8 IICType;//0:B+B 1:W+B
    u16 IDAddr;
    u8  SensorID;
    const u8 *sensorinitList;
} comm_sensor_ini_t;


#define sensorListLen 7
const u8 SIV100Binit[] = {
    0x04, 0x00,
    0x11, 0x04,
    0x12, 0x0A,
    0x13, 0x1F,
    0x16, 0x89,
    0x1B, 0x90,
    0x1F, 0x52,
    0x20, 0x00,
    0x21, 0x00,
    0x22, 0x00,
    0x23, 0x00,
    0x24, 0x00,
    0x25, 0x00,
    0x34, 0x7D,
    0x37, 0x7D,
    0x33, 0x0A,
    0x42, 0x7F,
    0x43, 0xC0,
//----------AE Control-----------
    0x44, 0x58,	 //0x58
    0x45, 0x28,	 //0x28
    0x46, 0x08,
    0x47, 0x15,
    0x48, 0x0A,
    0x49, 0x08,
    0x4A, 0x43,
//-------------------------------
    0x4B, 0x82,
    0x4C, 0x3C,
    0x4E, 0x17,
    0x4F, 0x94,
    0x50, 0x8A,
    0x5A, 0x00,
    0x60, 0xC8,
    0x61, 0x88,
    0x62, 0x01,
//-----------------HUE Red Control-----
    0x63, 0x7c, //0x80, //0x80, //0x7c,  //0x80,
    0x64, 0x80,
    0x65, 0xD2,
    0x66, 0x83,
    0x67, 0xD6,
    0x68, 0x80,
    0x69, 0x8A,
    0x6A, 0x70,
    0x6B, 0x90,
    0x6C, 0x70,
    0x6D, 0x88,
    0x6E, 0x77,
    0x6F, 0x46,
    0x70, 0xD8,
    0x71, 0x40,
    0x72, 0x05,
    0x73, 0x02,
    0x74, 0x0C,
    0x75, 0x04,
    0x76, 0x20,
    0x77, 0xB7,
    0x78, 0x95,
    0x80, 0xAf,
    0x81, 0x1D,
    0x82, 0x3D,
    0x83, 0x00,
    0x86, 0xA1,
    0x87, 0x04,
    0x88, 0x26,
    0x89, 0x0F,
    0x92, 0x44,
    0x93, 0x20,
    0x94, 0x20,
    0x95, 0x40,
    0x96, 0x18,
    0x97, 0x20,
    0x98, 0x20,
    0x99, 0x24,
    0x9A, 0x50,
    0xA4, 0xAA,
    0xA5, 0xAA,
    0xA6, 0xAD,
    0xA7, 0xDA,
    0xA8, 0xCC,
    0xA9, 0x00,
    0xAA, 0x00,
    0xAB, 0x22,
    0xAC, 0x11,
    0xAD, 0x00,
    0xAE, 0x00,
    0xAF, 0xA0,

    0xB0, 0x88,

//Gamma 1
    0xB1, 0x00,
    0xB2, 0x08,
    0xB3, 0x11,
    0xB4, 0x25,
    0xB5, 0x45,
    0xB6, 0x5F,
    0xB7, 0x74,
    0xB8, 0x87,
    0xB9, 0x97,
    0xBA, 0xA5,
    0xBB, 0xB2,
    0xBC, 0xC9,
    0xBD, 0xDD,
    0xBE, 0xF0,
    0xBF, 0xF8,
    0xC0, 0xFF,



    0xC1, 0x3A,	   //CC
    0xC2, 0xC6,
    0xC3, 0xFE,
    0xC4, 0x10,
    0xC5, 0x21,
    0xC6, 0x10,
    0xC7, 0xF3,
    0xC8, 0xBD,
    0xC9, 0x50,
    0xCA, 0x90,
    0xCB, 0x0A,
    0xCC, 0x10,
    0xCD, 0x06,
    0xCE, 0x06,
    0xCF, 0x20,
    0xD0, 0x20,
    0xD1, 0x26,
    0xD2, 0x86,
    0xD3, 0x00,
    0xD4, 0x10,
    0xD5, 0x14,
    0xD6, 0x14,
    0xD7, 0x00,
    0xD8, 0x00,
    0xD9, 0x00,
    0xDA, 0x00,
    0xDB, 0xFF,
    0xDC, 0x00,
    0xE1, 0x2A,
    0xE2, 0x6B,
    0xF0, 0x24,
    0xF2, 0x80,
    0xF4, 0xE0,
    0x40, 0x85,
    0xD7, 0x0E,
    0x03, 0x05,
//0x40, 0x85,
    0x40, 0x85,
    0x40, 0x8f,	  //0x86
    0x33, 0x0e,  //0x0a,   //0x0e,  Frame control;
    0xCB, 0x10,
    0x60, 0xC8,
    0x04, 0x00,


    0x40, 0xa6,
    0x41, 0x84,
    0x42, 0x7F,
    0x43, 0xC0,
    0x44, 0x58,
    0x45, 0x28,
    0x46, 0x08,
    0x47, 0x15,
    0x48, 0x0a,  //0x1E,
    0x49, 0x13,
    0x4A, 0x63,
    0x4B, 0x82,
    0x4C, 0x3C,
    0x4E, 0x17,
    0x4F, 0x8A,
    0x50, 0x94,

    0x40, 0x86,
    0x03, 0xC5,

    0x40, 0x85,

    0x04, 0x00,
    0x11, 0x04,
    0x12, 0x0A,
    0x13, 0x1F,
    0x16, 0x89,
    0x1B, 0x90,
    0x1F, 0x52,
    0x20, 0x00,
    0x21, 0x00,
    0x22, 0x78,
    0x23, 0x00,
    0x24, 0x00,
    0x25, 0x60,
    0x34, 0x7D,
    0x37, 0x7D,
    0x33, 0x04,//0x0A,

    0x40, 0x85,

    0x41, 0x80,
    0x42, 0x7F,
    0x43, 0xC0,
    0x44, 0x58,
    0x45, 0x28,
    0x46, 0x08,
    0x47, 0x15,
    0x48, 0x0A,
    0x49, 0x08,
    0x4A, 0x43,
    0x4B, 0x82,
    0x4C, 0x3C,
    0x4E, 0x17,
    0x4F, 0x94,
    0x50, 0x8A,
    0x5A, 0x00,
    0x60, 0xC8,
    0x61, 0x88,
    0x62, 0x01,
    0x63, 0x80,
    0x64, 0x80,
    0x65, 0xD2,
    0x66, 0x83,
    0x67, 0xD6,
    0x68, 0x80,
    0x69, 0x8A,
    0x6A, 0x70,
    0x6B, 0x90,
    0x6C, 0x70,
    0x6D, 0x88,
    0x6E, 0x77,
    0x6F, 0x46,
    0x70, 0xD8,
    0x71, 0x40,
    0x72, 0x05,
    0x73, 0x02,
    0x74, 0x0C,
    0x75, 0x04,
    0x76, 0x20,
    0x77, 0xB7,
    0x78, 0x95,
    0x80, 0xAF,
    0x81, 0x1D,
    0x82, 0x1D,
    0x83, 0x00,
    0x86, 0xA1,
    0x87, 0x04,
    0x88, 0x26,
    0x89, 0x0F,
    0x92, 0x44,
    0x93, 0x20,
    0x94, 0x20,
    0x95, 0x40,
    0x96, 0x18,
    0x97, 0x20,
    0x98, 0x20,
    0x99, 0x24,
    0x9A, 0x50,
    0xA4, 0xAA,
    0xA5, 0xAA,
    0xA6, 0xAD,
    0xA7, 0xDA,
    0xA8, 0xCC,
    0xA9, 0x00,
    0xAA, 0x00,
    0xAB, 0x22,
    0xAC, 0x11,
    0xAD, 0x00,
    0xAE, 0x00,
    0xAF, 0xA0,
    0xB0, 0x88,
    0xB1, 0x00,
    0xB2, 0x08,
    0xB3, 0x11,
    0xB4, 0x25,
    0xB5, 0x45,
    0xB6, 0x5F,
    0xB7, 0x74,
    0xB8, 0x87,
    0xB9, 0x97,
    0xBA, 0xA5,
    0xBB, 0xB2,
    0xBC, 0xC9,
    0xBD, 0xDD,
    0xBE, 0xF0,
    0xBF, 0xF8,
    0xC0, 0xFF,
    0xC1, 0x3A,
    0xC2, 0xC6,
    0xC3, 0xFE,
    0xC4, 0x10,
    0xC5, 0x21,
    0xC6, 0x10,
    0xC7, 0xF3,
    0xC8, 0xBD,
    0xC9, 0x50,
    0xCA, 0x90,
    0xCB, 0x0A,
    0xCC, 0x10,
    0xCD, 0x06,
    0xCE, 0x06,
    0xCF, 0x20,
    0xD0, 0x20,
    0xD1, 0x26,
    0xD2, 0x86,
    0xD3, 0x00,
    0xD4, 0x10,
    0xD5, 0x14,
    0xD6, 0x14,
    0xD7, 0x00,
    0xD8, 0x00,
    0xD9, 0x00,
    0xDA, 0x00,
    0xDB, 0xFF,
    0xDC, 0x00,
    0xE1, 0x2A,
    0xE2, 0x6B,
    0xF0, 0x24,
    0xF2, 0x80,
    0xF4, 0xE0,
//0x40, 0x86,
    0x40, 0x85,
    0xD7, 0x0E,
    0x03, 0x05,
    0x40, 0x86,
    0x33, 0x0a,  //0x0A,
    0xCB, 0x10,
    0x60, 0xC8,
    0x04, 0x00,
    0xCB, 0x50,
    0xCC, 0x58,
    0x40, 0x86,


//0xd5, 0x18,	//saturation
//0xd6, 0x12,	//satination
//0xca, 0x90,
//0xcb, 0x10,
//0xcc, 0x10, //sharpness
//0x63, 0x98, //hue red control
//0x64, 0x78,	//hue blue control



///////////////XH/////////////////

//0x63, 0xa8,
    0x04, 0x00,  //0x03,  //Mirr Filp
    0x33, 0x04,  //0x0d The limit frame
    0x65, 0xe8,
    0x66, 0xc8,

//0x80, 0xbf ,//0xa7,  //BlC control
//0x93, 0x48,
//0x96, 0x48,
//0x65, 0xe8,
//0x66, 0xc8,
//0x69, 0xee,	//cr
//0x6a, 0xde,	//cb
    0x80, 0xaf,
    0xa9, 0x55, //0x25, //0x55,
    0xaa, 0x54, //0x52, //0x54,
    0xab, 0x22,
    0xac, 0x22,
    0xad, 0x22,
    0xae, 0x22,
//0xc1, 0x3d,  //ccm
//0xc9, 0x56,  //ccm
//0xc4, 0x04,  //ccm
//0xd5, 0x14,	//saturation
//0xd6, 0x14,	//satination
    0xca, 0x90, //0x90,
    0xcb, 0x38, //Sharpness Low
    0xcc, 0x50, //sharpness High
    0x88, 0x00,	//Denoise
    0x92, 0x5c, //Denoise Filter
    0x93, 0x42, //Green pixel Filter
    0x94, 0x42,
    0x95, 0x48,
    0x96, 0x42, //BR pixel Filter
    0x97, 0x42,
    0x98, 0x48,
    0x9a, 0x44,


//0x63, 0x80, //hue red control
//0x64, 0x80,	//hue blue control
//0x65, 0xe8,
//0x66, 0xc8,
    0x41, 0x90, // AE
    0xff, 0xff,
};
const u8 HI708init[] = {
    //p0
    0x03, 0x00,
    0x01, 0xf1,
    0x01, 0xf3,
    0x01, 0xf1,

    0x11, 0x90,
    0x12, 0x00,

    0x20, 0x00,
    0x21, 0x04,
    0x22, 0x00,
    0x23, 0x04,

    0x24, 0x01,
    0x25, 0xe0,
    0x26, 0x02,
    0x27, 0x80,

    0x40, 0x00,
    0x41, 0x70,
    0x42, 0x00,
    0x43, 0x40,

    //blc
    0x80, 0x2e,
    0x81, 0x7e,
    0x82, 0x90,
    0x83, 0x30,
    0x84, 0x2c,
    0x85, 0x4b,
    0x89, 0x48,
    0x90, 0x0b,
    0x91, 0x0b,
    0x92, 0x48,
    0x93, 0x48,
    0x98, 0x38,
    0x99, 0x40,
    0xa0, 0x00,
    0xa8, 0x40,

    //p3
    0x03, 0x02,
    0x13, 0x40,
    0x14, 0x04,
    0x1a, 0x00,
    0x1b, 0x08,

    0x20, 0x33,
    0x21, 0xaa,
    0x22, 0xa7,
    0x23, 0xb1,
    0x3b, 0x48,

    0x50, 0x21,
    0x52, 0xa2,
    0x53, 0x0a,
    0x54, 0x30,
    0x55, 0x10,
    0x56, 0x0c,
    0x59, 0x0f,

    0x60, 0x54,
    0x61, 0x5d,
    0x62, 0x56,
    0x63, 0x5c,
    0x64, 0x56,
    0x65, 0x5c,
    0x72, 0x57,
    0x73, 0x5b,
    0x74, 0x57,
    0x75, 0x5b,
    0x80, 0x02,
    0x81, 0x46,
    0x82, 0x07,
    0x83, 0x10,
    0x84, 0x07,
    0x85, 0x10,
    0x92, 0x24,
    0x93, 0x30,
    0x94, 0x24,
    0x95, 0x30,
    0xa0, 0x03,
    0xa1, 0x45,
    0xa4, 0x45,
    0xa5, 0x03,
    0xa8, 0x12,
    0xa9, 0x20,
    0xaa, 0x34,
    0xab, 0x40,
    0xb8, 0x55,
    0xb9, 0x59,
    0xbc, 0x05,
    0xbd, 0x09,
    0xc0, 0x5f,
    0xc1, 0x67,
    0xc2, 0x5f,
    0xc3, 0x67,
    0xc4, 0x60,
    0xc5, 0x66,
    0xc6, 0x60,
    0xc7, 0x66,
    0xc8, 0x61,
    0xc9, 0x65,
    0xca, 0x61,
    0xcb, 0x65,
    0xcc, 0x62,
    0xcd, 0x64,
    0xce, 0x52,
    0xcf, 0x64,
    0xd0, 0x53,
    0xd1, 0x68,

    //p10
    0x03, 0x10,
    0x10, 0x01, //0x03,
    0x11, 0x43,
    0x12, 0x30,
    0x40, 0x80,
    0x41, 0x02,
    0x48, 0x98,
    0x50, 0x48,

    0x60, 0x7f,
    0x61, 0x00,
    0x62, 0xa0,
    0x63, 0xa0,
    0x64, 0x48,
    0x66, 0x90,
    0x67, 0x36,

    //p11
    0x03, 0x11,
    0x10, 0x25,
    0x11, 0x1f,
    0x20, 0x00,
    0x21, 0x38,
    0x23, 0x0a,

    0x60, 0x10,
    0x61, 0x82,
    0x62, 0x00,
    0x63, 0x83,
    0x64, 0x83,
    0x67, 0xf0,
    0x68, 0x30,
    0x69, 0x10,

    //p12
    0x03, 0x12,
    0x40, 0xe9,
    0x41, 0x09,
    0x50, 0x18,
    0x51, 0x24,


    0x70, 0x1f,
    0x71, 0x00,
    0x72, 0x00,
    0x73, 0x00,
    0x74, 0x10,
    0x75, 0x10,
    0x76, 0x20,
    0x77, 0x80,
    0x78, 0x88,
    0x79, 0x18,

    0xb0, 0x7d,

    //p13
    0x03, 0x13,
    0x10, 0x01,
    0x11, 0x89,
    0x12, 0x14,
    0x13, 0x19,
    0x14, 0x08,
    0x20, 0x06,
    0x21, 0x03,

    0x23, 0x30,
    0x24, 0x33,
    0x25, 0x08,
    0x26, 0x18,
    0x27, 0x00,
    0x28, 0x08,
    0x29, 0x50,
    0x2a, 0xe0,
    0x2b, 0x10,
    0x2c, 0x28,
    0x2d, 0x40,
    0x2e, 0x00,
    0x2f, 0x00,
    0x30, 0x11,
    0x80, 0x03,
    0x81, 0x07,
    0x90, 0x04,
    0x91, 0x02,
    0x92, 0x00,
    0x93, 0x20,
    0x94, 0x42,
    0x95, 0x60,

    //p14
    0x03, 0x14,
    0x10, 0x01,

    0x20, 0x80,
    0x21, 0x80,
    0x22, 0x78,
    0x23, 0x4d,
    0x24, 0x46,

    //p15 color correction
    0x03, 0x15,
    0x10, 0x03,
    0x14, 0x3c,
    0x16, 0x2c,
    0x17, 0x2f,
    0x30, 0xc4,
    0x31, 0x5b,
    0x32, 0x1f,
    0x33, 0x2a,
    0x34, 0xce,
    0x35, 0x24,
    0x36, 0x0b,
    0x37, 0x3f,
    0x38, 0x8a,
    0x40, 0x87,
    0x41, 0x18,
    0x42, 0x91,
    0x43, 0x94,
    0x44, 0x9f,
    0x45, 0x33,
    0x46, 0x00,
    0x47, 0x94,
    0x48, 0x14,

    //p16 gamma
    0x03, 0x16,
    0x30, 0x00,
    0x31, 0x0a,
    0x32, 0x1b,
    0x33, 0x2e,
    0x34, 0x5c,
    0x35, 0x79,
    0x36, 0x95,
    0x37, 0xa4,
    0x38, 0xb1,
    0x39, 0xbd,
    0x3a, 0xc8,
    0x3b, 0xd9,
    0x3c, 0xe8,
    0x3d, 0xf5,
    0x3e, 0xff,

    //P17
    0x03, 0x17,
    0xc4, 0x3c,
    0xc5, 0x32,

    //P20
    0x03, 0x20,
    0x10, 0x0c,
    0x11, 0x04,

    0x20, 0x01,
    0x28, 0x27,
    0x29, 0xa1,
    0x2a, 0xf0,
    0x2b, 0x34,
    0x2c, 0x2b,
    0x30, 0xf8,
    0x39, 0x22,
    0x3a, 0xde,
    0x3b, 0x22,
    0x3c, 0xde,
    0x60, 0x95,
    0x68, 0x3c,
    0x69, 0x64,
    0x6a, 0x28,
    0x6b, 0xc8,

    0x70, 0x42,
    0x76, 0x22,
    0x77, 0x02,
    0x78, 0x12,
    0x79, 0x27,
    0x7a, 0x23,
    0x7c, 0x1d,
    0x7d, 0x22,

    0x83, 0x00,
    0x84, 0xaf,
    0x85, 0x80,
    0x86, 0x00,
    0x87, 0xc0,
    0x88, 0x02,
    0x89, 0x49,
    0x8a, 0x00,
    0x8b, 0x3a,
    0x8c, 0x80,
    0x8d, 0x30,
    0x8e, 0xc0,

    0x91, 0x03,
    0x92, 0xdc,
    0x93, 0x6c,
    0x94, 0x01,
    0x95, 0xb7,
    0x96, 0x74,

    0x98, 0x8c,
    0x99, 0x23,

    0x9c, 0x08,
    0x9d, 0xa0,
    0x9e, 0x00,
    0x9f, 0xc0,

    0xb1, 0x14,
    0xb2, 0x50,
    0xb4, 0x14,
    0xb5, 0x38,
    0xb6, 0x26,
    0xb7, 0x20,
    0xb8, 0x1d,
    0xb9, 0x1b,
    0xba, 0x1a,
    0xbb, 0x19,
    0xbc, 0x19,
    0xbd, 0x18,

    0xc0, 0x16,
    0xc3, 0x48,
    0xc4, 0x48,

    //P22 awb
    0x03, 0x22,
    0x10, 0xf2,
    0x11, 0x2e,
    0x21, 0x40,
    0x30, 0x80,
    0x31, 0x80,
    0x38, 0x12,
    0x39, 0x33,

    0x40, 0xf0,
    0x41, 0x44,
    0x42, 0x33,
    0x43, 0xf3,
    0x44, 0x55,
    0x45, 0x44,
    0x46, 0x02,

    0x80, 0x25,
    0x81, 0x20,
    0x82, 0x48,
    0x83, 0x5e,
    0x84, 0x24,
    0x85, 0x59,
    0x86, 0x24,
    0x87, 0x4d,
    0x88, 0x38,
    0x89, 0x3e,
    0x8a, 0x29,
    0x8b, 0x02,
    0x8d, 0x22,
    0x8e, 0x71,
    0x8f, 0x63,

    0x90, 0x60,
    0x91, 0x5c,
    0x92, 0x56,
    0x93, 0x52,
    0x94, 0x4c,
    0x95, 0x36,
    0x96, 0x31,
    0x97, 0x2e,
    0x98, 0x2a,
    0x99, 0x29,
    0x9a, 0x26,
    0x9b, 0x09,

    0x03, 0x00,
    0x80, 0x08, //bLC
    // 0x11, 0x90 | 1, //旋转，镜像
    0x11, 0x90, //旋转，镜像
    0x43, 0x80,	//VB

    0x03, 0x10,
    0x40, 0x0c, //亮度偏执
    0x48, 0x88, //0x78,
    0x62, 0xc0, //饱和度+3
    0x63, 0xc0, //饱和度+3

    0x03, 0x17,
    0xc4, 0x3c,
    0xc5, 0x32,

    //AEC
    0x03, 0x20,
    0x70, 0x3a, //AE target
    0xb2, 0x90,
//	0x10,0x1c,//close AE
//	0x11,0x04,
//	0x18,0x38,//AE RESET
//	0x2a,0xf0,
//	0x2b,0x34,


    0x88, 0x00, //max曝光时间->min fps
    0x89, 0xb8,
    0x8a, 0xf0,

    0x10, 0x9c,
    0x18, 0x30,


    0x03, 0x13,
    0x20, 0x07,
    0x21, 0x07,
    0x24, 0x00,
    0x29, 0x40,


//    0x03,0x22,
//    0x01,0xfb,
    0x03, 0x20,
    0x10, 0x9c,
    0x01, 0xf0,
    0x03, 0x00,
    0x01, 0xf0,
    0xff, 0xff,
};
const u8 gc0328init[] = {
    0xfe, 0x80,
    0xfe, 0x80,
    0xfc, 0x16,
    0xfc, 0x16,
    0xfc, 0x16,
    0xfc, 0x16,
    0xf1, 0x00,
    0xf2, 0x00,
    0xfe, 0x00,
    0x4f, 0x00,
    0x03, 0x00,
    0x04, 0xc0,
    0x42, 0x00,
    0x77, 0x5a,
    0x78, 0x40,
    0x79, 0x56,

    0xfe, 0x00,
    0x0d, 0x01,
    0x0e, 0xe8,
    0x0f, 0x02,
    0x10, 0x88,
    0x09, 0x00,
    0x0a, 0x00,
    0x0b, 0x00,
    0x0c, 0x00,
    0x16, 0x00,
    0x17, 0x14,
    0x18, 0x0e,
    0x19, 0x06,

    0x1b, 0x48,
    0x1f, 0xC8,
    0x20, 0x01,
    0x21, 0x78,
    0x22, 0xb0,
    0x23, 0x06,
    0x24, 0x11,
    0x26, 0x00,
    0x50, 0x01,  //crop mode

    //global ga,in for range
    0x70, 0x45,

    ///////////,//banding/////////////
    0x05, 0x00, //0x02,//hb
    0x06, 0x84, //0x2c,//
    0x07, 0x00, //vb
    0x08, 0x5e, //0xb8,//
    0xfe, 0x01, //
    0x29, 0x01, //0x00,//anti-flicker step [11:8]
    0x2a, 0x2c, //0x60,//anti-flicker step [7:0]
    0x2b, 0x01, //exp level 0  14.28fps
    0x2c, 0x2c, //
    0x2d, 0x01, //
    0x2e, 0x2c, //
    0x2f, 0x01, //exp level 0  14.28fps
    0x30, 0x2c, //
    0x31, 0x01, //exp level 0  14.28fps
    0x32, 0x2c, //
    //0x2d , 0x02,//exp level 1  12.50fps
    //0x2e , 0x58,//
    //0x2f , 0x03,//exp level 2  10.00fps
    //0x30 , 0x84,//
    //0x31 , 0x04,//exp level 3  7.14fps
    //0x32 , 0xb0,//
    0xfe, 0x00, //

    ///////////,////AWB//////////////
    0xfe, 0x01,
    0x50, 0x00,
    0x4f, 0x00,
    0x4c, 0x01,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4d, 0x30,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x40,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x50,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x60,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x70,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4f, 0x01,
    0x50, 0x88,
    0xfe, 0x00,

    ///////////,/ BLK//////////////////////
    0xfe, 0x00,
    0x27, 0xb7,
    0x28, 0x7F,
    0x29, 0x20,
    0x33, 0x20,
    0x34, 0x20,
    0x35, 0x20,
    0x36, 0x20,
    0x32, 0x08,
    0x3b, 0x00,
    0x3c, 0x00,
    0x3d, 0x00,
    0x3e, 0x00,
    0x47, 0x00,
    0x48, 0x00,

    ///////////,/ block enable/////////////
    0x40, 0x7f,
    0x41, 0x26,
    0x42, 0xfb,
    0x44, 0x00,  //yuv
    0x45, 0x00,
    0x46, 0x02,
    0x4f, 0x01,
    0x4b, 0x01,
    0x50, 0x01,

    /////DN & E,EINTP/////
    0x7e, 0x0a,
    0x7f, 0x03,
    0x81, 0x15,
    0x82, 0x90,
    0x83, 0x02,
    0x84, 0xe5,
    0x90, 0x2c,
    0x92, 0x02,
    0x94, 0x02,
    0x95, 0x35,

    ///////////,/YCP///////////
    0xd1, 0x24, // 0x30 for front
    0xd2, 0x24, // 0x30 for front
    0xd3, 0x40,
    0xdd, 0xd3,
    0xde, 0x38,
    0xe4, 0x88,
    0xe5, 0x40,
    0xd7, 0x0e,

    ///////////,rgb gamma ////////////
    0xfe, 0x00,
    0xbf, 0x0e,
    0xc0, 0x1c,
    0xc1, 0x34,
    0xc2, 0x48,
    0xc3, 0x5a,
    0xc4, 0x6e,
    0xc5, 0x80,
    0xc6, 0x9c,
    0xc7, 0xb4,
    0xc8, 0xc7,
    0xc9, 0xd7,
    0xca, 0xe3,
    0xcb, 0xed,
    0xcc, 0xf2,
    0xcd, 0xf8,
    0xce, 0xfd,
    0xcf, 0xff,

    ///////////,//Y gamma//////////
    0xfe, 0x00,
    0x63, 0x00,
    0x64, 0x05,
    0x65, 0x0b,
    0x66, 0x19,
    0x67, 0x2e,
    0x68, 0x40,
    0x69, 0x54,
    0x6a, 0x66,
    0x6b, 0x86,
    0x6c, 0xa7,
    0x6d, 0xc6,
    0x6e, 0xe4,
    0x6f, 0xff,

    ///////////,///ASDE/////////////
    0xfe, 0x01,
    0x18, 0x02,
    0xfe, 0x00,
    0x97, 0x30,
    0x98, 0x00,
    0x9b, 0x60,
    0x9c, 0x60,
    0xa4, 0x50,
    0xa8, 0x80,
    0xaa, 0x40,
    0xa2, 0x23,
    0xad, 0x28,

    ///////////,///abs///////////
    0xfe, 0x01,
    0x9c, 0x00,
    0x9e, 0xc0,
    0x9f, 0x40,

    ///////////,/// AEC////////////
    0xfe, 0x01,
    0x08, 0xa0,
    0x09, 0xe8,
    0x10, 0x08,
    0x11, 0x21,
    0x12, 0x11,
    0x13, 0x45,
    0x15, 0xfc,
    0x18, 0x02,
    0x21, 0xf0,
    0x22, 0x60,
    0x23, 0x30,
    0x25, 0x00,
    0x24, 0x14,
    0x3d, 0x80,
    0x3e, 0x40,

    ///////////,/////AWB///////////
    0xfe, 0x01,
    0x51, 0x88,
    0x52, 0x12,
    0x53, 0x80,
    0x54, 0x60,
    0x55, 0x01,
    0x56, 0x02,
    0x58, 0x00,
    0x5b, 0x02,
    0x5e, 0xa4,
    0x5f, 0x8a,
    0x61, 0xdc,
    0x62, 0xdc,
    0x70, 0xfc,
    0x71, 0x10,
    0x72, 0x30,
    0x73, 0x0b,
    0x74, 0x0b,
    0x75, 0x01,
    0x76, 0x00,
    0x77, 0x40,
    0x78, 0x70,
    0x79, 0x00,
    0x7b, 0x00,
    0x7c, 0x71,
    0x7d, 0x00,
    0x80, 0x70,
    0x81, 0x58,
    0x82, 0x98,
    0x83, 0x60,
    0x84, 0x58,
    0x85, 0x50,
    0xfe, 0x00,

    ///////////,/////LSC////////////////
    0xfe, 0x01,
    0xc0, 0x10,
    0xc1, 0x0c,
    0xc2, 0x0a,
    0xc6, 0x0e,
    0xc7, 0x0b,
    0xc8, 0x0a,
    0xba, 0x26,
    0xbb, 0x1c,
    0xbc, 0x1d,
    0xb4, 0x23,
    0xb5, 0x1c,
    0xb6, 0x1a,
    0xc3, 0x00,
    0xc4, 0x00,
    0xc5, 0x00,
    0xc9, 0x00,
    0xca, 0x00,
    0xcb, 0x00,
    0xbd, 0x00,
    0xbe, 0x00,
    0xbf, 0x00,
    0xb7, 0x07,
    0xb8, 0x05,
    0xb9, 0x05,
    0xa8, 0x07,
    0xa9, 0x06,
    0xaa, 0x00,
    0xab, 0x04,
    0xac, 0x00,
    0xad, 0x02,
    0xae, 0x0d,
    0xaf, 0x05,
    0xb0, 0x00,
    0xb1, 0x07,
    0xb2, 0x03,
    0xb3, 0x00,
    0xa4, 0x00,
    0xa5, 0x00,
    0xa6, 0x00,
    0xa7, 0x00,
    0xa1, 0x3c,
    0xa2, 0x50,
    0xfe, 0x00,

    ///////////,////CCT ///////////
    0xb1, 0x12,
    0xb2, 0xf5,
    0xb3, 0xfe,
    0xb4, 0xe0,
    0xb5, 0x15,
    0xb6, 0xc8,

    /*   /////s,kin CC for front //////
    	 0xb1 , 0x00,
    	 0xb2 , 0x00,
    	 0xb3 , 0x00,
    	 0xb4 , 0xf0,
    	 0xb5 , 0x00,
    	 0xb6 , 0x00,
    	 */

    ///////////,////AWB////////////////
    0xfe, 0x01,
    0x50, 0x00,
    0xfe, 0x01,
    0x4f, 0x00,
    0x4c, 0x01,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4f, 0x00,
    0x4d, 0x34,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x02,
    0x4e, 0x02,
    0x4d, 0x44,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x53,
    0x4e, 0x00,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4e, 0x04,
    0x4d, 0x65,
    0x4e, 0x04,
    0x4d, 0x73,
    0x4e, 0x20,
    0x4d, 0x83,
    0x4e, 0x20,
    0x4f, 0x01,
    0x50, 0x88,
    /////////ou,tput////////
    0xfe, 0x00,
    0xf1, 0x07,
    0xf2, 0x01,
    0xff, 0xff
};

const u8 bf3603init[] = {
    0x12, 0x80,
    //	0xff,0xff,
    0x09, 0x00,
    0x11, 0x80,
    0x13, 0x00,
    0x01, 0x15,
    0x02, 0x22,
    0x87, 0x18,
    0x8c, 0x01, //01 分频  02 不分频
    0x8d, 0x7c, //7C 分频  64 不分频
    0x13, 0x07,

    //window
    0x17, 0x00,	//HStart
    0x18, 0xa0,	//HStop
    0x19, 0x00,	//VStart
    0x1a, 0x78,	//VStop

    //frame rate limit
    0x2a, 0x00,	//hb_h
    0x2b, 0x00,	//hb_l
    0x2c, 0x01,	//vb_en
    0x92, 0x6e,	//vb_l
    0x93, 0x00,	//vb_h
    0x8e, 0x02,	//max_int_h
    0x8f, 0x67,	//max_int_l


    // DBLK manual
    0x28, 0x03,
    0x2c, 0x03,
    0x00, 0x20,
    0x0d, 0x20,
    0x0e, 0x20,
    0x0f, 0x20,
    0x05, 0x16,
    0x14, 0x16,
    0x06, 0x19,
    0x08, 0x19,
    0x26, 0x08,
    0x27, 0x08,
    0X1F, 0xa0,
    0X22, 0xa0, //for  DBLK manual
    //DBLK auto
    0x28, 0x00,
    0x2c, 0x00,
    /* {0x1e,0x30},   */
    0x1e, 0x00,
    /* {0x15,0x40}, */
    0x15, 0x02,
    0x3A, 0x02,
    0x2f, 0x00,
    0x16, 0x20,
    0x29, 0x04,
    0x56, 0x40,


    //lens shading
    0x35, 0x60,
    0x65, 0x58,
    0x66, 0x58,
    //global gain
    0x82, 0x16,
    0x83, 0x25,
    0x84, 0x1a,
    0x85, 0x26,
    0x86, 0x30,

    0x96, 0x26, // AE speed
    0x97, 0x0c,

    0x70, 0x6f,
    0x72, 0x4f,
    0x73, 0x2f,
    0x74, 0x27,
    0x75, 0x0e,
    0x69, 0x00,
    0x76, 0xff,
    0x80, 0x55,
    0x89, 0x02, //02 分频  05 不分频
    0x8a, 0xf8, //f8 分频  fc 不分频
    //black level
    0x90, 0x20,
    0x91, 0x1c,

    0X1F, 0x20,
    0X22, 0x20,
    0x39, 0x80,
    0x3f, 0xC0,

    0x3b, 0x60, //自适应
    0x3c, 0x10,


    //gamma 低噪
    0X40, 0X32,
    0X41, 0X2c,
    0X42, 0X30,
    0X43, 0X1d,
    0X44, 0X1a,
    0X45, 0X14,
    0X46, 0X11,
    0X47, 0X0e,
    0X48, 0X0d,
    0X49, 0X0c,
    0X4b, 0X0B,
    0X4c, 0X09,
    0X4e, 0X09,
    0X4f, 0X08,
    0X50, 0X07,

    //color matrix
    0x51, 0x30,
    0x52, 0x92,
    0x53, 0x02,
    0x54, 0x86,
    0x57, 0x30,
    0x58, 0x8a,
    0x59, 0x80,
    0x5a, 0x92,
    0x5b, 0x32,

    0x5c, 0x2e,
    0x5d, 0x17,
    // old  AWB
    0x6a, 0x01,
    0x23, 0x66,
    0xa0, 0x9f,
    0xa1, 0x21,
    0xa2, 0x11,
    0xa3, 0x29,
    0xa4, 0x0e,
    0xa5, 0x26,
    0xa6, 0x04,
    0xa7, 0x80,
    0xa8, 0x80,
    0xa9, 0x26,
    0xaa, 0x26,
    0xab, 0x2a,
    0xac, 0x3c,
    0xad, 0xf0,
    0xae, 0xff,

    // color saturation
    0xb0, 0xe4,
    0xb1, 0xc0,
    0xb2, 0xb0,
    0xb3, 0x86,
    //AE target
    0x24, 0x8a,
    0x25, 0x7a,
    //anti webcamera banding
    0x9d, 0x4c,
    0xff, 0xff,
};
const u8 gc0307init[] = {
    0x48,  0xc3, //
    0x4E,  0x22, //	sync mode
    //	4 opclk_gated_in_subsample
    //	3 pclk_gated in HB
    //	2 pclk_polarity
    //	1 hsync_polarity
    //	0 vsync_polarity

    //========= frame timing
    0x01,  0xed,//0xab, //  865-->0x9c  --->25fps
    //  HB,  win_width 640 + 4(CISCTL fixed value) + sh_delay 50 +HB 106=800
    0x10,  0x00, //

    //0x02,  0x04,//0x70, //	VB, win_height488+VB112=600
    0x02,  0x1c,

    0x1C,  0x02, //	Vs_st
    0x1D,  0x02, //	Vs_et
    //0x10,  0x00, //	high 4 bits of VB, HB
    0x11,  0x05, //	row_tail,  AD_pipe_number

    0x08,  0x00,
    0x06,  0x00,
    0xd1,  0x4a,

    //========= windowing
    0x09,  0x01, //win height
    0x0A,  0xE8,
    0x0B,  0x02, //win width, pixel array only 640
    0x0C,  0x80,

    //========= analog
    0x0D,  0x77,	//0x77,
    0x0E,  0x02,
    0x0F,  0x82,	// [5:4]  mirror
    0x10,  0x00,
    0x12,  0x70,
    0x13,  0x00,
    0x14,  0x00,
    0x15,  0xba,
    0x16,  0x13,
    0x17,  0x52,
    0x18,  0x50,
    0x1E,  0x41,	//0x0d,
    0x1F,  0x32,
    0x19,  0x06,
    0x1a,  0x06,

    //========= offset =========//

    0x59,  0x0f,

    //check 5a,5b

    0x58,  0x88,  //88, more G substract

    0x57,  0x08,  //08, why 20?
    0x56,  0x77,  //77, why 65?

    0x3c,  0x00,   //manual offset adjust
    0x3d,  0x00,
    0x3e,  0x00,
    0x3f,  0x00,

    //========= blk
    0x35,  0xd8,
    0x36,  0x40,

    //========= auto saturation
    0xb5,  0x70,
    0xb6,  0x30, //0x38,//0x40
    0xb7,  0x00,
    0xb8,  0x30, //0x38,
    0xb9,  0xc2, //0xc2
    0xba,  0x0f,


    //========= manual_gain
    //0x61,  0x80, //	manual_gain_g1
    0x63,  0x9a, //	manual_gain_r
    0x65,  0x98,//0x98, //	manual_gai_b, 0xa0=1.25, 0x98=1.1875
    //0x67,  0x80, //	manual_gain_g2
    0x68,  0x17, //	18,global_manual_gain   2.4bits, why 24
    //	pga_offset, usage max value about 0xBB,  range_gain = 1024/(1024-0xc0)= 0x14 is enough

    //=========CC  cc7.txt
    0x69,  0x58,  //54   red
    0x6A,  0xf6, //ff
    0x6B,  0xfb, //fe
    0x6C,  0xf4, //ff
    0x6D,  0x5a, //5f
    0x6E,  0xe6, //e1


    //=========lsc
    0x70,  0x14,
    0x71,  0x1c,
    0x72,  0x20,
    0x73,  0x10,
    0x74,  0x3c,
    0x75,  0x52,


    //=========dn
    0x7d,  0x0f,  //dn_mode
    0x7e,  0x35, //35
    0x7f,  0x86, //86
    //0x80,  0x0c,
    //0x81,  0x0c,
    //0x82,  0x44,

    //dd
    0x83,  0x18,
    0x84,  0x18,
    0x85,  0x04,
    0x87,  0x34,


    //=========intp-ee

    0x88,  0x04,
    0x89,  0x01,
    0x8a,  0x50,//0x60,  //edge max, edge_min
    0x8b,  0x50,//0x60,
    0x8c,  0x07,  //edge mode
    0x8d,  0x86,



    0x5a,  0x45,
    0x5b,  0x42,
    0x5c,  0x58,	// 0x68,
    0x5d,  0x58,	// 0x68,

    0x50,  0x0c,//intp mode
    0x5f,  0x3c,

    0x8e,  0x04,//0x02,//edge_th
    0x86,  0x04,//0x02,

    0x51,  0x20,
    0x52,  0x08,
    0x53,  0x00,


    //========= YCP
    //saturation
    //0xa0,  0x40,
    0x77,  0x80,   //contrast center
    0x78,  0x00,
    0x79,  0x00,
    0x7a,  0x00,//0xf0,   //luma offset
    0xa1,  0x40,   //40, contrast
    0xa2,  0x38, //0x38  //40
    0xa3,  0x38, //0x38

    0xa4,  0xc8,
    0xa5,  0x02,
    0xa6,  0x28,
    0xa7,  0x02,
    0xa8,  0xee,
    0xa9,  0x12,
    0xaa,  0x01,
    0xab,  0x20,

    //========= ABS
    0xad,  0x10,
    0xae,  0x18, //
    0xaf,  0x74, //74
    0xb0,  0xf0, //e0
    0xb1,  0x20, //20
    0xb2,  0x6c, //
    0xb3,  0x40, //
    0xb4,  0x04, //
    0xb6,  0x40,
    0xb8,  0x38,
    0xb9,  0xc3,


    //========= AWB
    0xbb,  0x42,
    0xbc,  0x60,
    0xbd,  0x50,
    0xbe,  0x50,

    0xbf,  0x0c,
    0xc0,  0x06,
    0xc1,  0x70,//70
    0xc2,  0xf1,//f1
    0xc3,  0x40,//40
    0xc4,  0x20,//18, why 1c
    0xc5,  0x33,//23
    0xc6,  0x1d,
    0xc7,  0x49,
    0xc8,  0x45,

    0xca,  0x54,//0x4a,0x56, //AWB_R_gain_limit, D_light, R_gain~=ff  50
    0xcb,  0x52,//0x4a,0x52, //AWB_G_gain_limit, A_light, G_gain~=ff  4c
    0xcc,  0x6b,//0x51,0x66, //AWB_B_gain_limit, A_light, B_gain~=ff  88


    0xcd,  0x80, //R_ratio
    0xce,  0x80, //G_ratio
    0xcf,  0x80, //B_ratio


    //=========  aecT
    0x20,  0x07,  //07
    0x21,  0x80, //a0
    0x22,  0x40, //0x48, 52
    0x23,  0x88,
    0x24,  0x96,  //0x96,
    0x25,  0x01,
    0x26,  0x01,
    0x27,  0x00,
    /*
       0x28,  0x02, // 25FPS
       0x29,  0x0c,
       0x2a,  0x04,  //12.5FPS
       0x2b,  0x18,
       0x2c,  0x08, //6.25FPS
       0x2d,  0x30,
       0x2e,  0x0c, // 4 FPS
       0x2f,  0xb0,
       */

    0x28,  0x02, // 30fps *4
    0x29,  0x02,
    0x2a,  0x02,  // 15fps *2
    0x2b,  0x02,
    0x2c,  0x02, // 8fps *1.5
    0x2d,  0x02,
    0x2e,  0x02, // 4 fps *2
    0x2f,  0x02,
    /*
       0x28, 0x02,   //exp level 1  // 4
       0x29, 0x5c,
       0x2a, 0x03,   //exp level 2  // 6
       0x2b, 0x8a,
       0x2c, 0x07,   //exp level 3  // 12
       0x2d, 0x14,
       0x2e, 0x0d,   //exp level 4  // 23
       0x2f, 0x91,
       */
    0x30,  0x20,
    0x31,  0x00,
    0x32,  0x00,
    0x33,  0x90,
    0x34,  0x10,

    0xd0,  0x34,
    0xd1,  0x39,//0x50, //AE Target
    0xd2,  0xf2,
    0xd3,  0xa5,
    0xd4,  0x96,
    0xd5,  0x10,
    0xd6,  0x9c,
    //0xd7,  0x9c,
    0xd7,  0x04,
    //	0xd6,  0x97,
    //	0xd7,  0x97,
    0xd8,  0x02,

    0xdb,  0x40,
    0xdd,  0x22, //level 3 , 6.25FPS

    //=========
    0xe0,  0x03,
    0xe1,  0x02,
    0xe2,  0x27,
    0xe3,  0x1e,
    0xe8,  0x3b,
    0xe9,  0x6e,
    0xea,  0x2c,
    0xeb,  0x50,
    0xec,  0x73,

    //=========
    //=========
    0xf0,  0x01,


    //========= lscP
    0x45,  0x07,
    0x46,  0x06,
    0x47,  0x05,
    0x48,  0x06,
    0x49,  0x05,
    0x4a,  0x04,

    //============================= awbP

    0x62,  0xec,
    0x63,  0x20,
    0x64,  0x16,
    0x65,  0x20,
    0x66,  0xec,
    0x67,  0x16,


    //============================= ccP

    0x69,  0x00,
    //CC_G
    0x70,  0x47, //0x5d
    0x71,  0xf2, //ed
    0x72,  0xff, //ff
    0x73,  0xff, //e5
    0x74,  0x5d, //5f
    0x75,  0xe6, //e6

    //CC_B
    0x76,  0x40, //41
    0x77,  0xf0, //ef
    0x78,  0x00, //ff
    0x79,  0x00, //ff
    0x7a,  0x4b, //5f
    0x7b,  0xd1, //fa

    //============================= AGP
    0x7f,  0x10,
    0x80,  0x48,
    0x81,  0x03,
    0x82,  0x0f,

    //chris 20090403
    0x83,  0x14,  //23
    0x84,  0x28,  //38
    0x85,  0x44,  //4
    0x86,  0x5d,
    0x87,  0x72,
    0x88,  0x86,
    0x89,  0x95,
    0x8a,  0xb1,
    0x8b,  0xc6,
    0x8c,  0xd5,
    0x8d,  0xde,
    0x8e,  0xe6,
    0x8f,  0xec,
    0x90,  0xf0,
    0x91,  0xf7,
    0x92,  0xfb,
    0x93,  0xff,

    //about gamma1 is hex r oct
    0x94,  0x04,
    0x95,  0x0E,
    0x96,  0x1B,
    0x97,  0x28,
    0x98,  0x35,
    0x99,  0x41,
    0x9a,  0x4E,
    0x9b,  0x67,
    0x9c,  0x7E,
    0x9d,  0x94,
    0x9e,  0xA7,
    0x9f,  0xBA,
    0xa0,  0xC8,
    0xa1,  0xD4,
    0xa2,  0xE7,
    0xa3,  0xF4,
    0xa4,  0xFA,

    //========= open functions
    0xf0,  0x00,   //set back to page0
    0x44,  0xe0,	//	mirror before is 0xe2
    0x45,  0x24,	//	mirror before is 0x27,
    0x47,  0x20,  //	mirror before is 0x2c ;   60 , bit7 dark_mode 1 dark_row
    0x40,  0x7c, // close lens
    0x41,  0x0f, // 0x0f, // open abs
    0x42,  0x10,
    0x4a,  0x00,
    0x4f,  0x01,
    0x43,  0x40,
    0xff, 0xff,
};
const u8 gc0308init[] = {
    0xfe, 0x00,

    0x0f, 0x00,	//[7:4]VB_High_4bit, [3:0]HB_High_4bit
    0x01, 0xbe,	//hb[7:0]
    0x02, 0x0f,	//vb[7:0]
    0x03, 0x2c,	//hb[7:0]
    0x04, 0x01,	//vb[7:0]

    0xec, 0x20,	//[5:4]exp_level  [3:0]minimum exposure high 4 bits

    0xe2, 0x00, 	//anti-flicker step [7:0]
    0xe3, 0x97, 	//anti-flicker step [7:0]
    0xe4, 0x02, 	//exp level 1
    0xe5, 0x5c,
    0xe6, 0x03, 	//exp level 2
    0xe7, 0x8a,
    0xe8, 0x07, 	//exp level 3
    0xe9, 0x14,
    0xea, 0x0d, 	//exp level 4
    0xeb, 0x91,


    0x05, 0x00,	// row_start_high
    0x06, 0x00,	// row_start_low
    0x07, 0x00,	// col_start_high
    0x08, 0x00,	// col_start_low
    0x09, 0x01,	//[8]cis_win_height  488
    0x0a, 0xe8,	//[7:0]cis_win_height
    0x0b, 0x02,	//[9:8]cis_win_width 648
    0x0c, 0x88,	//[7:0]cis_win_width
    0x0d, 0x02,	//vs_st
    0x0e, 0x02,	//vs_et

    0x10, 0x24,	//*26[7:4]restg_width, [3:0]sh_width
    0x11, 0x0d,	//fd//[7:4]tx_width, [3:0]space width,*2
    0x12, 0x2a,	//sh_delay
    0x13, 0x00,	//[3:0] row_tail_width
    0x14, 0x10,	//[7]hsync_always ,[6] NA,  [5:4] CFA sequence
    // [3:2]NA,  [1]upside_down,  [0] mirror
    0x15, 0x0a,	//[7:6]output_mode,,[5:4]restg_mode,[3:2]sdark_mode, [1]new exposure,[0]badframe_en
    0x16, 0x05,	//[7:5]NA, [4]capture_ad_data_edge, [3:0]Number of A/D pipe stages
    0x17, 0x01,	//[7:6]analog_opa_r,[5]coltest_en, [4]ad_test_enable,
    //[3]tx_allow,[2]black sun correction,[1:0]black sun control reg
    0x18, 0x44,	//[7]NA,  [6:4]column gain ee, [3]NA, [2:0]column gain eo
    0x19, 0x44,	//[7]NA,  [6:4]column gain oe, [3]NA, [2:0]column gain oo
    0x1a, 0x1a,	// 1e//[7]rsv1,[6]rsv0, [5:4]coln_r,
    //[3:2]colg_r column gain opa bias current, [1]clk_delay, [0] apwd
    0x1b, 0x00,	//[7:2]NA, [1:0]BIN4 AND BIN2
    0x1c, 0x49,	//c1//[7]hrst_enbale, [6:4]da_rsg, [3]tx high enable, [2]NA, [1:0]da18_r
    0x1d, 0xBa,	//08//[7]vref_en, [6:4]da_vef, [3]da25_en, [2]NA, [1:0]da25_r,set da25 voltage
    0x1e, 0x61,	//60//[7]LP_MTD,[6:5]opa_r,ADC's operating current,  [4:2]NA, [1:0]sref
    0x1f, 0x15,	//[7:6]NA, [5:4]sync_drv, [3:2]data_drv, [1:0]pclk_drv

    0x20, 0xfe,	//[7]bks[6]gamma[5]cc[4]ee[3]intp[2]dn[1]dd[0]lsc
    0x21, 0xf8,	//[7]na[6]na[5]skin_ee[4]cbcr_hue_en[3]y_as_en[2]auto_gray_en[1]y_gamma_en[0]na
    0x22, 0x57,	//*57[7]na [6]auto_dndd [5]auto_ee [4]auto_sa [3]na [2]abs [1]awb  [0]na
    0x24, 0xa0,	//
    0x25, 0x0f,

    //output sync_mode
    0x26, 0x02,	//
    0x28, 0x00,
    0x2a, 0xff,
    0x2b, 0x03,
    0x2c, 0x00,
    0x2f, 0x01,	//*30	debug mode3
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// grab     ////////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x30, 0xf7,	//blk mode [7]dark current mode:1 use exp rated dark ,0 use ndark row calculated
    //[1]dark_current_en
    //[0]offset_en
    0x31, 0x40,	//blk_value limit.64 low align to 11bits;8 for 256 range
    0x32, 0x00,	//global offset
    0x39, 0x04,	// exp_ate_darkc
    0x3a, 0x20,	//{7:6}offset submode {5:0}offset ratio
    0x3b, 0x20,	//{7:6}darkc submode {5:0}dark current ratio
    0x3c, 0x3e,	//manual g1 offset
    0x3d, 0x3d,	//manual r offset
    0x3e, 0x00,	//manual b offset
    0x3f, 0x3e,	//manual g2 offset
    //gain
    0x50, 0x16,	//10  //global gain
    //0x50, 0x16,	//10  //global gain

    0x53, 0x80,	//G
    0x54, 0x80,	//R channel gain
    0x55, 0x80,	//B channel gain
    0x56, 0x80,
    0x57, 0x83,
    0x59, 0x85,
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// LSC_t    ////////////////////////////////
    /////////////////////////////////////////////////////////////////////

    0x8b, 0x22, 	//r2
    0x8c, 0x20, 	//g2
    0x8d, 0x20, 	//b2
    0x8e, 0x10, 	//r4
    0x8f, 0x10, 	//g4
    0x90, 0x10, 	//b4
    0x91, 0x3c, 	//[7]singed4 [6:0]row_cneter
    0x92, 0x50, 	//col_center
    0x5a, 0x4d,
    0x5c, 0x56,
    0x5d, 0x12, 	//decrease 1
    0x5e, 0x1a, 	//decrease 2
    0x5f, 0x24, 	//decrease 3
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// DNDD_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x60, 0x07,	//[4]zero weight mode
    //[3]share mode
    //[2]c weight mode
    //[1]lsc decrease mode
    //[0]b mode
    0x61, 0x15,	//[7:6]na
    //[5:4]c weight adap ratio
    //[3:2]dn lsc ratio
    //[1:0]b ratio
    0x62, 0x04,	//b base
    //0x63,0x02 ,	//b increase RO
    0x64, 0x03,	//[7:4]n base [3:0]c weight
    //0x65    	//[7:4]n increase [3:0]c coeff
    0x66, 0xe5,	//dark_th ,bright_th
    0x67, 0x86,	//flat high, flat low
    0x68, 0x82,	//[7:4]dd limit [1:0]dd ratio

    /////////////////////////////////////////////////////////////////////
    /////////////////////////// asde_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x69, 0x18,	//gain high th
    0x6a, 0x0f,	//[7:4]dn_c slop          //[3]use post_gain [2]use pre_gain [1]use global gain [0]use col gain
    0x6b, 0x00,	//[7:4]dn_b slop [3:0]dn_n slop
    0x6c, 0x5f,	//[7:4]bright_th start [3:0]bright_th slop
    0x6d, 0x8f,	//[7:4]dd_limit_start[3:0]dd_limit slop
    0x6e, 0x55,	//[7:4]ee1 effect start [3:0]slope  broad
    0x6f, 0x38,	//[7:4]ee2 effect start [3:0]slope  narrow
    0x70, 0x15,	//saturation dec slope
    0x71, 0x23,	// 0x43 [7:4]low limit,[3:0]saturation slope
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// eeintp_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x72, 0xdc,	//[7]edge_add_mode [6]new edge mode [5]edge2_mode [4]HP_mode
    //[3]lp intp en [2]lp edge en [1:0]lp edge mode

    0x73, 0x80,	//[7]edge_add_mode2 [6]NA [5]only 2direction [4]fixed direction th
    //[3]only defect map [2]intp_map dir [1]HP_acc [0]only edge map

    //for high resolution in light scene
    0x74, 0x02,	//direction th1
    0x75, 0x3f,	//direction th2
    0x76, 0x02,	//direction diff th      h>v+diff ; h>th1 ; v<th2
    0x77, 0x44,	// 0x56,	//[7:4]edge1_effect [3:0]edge2_effect
    0x78, 0x88,	//[7:4]edge_pos_ratio  [3:0]edge neg ratio
    0x79, 0x81,	//edge1_max,edge1_min
    0x7a, 0x81,	//edge2_max,edge2_min
    0x7b, 0x22,	//edge1_th,edge2_th
    0x7c, 0xff,	//pos_edge_max,neg_edge_max
    //for high resolution in light scene


    //CCT
    0x93, 0x44,
    0x94, 0x02,
    0x95, 0x06,
    0x96, 0xd8,
    0x97, 0x38,
    0x98, 0xf0,
    //YCPT
    0xb0, 0x40,
    0xb1, 0x38,	//manual cb
    0xb2, 0x38,	//manual cr
    0xb3, 0x38,
    0xb6, 0xe0,
    0xbd, 0x3f,
    0xbe, 0x36,	// [5:4]gray mode 00:4&8  01:4&12 10:4&20  11:8$16   [3:0] auto_gray
    0xbf, 0x01,
    //=================

    //AECT
    0xd0, 0xCb,	// exp is gc mode
    0xd1, 0x10,	//every N
    0xd2, 0x90,	// 7 aec enable 5 clore y mode 4skin weight 3 weight drop mode
    0xd3, 0x45,	 // 0x50 Y_target and low pixel thd high X4 low X2
    0xd4, 0x42,
    0xd5, 0xF2,	//lhig
    0xd6, 0x16,	// ignore mode
    0xdb, 0x92,
    0xdc, 0xA5,	//fast_margin  fast_ratio
    0xdd, 0x96,
    0xdf, 0x23,	// I_fram D_ratio

    0xd9, 0x00,	// colore offset in CAL ,now is too dark so set zero
    0xda, 0x00,	// GB offset
    0xe0, 0x09,


    0xed, 0x97,	//minimum exposure low 8  bits
    0xee, 0xe0,	// 0xa0 max_post_dg_gain
    0xef, 0x40,	//max_pre_dg_gain


    //ABBT
    0x80, 0x03,
    //0x9f , 0x16,
    //0xa0 , 0x2e,


    0x9f, 0x16,
    0xa0, 0x2C,
    0xa1, 0x4e,
    0xa2, 0x6d,
    0xa3, 0x80,
    0xa4, 0x94,
    0xa5, 0xa3,
    0xa6, 0xbe,
    0xa7, 0xd1,
    0xa8, 0xe2,
    0xa9, 0xec,
    0xaa, 0xf6,
    0xab, 0xfc,
    0xac, 0xff,
    0xad, 0xff,
    0xae, 0xff,
    0xaf, 0xff,

    0xc0, 0x00,
    0xc1, 0x0b,
    0xc2, 0x15,
    0xc3, 0x27,
    0xc4, 0x39,
    0xc5, 0x49,
    0xc6, 0x5a,
    0xc7, 0x6a,
    0xc8, 0x89,
    0xc9, 0xa8,
    0xca, 0xc6,
    0xcb, 0xef,
    0xcc, 0xff,
    //ABS
    0xf0, 0x02,
    0xf1, 0x01,
    0xf2, 0x01,	//manual stretch K
    0xf3, 0x30, 	//the limit of Y_stretch
    0xf4, 0xcb,
    0xf5, 0x59,
    0xf9, 0x9f,
    0xfa, 0x78,


    /////////////////////////////////////////////////////////////////////
    /////////////////////////// AWB_p     ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0xfe, 0x01,
    0x00, 0xf5,		//high_low limit
    0x01, 0x0a,		//y2c center
    0x02, 0x20,		//y2c
    0x05, 0x10,
    0x06, 0x20,
    0x08, 0x0a,
    0x0a, 0xa0,		// number limit
    0x0b, 0x60,		// skip_mode
    0x0c, 0x08,
    0x0e, 0x4c,		// width step
    0x0f, 0x39,		// height step
    0x10, 0x41,
    0x11, 0x37,
    0x12, 0x44,
    0x13, 0x39,		//smooth 2
    0x14, 0x43,		//R_5k_gain_base
    0x15, 0x45,		//B_5k_gain_base
    0x16, 0xbc,		//sinT
    0x17, 0xac,		//cosT
    0x18, 0x18,		//X1 thd
    0x19, 0x40,		//X2 thd
    0x1a, 0xd0,		//Y1 thd
    0x1b, 0xf5,		//Y2 thd


    0x70, 0x40,		// A R2G low
    0x71, 0x58,		// A R2G high
    0x72, 0x30,		// A B2G low
    0x73, 0x48,		// A B2G high
    0x74, 0x20,		// A G low
    0x75, 0x60,		// A G high
    0x78, 0x32,


    //HSP
    0x30, 0x03,	//[1]HSP_en [0]sa_curve_en
    0x31, 0x40,
    0x32, 0xe0,
    0x33, 0xe0,
    0x34, 0xe0,
    0x35, 0xb0,
    0x36, 0xc0,
    0x37, 0xc0,
    0x38, 0x04,	//sat1, at8
    0x39, 0x09,
    0x3a, 0x12,
    0x3b, 0x1C,
    0x3c, 0x28,
    0x3d, 0x31,
    0x3e, 0x44,
    0x3f, 0x57,
    0x40, 0x6C,
    0x41, 0x81,
    0x42, 0x94,
    0x43, 0xA7,
    0x44, 0xB8,
    0x45, 0xD6,
    0x46, 0xEE,	//sat15,at224
    0x47, 0x0c,	//blue_edge_dec_ratio
    //OUT

    0xfe, 0x00,
    0x01, 0x6b,  //HB
    0x02, 0x70,  //VB
    0x03, 0x00,
    0x04, 0x96,
    0xe3, 0x96, 	 //anti-flicker step [7:0]
    0xe4, 0x02,  //exp level 1  // 4
    0xe5, 0x58,
    0xe6, 0x02,  //exp level 2  // 6
    0xe7, 0x58,
    0xe8, 0x02,  //exp level 2  // 6
    0xe9, 0x58,
    0xea, 0x02,  //exp level 2  // 6
    0xeb, 0x58,

    0xff, 0xff
};

const u8 gc0309init[] = {
    0xfe, 0x00,

    0x0f, 0x00,	//[7:4]VB_High_4bit, [3:0]HB_High_4bit
    0x01, 0xbe,	//hb[7:0]
    0x02, 0x38,//0x0f,	//vb[7:0]

    0xec, 0x20,	//[5:4]exp_level  [3:0]minimum exposure high 4 bits

    0xe2, 0x01, 	//anti-flicker step [7:0]
    0xe3, 0x10, 	//anti-flicker step [7:0]
    0xe4, 0x01, 	//exp level 1
    0xe5, 0x10,
    0xe6, 0x02, 	//exp level 2
    0xe7, 0x20,
    0xe8, 0x02, 	//exp level 3
    0xe9, 0x20,
    0xea, 0x04, 	//exp level 4
    0xeb, 0x40,

    0x05, 0x00,	// row_start_high
    0x06, 0x00,	// row_start_low
    0x07, 0x00,	// col_start_high
    0x08, 0x00,	// col_start_low
    0x09, 0x01,	//[8]cis_win_height  488
    0x0a, 0xe8,	//[7:0]cis_win_height
    0x0b, 0x02,	//[9:8]cis_win_width 648
    0x0c, 0x88,	//[7:0]cis_win_width
    0x0d, 0x02,	//vs_st
    0x0e, 0x02,	//vs_et

    0x10, 0x26,	//*26[7:4]restg_width, [3:0]sh_width
    0x11, 0x0d,	//fd//[7:4]tx_width, [3:0]space width,*2
    0x12, 0x2a,	//sh_delay
    0x13, 0x00,	//[3:0] row_tail_width
    0x14, 0x10,	//[7]hsync_always ,[6] NA,  [5:4] CFA sequence
    // [3:2]NA,  [1]upside_down,  [0] mirror
    0x15, 0x0a,	//[7:6]output_mode,,[5:4]restg_mode,[3:2]sdark_mode, [1]new exposure,[0]badframe_en
    0x16, 0x05,	//[7:5]NA, [4]capture_ad_data_edge, [3:0]Number of A/D pipe stages
    0x17, 0x01,	//[7:6]analog_opa_r,[5]coltest_en, [4]ad_test_enable,
    //[3]tx_allow,[2]black sun correction,[1:0]black sun control reg
    0x18, 0x44,	//[7]NA,  [6:4]column gain ee, [3]NA, [2:0]column gain eo
    0x19, 0x44,	//[7]NA,  [6:4]column gain oe, [3]NA, [2:0]column gain oo
    0x1a, 0x2a,	// 1e//[7]rsv1,[6]rsv0, [5:4]coln_r,
    //[3:2]colg_r column gain opa bias current, [1]clk_delay, [0] apwd
    0x1b, 0x00,	//[7:2]NA, [1:0]BIN4 AND BIN2
    0x1c, 0x49,	//c1//[7]hrst_enbale, [6:4]da_rsg, [3]tx high enable, [2]NA, [1:0]da18_r
    0x1d, 0x9a,	//08//[7]vref_en, [6:4]da_vef, [3]da25_en, [2]NA, [1:0]da25_r,set da25 voltage
    0x1e, 0x61,	//60//[7]LP_MTD,[6:5]opa_r,ADC's operating current,  [4:2]NA, [1:0]sref
    0x1f, 0x16,	//[7:6]NA, [5:4]sync_drv, [3:2]data_drv, [1:0]pclk_drv

    0x20, 0xfe,	//[7]bks[6]gamma[5]cc[4]ee[3]intp[2]dn[1]dd[0]lsc
    0x21, 0xfa,	//[7]na[6]na[5]skin_ee[4]cbcr_hue_en[3]y_as_en[2]auto_gray_en[1]y_gamma_en[0]na
    0x22, 0x47,	//*57[7]na [6]auto_dndd [5]auto_ee [4]auto_sa [3]na [2]abs [1]awb  [0]na
    0x24, 0xa0,	//
    0x25, 0x0f,

    //output sync_mode
    0x26, 0x02,	//
    0x28, 0x02,
    0x2a, 0x04,
    0x2b, 0x00,
    0x2c, 0x0c,
    0x2f, 0x01,	//*30	debug mode3
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// grab     ////////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x30, 0xf7,	//blk mode [7]dark current mode:1 use exp rated dark ,0 use ndark row calculated
    //[1]dark_current_en
    //[0]offset_en
    0x31, 0x50,	//blk_value limit.64 low align to 11bits;8 for 256 range
    0x32, 0x00,	//global offset
    0x39, 0x04,	// exp_ate_darkc
    0x3a, 0x20,	//{7:6}offset submode {5:0}offset ratio
    0x3b, 0x20,	//{7:6}darkc submode {5:0}dark current ratio
    0x3c, 0x01,	//manual g1 offset
    0x3d, 0xFE,	//manual r offset
    0x3e, 0x01,	//manual b offset
    0x3f, 0x01,	//manual g2 offset
    //gain
    0x50, 0x2a,	//10  //global gain

    0x53, 0x80,	//G
    0x54, 0x80,	//R channel gain
    0x55, 0x80,	//B channel gain
    0x56, 0x80,
    0x57, 0x83,
    0x59, 0x85,

    //0x57, 0x7d,
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// LSC_t    ////////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x8b, 0x10, 	//r2
    0x8c, 0x10, 	//g2
    0x8d, 0x10, 	//b2
    0x8e, 0x10, 	//r4
    0x8f, 0x10, 	//g4
    0x90, 0x10, 	//b4
    0x91, 0x3c, 	//[7]singed4 [6:0]row_cneter
    0x92, 0x50, 	//col_center
    0x5a, 0x53,
    0x5c, 0x4e,
    0x5d, 0x12, 	//decrease 1
    0x5e, 0x1a, 	//decrease 2
    0x5f, 0x24, 	//decrease 3
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// DNDD_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x60, 0x07,	//[4]zero weight mode
    //[3]share mode
    //[2]c weight mode
    //[1]lsc decrease mode
    //[0]b mode
    0x61, 0x15,	//[7:6]na
    //[5:4]c weight adap ratio
    //[3:2]dn lsc ratio
    //[1:0]b ratio
    0x62, 0x08,	//b base
    //0x63,0x02 ,	//b increase RO
    0x64, 0x03,	//[7:4]n base [3:0]c weight
    //0x65    	//[7:4]n increase [3:0]c coeff
    0x66, 0xe4,	//dark_th ,bright_th
    0x67, 0x86,	//flat high, flat low
    0x68, 0x72,	//[7:4]dd limit [1:0]dd ratio

    /////////////////////////////////////////////////////////////////////
    /////////////////////////// asde_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x69, 0x18,	//gain high th
    0x6a, 0x0f,	//[7:4]dn_c slop          //[3]use post_gain [2]use pre_gain [1]use global gain [0]use col gain
    0x6b, 0x00,	//[7:4]dn_b slop [3:0]dn_n slop
    0x6c, 0x5f,	//[7:4]bright_th start [3:0]bright_th slop
    0x6d, 0x8f,	//[7:4]dd_limit_start[3:0]dd_limit slop
    0x6e, 0x55,	//[7:4]ee1 effect start [3:0]slope  broad
    0x6f, 0x38,	//[7:4]ee2 effect start [3:0]slope  narrow
    0x70, 0x15,	//saturation dec slope
    0x71, 0x33,	//[7:4]low limit,[3:0]saturation slope
    /////////////////////////////////////////////////////////////////////
    /////////////////////////// eeintp_t    ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0x72, 0xdc,	//[7]edge_add_mode [6]new edge mode [5]edge2_mode [4]HP_mode
    //[3]lp intp en [2]lp edge en [1:0]lp edge mode

    0x73, 0x80,	//[7]edge_add_mode2 [6]NA [5]only 2direction [4]fixed direction th
    //[3]only defect map [2]intp_map dir [1]HP_acc [0]only edge map

    //for high resolution in light scene
    0x74, 0x00,	//direction th1
    0x75, 0x3f,	//direction th2
    0x76, 0x05,	//direction diff th      h>v+diff ; h>th1 ; v<th2
    0x77, 0x44,	//[7:4]edge1_effect [3:0]edge2_effect
    0x78, 0x88,	//[7:4]edge_pos_ratio  [3:0]edge neg ratio
    0x79, 0x81,	//edge1_max,edge1_min
    0x7a, 0x81,	//edge2_max,edge2_min
    0x7b, 0x22,	//edge1_th,edge2_th
    0x7c, 0xff,	//pos_edge_max,neg_edge_max
    //for high resolution in light scene

    //CCT
    0x93, 0x44,
    0x94, 0x02,
    0x95, 0x06,
    0x96, 0xd8,
    0x97, 0x38,
    0x98, 0xf0,
    //YCPT
    0xb0, 0x40,
    0xb1, 0x3c,	//manual cb
    0xb2, 0x3c,	//manual cr
    0xb3, 0x40,
    0xb6, 0xe0,
    0xbd, 0x3f,
    0xbe, 0x36,	// [5:4]gray mode 00:4&8  01:4&12 10:4&20  11:8$16   [3:0] auto_gray
    0xbf, 0x01,

    //=================

    //AECT
    0xd0, 0xC9,	// exp is gc mode
    0xd1, 0x10,	//every N
    0xd2, 0x90,	// 7 aec enable 5 clore y mode 4skin weight 3 weight drop mode
    0xd3, 0x88,	 //Y_target and low pixel thd high X4 low X2
    0xd5, 0xF2,	//lhig
    0xd6, 0x03,	// ignore mode
    0xdb, 0x92,
    0xdc, 0xA5,	//fast_margin  fast_ratio
    0xdd, 0x32,
    0xdf, 0x23,	// I_fram D_ratio

    0xd9, 0x00,	// colore offset in CAL ,now is too dark so set zero
    0xda, 0x00,	// GB offset
    0xe0, 0x09,


    0xed, 0x04,	//minimum exposure low 8  bits
    0xee, 0x60,	//max_post_dg_gain
    0xef, 0x40,	//max_pre_dg_gain


    //ABBT
    0x80, 0x03,

    0x9F, 0x08,          // m2
    0xA0, 0x0F,
    0xA1, 0x21,
    0xA2, 0x32,
    0xA3, 0x43,
    0xA4, 0x50,
    0xA5, 0x5E,
    0xA6, 0x78,
    0xA7, 0x90,
    0xA8, 0xA6,
    0xA9, 0xB9,
    0xAA, 0xC9,
    0xAB, 0xD6,
    0xAC, 0xE0,
    0xAD, 0xEE,
    0xAE, 0xF8,
    0xAF, 0xFF,

    //wint
    //Y_gamma
    0xc0, 0x00,	//Y_gamma_0
    0xc1, 0x14,	//Y_gamma_1
    0xc2, 0x21,	//Y_gamma_2
    0xc3, 0x36,	//Y_gamma_3
    0xc4, 0x49,	//Y_gamma_4
    0xc5, 0x5B,	//Y_gamma_5
    0xc6, 0x6B,	//Y_gamma_6
    0xc7, 0x7B,	//Y_gamma_7
    0xc8, 0x98,	//Y_gamma_8
    0xc9, 0xB4,	//Y_gamma_9
    0xca, 0xCE,	//Y_gamma_10
    0xcb, 0xE8,	//Y_gamma_11
    0xcc, 0xFF,	//Y_gamma_12
    //ABS
    0xf0, 0x02,
    0xf1, 0x01,
    0xf2, 0x02,	//manual stretch K
    0xf3, 0x30, 	//the limit of Y_stretch
    0xf9, 0x9f,
    0xfa, 0x78,

    /////////////////////////////////////////////////////////////////////
    /////////////////////////// AWB_p     ///////////////////////////////
    /////////////////////////////////////////////////////////////////////
    0xfe, 0x01,
    0x00, 0xf5,		//high_low limit
    0x01, 0x0a,		//y2c center
    0x02, 0x1a,		//y2c
    0x0a, 0xa0,		// number limit
    0x0b, 0x64,		// skip_mode
    0x0c, 0x08,
    0x0e, 0x4c,		// width step
    0x0f, 0x39,		// height step
    0x11, 0x3f,
    0x13, 0x11,		//smooth 2
    0x14, 0x40,		//R_5k_gain_base
    0x15, 0x40,		//B_5k_gain_base
    0x16, 0xc2,		//sinT
    0x17, 0xa8,		//cosT
    0x18, 0x18,		//X1 thd
    0x19, 0x40,		//X2 thd
    0x1a, 0xd0,		//Y1 thd
    0x1b, 0xf5,		//Y2 thd

    0x70, 0x43,		// A R2G low
    0x71, 0x58,		// A R2G high
    0x72, 0x30,		// A B2G low
    0x73, 0x48,		// A B2G high
    0x74, 0x20,		// A G low
    0x75, 0x60,		// A G high



    //HSP
    0x30, 0x03,	//[1]HSP_en [0]sa_curve_en
    0x31, 0x40,
    0x32, 0x10,
    0x33, 0xe0,
    0x34, 0xe0,
    0x35, 0x00,
    0x36, 0x80,
    0x37, 0x00,
    0x38, 0x04,	//sat1, at8
    0x39, 0x09,
    0x3a, 0x12,
    0x3b, 0x1C,
    0x3c, 0x28,
    0x3d, 0x31,
    0x3e, 0x44,
    0x3f, 0x57,
    0x40, 0x6C,
    0x41, 0x81,
    0x42, 0x94,
    0x43, 0xA7,
    0x44, 0xB8,
    0x45, 0xD6,
    0x46, 0xEE,	//sat15,at224
    0x47, 0x0d,	//blue_edge_dec_ratio
    //OUT

    0xfe, 0x00,
    0xff, 0xff
};

const comm_sensor_ini_t CommSensorList[] = {
    {
        //GC0307
        0x00, 0x01, 0x42, 0x43, 0x00, 0x00, 0x99, gc0307init,
    },
    {
        //GC0308
        0x00, 0x01, 0x42, 0x43, 0x00, 0x00, 0x9b, gc0308init,
    },
    {
        //GC0309
        0x00, 0x01, 0x42, 0x43, 0x00, 0x00, 0xa0, gc0309init,
    },
    {
        //bf3603
        0x00, 0x01, 0xdc, 0xdd, 0x00, 0xfc, 0x36, bf3603init,
    },
    {
        //GC0328
        0x00, 0x01, 0x42, 0x43, 0x00, 0xf0, 0x9d, gc0328init,
    },
    {
        //hi708
        0x00, 0x01, 0x60, 0x61, 0x00, 0x04, 0x96, HI708init,
    },
    {
        //Siv100B
        0x00, 0x01, 0x66, 0x67, 0x00, 0x01, 0x0c, SIV100Binit,
    },
};
#endif

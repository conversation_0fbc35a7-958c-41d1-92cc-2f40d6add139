#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "bf20a1.h"
#include "isp_alg.h"
#include "app_config.h"

static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *bf20a1_mipi_get_ae_params();
extern void *bf20a1_mipi_get_awb_params();
extern void *bf20a1_mipi_get_iq_params();
extern void bf20a1_mipi_ae_ev_init(u32 fps);


#define LINE_LENGTH_CLK     640//800//794
#define FRAME_LENGTH        500//500//494
#define FRAME_LENGTH25      600//600//593
#define FRAME_LENGTH20      750//600//593
#define FRAME_LENGTH15      1000//600//593

#define ROW_TIME_NS         66667//66167//66667
#define INPUT_CLK  24
#define PCLK  12

#define LINE_LENGTH_CLK_25FPS     960//960//953
#define LINE_LENGTH_CLK_20FPS     1200//1200//1191
#define LINE_LENGTH_CLK_15FPS     1600//1600//1588
#define ROW_TIME_NS_25FPS         80000//80000//79417
#define ROW_TIME_NS_20FPS         100000//100000//99250
#define ROW_TIME_NS_15FPS         133333//133333//132333

/* #define OUT_33_3FPS */
static u32 uframelen = FRAME_LENGTH;


static u32 cur_again = -1;
static u32 cur_dgain = -1;
static u32 cur_expline = -1;
static u32 cur_line_length_clk = -1;

static u32 line_length_clk = LINE_LENGTH_CLK;

static void *iic = NULL;

#define WRCMD 0x7c
#define RDCMD 0x7d


typedef struct {
    u8 addr;
    u8 value;
} sensor_ini_regs_t;

u8 dType;


const sensor_ini_regs_t bf20a1_INI_REG[] = {
	#if  0
	0xf2, 0x01,//soft rest
	0x5d, 0x00,
	0x50, 0x50,
	0x51, 0x50,
	0x52, 0x50,
	0x53, 0x50,
	0xe0, 0x06,//MIPI CLK 120M
	0xe2, 0xac,
	0xe3, 0xcc,
	0xe5, 0x3b,
	0xe6, 0x04,
	0x73, 0x04,
	0x7a, 0x2b,
	0x7e, 0x10,
	0x07, 0xe1,
	0x08, 0x02,
	0x09, 0x81,
	0x0a, 0x02,
	0x0b, 0xe1,
	0x0c, 0x90,
	0x25, 0x4a,
	0x58, 0x10,
	0x59, 0x10,
	0x5a, 0x10,
	0x5b, 0x10,
	0x5c, 0x98,
	0x5e, 0x78,
	0x5f, 0x49,
	0x2d, 0x02,
	0x4f, 0x00,
	0x10, 0x10,
	0xe4, 0x32,
	0x15, 0x11,
	0x6d, 0x01,
	0x6e, 0x50,
	0x6a, 0x28,
	// 0x6b, 0x03,
	// 0x6c, 0x84,
	0x73, 0x04,
	0x00, (0x10| (1 << 1) | 1),
	0x70, 0x04,
    0x71, 0x01,
    0x72, 0x01	,
    0x73, 0x03,
#if 0 //������ʱ��
	0x7d,0x1f,//0x1e,
#else
	0x7d,0x1e,
#endif
	0x27, 0x80,
	// 0x28, 0xea,
	0xe8, 0x10,
	0x5d, 0xff,
	#else

	//VGA
//�г���794  ֡����494
//Raw 10: XCLK=24M, MCLK=12M
    // 0xf2, 0x01,//soft rest
     0x5d, 0x00, //BLC off
     0x50, 0x50,
     0x51, 0x50,
     0x52, 0x50,
     0x53, 0x50,
     0xe0, 0x06,//MIPI CLK 120M
     0xe2, 0xac,
     0xe3, 0xcc,
     0xe5, 0x3b,
     0xe6, 0x04,
     0x70, 0x02,
     0x71, 0x01,
     0x72, 0x03,
     0x73, 0x04,
     0x74, 0x02,
     0x75, 0x01,
     0x76, 0x05,
     0x77, 0x02,
     0x78, 0x0a,
     0x79, 0x02,
     0x7a, 0x2b,
     0x7e, 0x10,
#if FLIP
     0x00, 0x13, // mirror and flip
#else
     0x00, 0x10, // mirror and flip
#endif
     0x07, 0xe1,
     0x08, 0x02,
     0x09, 0x81,
     0x0a, 0x02,
     0x0b, 0xe1,
     0x0c, 0x90,
     0x25, 0x4a,
     0x58, 0x10,
     0x59, 0x10,
     0x5a, 0x10,
     0x5b, 0x10,
     0x5c, 0x98,
     0x5e, 0x78,
     0x5f, 0x49,
     0x2d, 0x02,
     0x4f, 0x00,
     0x10, 0x10,//bit[0]: 1 black sun enable
     0xe4, 0x32,
     0x15, 0x11,
     0x6d, 0x01,
     0x6e, 0x50,
     0x6a, 0x28,//����Ĵ���
     0x6b, 0x07,//{0x6b,0x6c}:�ع�ʱ��Ĵ���
     0x6c, 0x08,
     0xe8, 0x10,
     0x5d, 0xff, //BLC ON

	#endif

};


unsigned char wr_bf20a1_reg(u8 regID, unsigned char regDat)
{
		u8 ret = 1;

		dev_ioctl(iic, IIC_IOCTL_START, 0);

		if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
			ret = 0;
			goto __wend;
		}
		if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
			ret = 0;
			goto __wend;
		}
		if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
			ret = 0;
			goto __wend;
		}
	__wend:

		dev_ioctl(iic, IIC_IOCTL_STOP, 0);

		return ret;


}

unsigned char rd_bf20a1_reg(u8 regID, unsigned char *regDat)
{
		u8 ret = 1;

		dev_ioctl(iic, IIC_IOCTL_START, 0);
		if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
			ret = 0;
			goto __rend;
		}

		delay(10);

		if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
			ret = 0;
			goto __rend;
		}

		delay(10);

		if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
			ret = 0;
			goto __rend;
		}

		delay(10);

		if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
			ret = 0;
			goto __rend;
		}
	__rend:

		dev_ioctl(iic, IIC_IOCTL_STOP, 0);
		return ret;

}


/*************************************************************************************************
    sensor api
*************************************************************************************************/
static u32 bf20a1_frame_rate = 30;
void bf20a1_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u8 v;

    printf("1245 %dx%d  fps:%d\n", *width, (*height), *frame_freq);
    bf20a1_mipi_set_output_size(width, height, frame_freq);

    for (i = 0; i < sizeof(bf20a1_INI_REG) / sizeof(sensor_ini_regs_t); i++) {
        wr_bf20a1_reg(bf20a1_INI_REG[i].addr, bf20a1_INI_REG[i].value);
    }

	 bf20a1_frame_rate = *frame_freq;
	if (bf20a1_frame_rate == 30) {
        //output format 12M 800X500 30fps
		    wr_bf20a1_reg(0x03, 0x00);
		    wr_bf20a1_reg(0x04, 0x00);
    } else if (bf20a1_frame_rate == 25) {
        //output format 12M 800X500 25fps
		    wr_bf20a1_reg(0x03, 0x64);
		    wr_bf20a1_reg(0x04, 0x00);
    }
    else if (bf20a1_frame_rate == 15) {
        //output format 12M 800X500 15fps
		    wr_bf20a1_reg(0x03, 0xf4);
		    wr_bf20a1_reg(0x04, 0x01);
    }

	bf20a1_mipi_ae_ev_init(*frame_freq);
    cur_again = -1;
    cur_dgain = -1;
    cur_expline = -1;
    cur_line_length_clk = -1;
    return;
}


s32 bf20a1_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 bf20a1_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

s32 bf20a1_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i ;

    for (i = 0; i < 3; i++) { //
        rd_bf20a1_reg(0xfd, &pid);
        rd_bf20a1_reg(0xfd, &ver);
    }

    puts("BF314a Sensor PID \n");
    put_u8hex(pid);
    put_u8hex(ver);
    puts("\n");

    // if (pid != 0x22 && ver != 0x35)
    if (pid != 0xa1 && ver != 0xa1) {
        puts("----not bf20a1_mipi-----\n");
        return -1;
    }
    puts("----hello bf20a1_mipi-----\n");
    return 0;
}

void bf20a1_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }
    gpio_direction_output(pwdn_gpio, 0);
    gpio_direction_output(reset_gpio, 1);
    delay(40000);
    gpio_direction_output(reset_gpio, 0);
    delay(40000);
    gpio_direction_output(reset_gpio, 1);
    delay(40000);
    return;
}


static u8 cur_sensor_type = 0xff;

s32 bf20a1_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{

    puts("\n\n bf20a1_mipi_check \n\n");

    printf("\n isp_dev BF20A1==========%d\n",isp_dev);
    if (!iic) {

        iic = dev_open("iic0", &_hw_iic);
        /* iic = dev_open("swiic0", &_sw_iic); */
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
     printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    bf20a1_mipi_reset(isp_dev);

    if (0 != bf20a1_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

void resetStatic();
s32 bf20a1_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\n bf20a1_mipi_init22 \n\n");

    bf20a1_mipi_config_SENSOR(width, height, format, frame_freq);

    return 0;
}


static void updateforshutter()
{
//    if (val3e01 < 0x05)
//        wrbf20a1Reg(0x3314, 0x12);
//    if (val3e01 > 0x10)
//        wrbf20a1Reg(0x3314, 0x02);
}

#if 0
static u32 Gain[64] = {
    64, 67, 69, 73, 76, 80, 82, 87, 90, 95, 98,
    104, 109, 115, 119, 124, 128, 136, 140, 147, 155,
    166, 173, 183, 189, 198, 202, 212, 219, 231, 237,
    247, 248, 265, 276, 293, 305, 322, 332, 348, 363,
    378, 390, 406, 417, 437, 448, 466, 483, 520, 539,
    574, 597, 630, 649, 682, 707, 744, 765, 801, 824,
    861, 881, 915, //946,1017,1056,1126,1174,1241,1282,
    //1354,1410,1488,1529,1597,1643,1708,1745,1811,
};
#endif // 0
static u32 Gain[64] = {
    64, 68, 72, 76, 80, 84, 88, 92, 96, 100,
    104, 108, 112, 116, 120, 124, 128, 136, 144, 152,
    160, 168, 176, 184, 192, 200, 208, 216, 224, 232,
    240, 248, 256, 272, 288, 304, 320, 336, 352, 368,
    384, 400, 416, 432, 448, 464, 480, 496, 512, 544,
    576, 608, 640, 672, 704, 736, 768, 800, 832, 864,
    896, 928, 960, 992, //946,1017,1056,1126,1174,1241,1282,
    //1354,1410,1488,1529,1597,1643,1708,1745,1811,
};


static void set_again(u32 again)
{
    // auto decrease to 15fps
    #if 0

     int i;
    u32 gain_sensor = 0x00;
    u32 gainMSB=0, gainLSB=0;

    again = again / 64;
	//param : 1x = 16
	if (16*16 < again)
		again = 248;
	if (16 > again)
		again = 16;

	if(8*16 <= again)
	{
		gainMSB = 3;
		gainLSB = again / 8 - 16;
	}
	else if (4*16 <= again)
	{
		gainMSB = 2;
		gainLSB = again / 4 - 16;
	}
	else if (2*16 <= again)
	{
		gainMSB = 1;
		gainLSB = again / 2 - 16;
	}
	else
	{
		gainMSB = 0;
		gainLSB = again - 16;
	}

	if (gainLSB > 15)
		gainLSB = 15;

	gain_sensor = ((gainMSB << 4) + gainLSB ); // gainMSB << 4
    wr_bf20a1_reg(0x6A, gain_sensor);

    #else

      int i;
    u32 gain_sensor = 0x00;

    again = again / 16;

    if (again < 64) {
        again = 64;
    }
    if (again > 992) {
        again = 992;
    }

    // printf("real again:0x%x\n", again);
    for (i = 0; i <= 0x3f ; i++) {
        if ((again >= Gain[i]) && (again < Gain[i + 1])) {
            break;
        }
    }

   // again = Gain[i];
   // gain_sensor = again;
    //wr_bf20a1_reg(0x6A, gain_sensor + 0x10);
     gain_sensor =  i;
    wr_bf20a1_reg(0x6A, gain_sensor);
    // printf("gain_sensor: 0x%x\n", gain_sensor);

    #endif

}

//static void set_dgain(u32 dgain, u32 gain)
static void set_dgain(u32 dgain)
{

    if( cur_dgain == dgain )
    {
        return;
    }
    cur_dgain  = dgain;


}

static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
	#if 0
    if (gain < 1024) {
        gain = 1024;
    }
    if (gain > 62 * 1024) {
        gain = 62 * 1024;
    }

    *_again = gain;
    *_dgain = 0;
     #else
	   u32 ag;
  //  *_again = 0;
  //  *_dgain = gain * 0x10 / (1 << 10);


     *_again = gain;
     *_dgain = 0;

     #endif // 0
}

static void set_shutter(u32 texp)
{
	u8 read0, read1;
//	printf("cur_expline = %x %x\n", cur_expline, texp);
	if( cur_expline == texp )
	{
	    return;
	}
	cur_expline  = texp;
	wr_bf20a1_reg( 0x6b, texp >> 8 );
	wr_bf20a1_reg( 0x6c, texp & 0xff );
	rd_bf20a1_reg(0x6b, &read0);
	rd_bf20a1_reg(0x6c, &read1);
	printf("read = %x %x\n", read0, read1);
}


u32 bf20a1_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;

#if BF20A1_FPS_VARIABLE
    // if (exp_time_us <= 30000) {
    //     line_length_clk = LINE_LENGTH_CLK,
    //     //texp = exp_time_us * 1000 * 2 / ROW_TIME_NS;
    //     texp = exp_time_us * PCLK * 1 / LINE_LENGTH_CLK;
    // } else if (exp_time_us <= 40000) {
    //     line_length_clk = LINE_LENGTH_CLK_25FPS;
    //    // texp = exp_time_us * 1000 * 2 / ROW_TIME_NS_25FPS;
    //     texp = exp_time_us * PCLK * 1 / LINE_LENGTH_CLK_25FPS ;
    // } else if (exp_time_us <= 60000) {
    //     line_length_clk = LINE_LENGTH_CLK_20FPS;
    //    // texp = exp_time_us * 1000 * 2 / ROW_TIME_NS_20FPS;
    //     texp = exp_time_us * PCLK * 1 / LINE_LENGTH_CLK_20FPS;
    //     texp = 1000;
    // } else {
    //     line_length_clk = LINE_LENGTH_CLK_15FPS;
    //    // texp = exp_time_us * 1000 * 2 / ROW_TIME_NS_15FPS;
    //     texp = exp_time_us * PCLK * 1 /LINE_LENGTH_CLK_15FPS;
    // }
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#else

     texp = exp_time_us * PCLK * 1 / LINE_LENGTH_CLK;
     //texp = exp_time_us * 1000  / ROW_TIME_NS;
#endif // BF20A1_FPS_VARIABLE
    if (texp < 1) {
        texp = 1;
    }
    // if (texp > FRAME_LENGTH * 1 - 4) {
    //     texp = FRAME_LENGTH * 1 - 4;
    // }
    texp_align = (texp) * LINE_LENGTH_CLK / (PCLK * 1);
    //texp_align = (texp) * ROW_TIME_NS / (1000 * 2);

    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << 10) / texp_align;
        //printf("ratio = %d\n",ratio);
    } else {
        ratio = (1 << 10);
    }

    shutter->ae_exp_line =  texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;

//    printf(" \n  0exp_time_us=%d, texp=%d, gain=%d->%d\n", exp_time_us, texp, gain,shutter->ae_gain);
    return 0;

}

u32 bf20a1_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
	#if 0
    /* static int ffff = 0, */
    /* static u8 aaaa = 0, */
    static u32 lastdgain = -1;
    static u32 lastexline = -1;
    u32 again, dgain;

    calc_gain((shutter->ae_gain), &again, &dgain);


    set_again(again);
    set_dgain(dgain, shutter->ae_gain / 1024);
    //set_dgain(dgain);
    set_shutter(shutter->ae_exp_line);

    if ((lastdgain != dgain)) { //||(lastexline!=shutter->ae_exp_line))
//        updateforgain(shutter->ae_gain/1024);
//        wr_bf20a1_reg(0x3812,0x30);

    lastdgain = dgain;
    lastexline = shutter->ae_exp_line;
    return 0;
    #else

    u32 again, dgain;
    calc_gain((shutter->ae_gain), &again, &dgain);

    set_again(again);
    set_dgain(dgain);

    set_shutter(shutter->ae_exp_line);

    // printf("again:%d dgain:%d itime:%d\n", (int)again, (int)dgain, (int)shutter->ae_exp_line);

    return 0;
    #endif // 0

}

void bf20a1_mipi_sleep()
{


}

void bf20a1_mipi_wakeup()
{


}

void bf20a1_mipi_wr_reg(u16 addr, u16 val)
{
    printf("update reg%x with %x\n", addr, val);
    wr_bf20a1_reg((u8)addr, (u8)val);
}
u16 bf20a1_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_bf20a1_reg((u8)addr, &val);
    return val;
}

REGISTER_CAMERA(bf20a1_mipi) = {
    .logo 				= 	"bf20a1M",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			= 	SEN_IN_FORMAT_BGGR,
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_CSI2,
   // .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_PCLK_SAMPLE_FALLING,
 .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
#ifdef OUT_33_3FPS
    .fps         		= 	30,
    .real_fps           = (u32)(33.333333 * 65536);
#else
    /* .fps          		= 	30, */
    .fps         		= 	25, // 25 // 15
#endif

    .sen_size 			= 	{BF20A1_MIPI_OUTPUT_W, BF20A1_MIPI_OUTPUT_H},
    .isp_size 			= 	{BF20A1_MIPI_OUTPUT_W, BF20A1_MIPI_OUTPUT_H},
    /* .isp_size 			= 	{1024, 576}, */

#ifdef OUT_33_3FPS
    .cap_fps         		= 	30,
#else
    /* .cap_fps         		= 	30, */
    .cap_fps         		= 	25, // 25 //15
#endif
    .sen_cap_size 			= 	{BF20A1_MIPI_OUTPUT_W, BF20A1_MIPI_OUTPUT_H},
    .isp_cap_size 			= 	{BF20A1_MIPI_OUTPUT_W, BF20A1_MIPI_OUTPUT_H},

    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	bf20a1_mipi_check,
        .init 		        = 	bf20a1_mipi_init,
        .set_size_fps 		=	bf20a1_mipi_set_output_size,
        .power_ctrl         =   bf20a1_mipi_power_ctl,

        .get_ae_params  	=	bf20a1_mipi_get_ae_params,
        .get_awb_params 	=	bf20a1_mipi_get_awb_params,
        .get_iq_params 	    	=	bf20a1_mipi_get_iq_params,

        .sleep 		        =	bf20a1_mipi_sleep,
        .wakeup 		    =	bf20a1_mipi_wakeup,
        .write_reg 		    =	bf20a1_mipi_wr_reg,
        .read_reg 		    =	bf20a1_mipi_rd_reg,

    }
};




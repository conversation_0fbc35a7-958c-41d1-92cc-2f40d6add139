#ifndef __BF20A1_MIPI_H__
#define __BF20A1_MIPI_H__

#include "typedef.h"

#define BF20A1_MIPI_OUTPUT_W    640
#define BF20A1_MIPI_OUTPUT_H    480

#define BF20A1_FPS_VARIABLE    1


s32 bf20a1_mipi_set_output_size(u16 *width, u16 *height, u8 *freq);
s32 bf20a1_mipi_power_ctl(u8 isp_dev, u8 is_work);

//s32 GC1004_check(u8 isp_dev);
s32 bf20a1_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);


void bf20a1_mipi_sleep();
void bf20a1_mipi_wakeup();
void bf20a1_mipi_W_Reg(u16 addr, u16 val);
u16 bf20a1_mipi_R_Reg(u16 addr);


#endif




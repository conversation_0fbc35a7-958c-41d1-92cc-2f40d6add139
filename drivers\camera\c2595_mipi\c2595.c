#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "c2595.h"
#include "isp_alg.h"
#include "app_config.h"
#include "delay.h"

// 硬件参数定义
#define LINE_LENGTH_CLK       1600    // 行长度时钟数（HTS）, 影响行时间
#define FRAME_LENGTH          1200    // 帧长度行数（VTS）, 影响帧率
#define ROW_TIME_NS           66667   // 单行时间（纳秒）, 用于时序计算
#define INPUT_CLK             24      // 输入时钟频率（MHz）
#define PCLK                  90      // 像素时钟频率（MHz）

// I2C通信定义
#define WRCMD        0x6c    // I2C写地址
#define RDCMD        0x6d    // I2C读地址

// 传感器ID定义
#define device_id_h      0x0000  // 芯片ID高字节寄存器地址
#define device_id_l       0x0001  // 芯片ID低字节寄存器地址
#define device_value_h      0x02    // 芯片ID高字节期望值
#define device_value_l       0x09    // 芯片ID低字节期望值

// 寄存器地址定义
#define C2595_EXPOSURE_HIGH_REG     0x0202  // 曝光时间高字节寄存器
#define C2595_EXPOSURE_LOW_REG      0x0203  // 曝光时间低字节寄存器
#define C2595_DGAIN_HIGH_REG        0x0216  // 数字增益高字节寄存器
#define C2595_DGAIN_LOW_REG         0x0217  // 数字增益低字节寄存器
#define C2595_VTS_HIGH_REG          0x0340  // 垂直总行数高字节寄存器
#define C2595_VTS_LOW_REG           0x0341  // 垂直总行数低字节寄存器
#define C2595_HTS_HIGH_REG          0x0342  // 水平总像素数高字节寄存器
#define C2595_HTS_LOW_REG           0x0343  // 水平总像素数低字节寄存器
#define C2595_STREAM_CTRL_REG       0x0100  // 流控制寄存器
#define C2595_STREAM_ON             0x01    // 开启视频流输出
#define C2595_STREAM_OFF            0x00    // 关闭视频流输出

// 模拟增益寄存器组
#define C2595_AGAIN_REG_32A9        0x32A9  // 模拟增益寄存器1
#define C2595_AGAIN_REG_32AC        0x32AC  // 模拟增益寄存器2
#define C2595_AGAIN_REG_32AD        0x32AD  // 模拟增益寄存器3
#define C2595_AGAIN_REG_3211        0x3211  // 模拟增益寄存器4
#define C2595_AGAIN_REG_3216        0x3216  // 模拟增益寄存器5
#define C2595_AGAIN_REG_3217        0x3217  // 模拟增益寄存器6

// 参数限制定义
#define C2595_MIN_EXPOSURE_LINES    150     // 最小曝光行数限制
#define C2595_VTS_MARGIN            8       // VTS安全边距（行数）
#define C2595_MAX_VTS               5000    // 最大VTS值限制
#define C2595_AGAIN_MIN             1024    // 最小模拟增益值（1x）
#define C2595_AGAIN_MAX             8192    // 最大模拟增益值（8x）
#define C2595_DGAIN_MIN             1       // 最小数字增益值
#define C2595_DGAIN_MAX             0xB54   // 最大数字增益值（12位）
#define RATIO_SHIFT_BITS            10      // 比例计算位移位数
#define GAIN_CONVERT_BASE           100     // 增益转换基数
#define GAIN_CONVERT_FACTOR         1024    // 增益转换系数
#define C2595_I2C_DELAY_US          10      // I2C通信延时（微秒）
#define C2595_FPS_30                30      // 30fps帧率标识
#define C2595_FPS_15                15      // 15fps帧率标识
// 时序参数定义
#define C2595_VTS_30FPS             1232    // 30fps对应的VTS值
#define C2595_VTS_15FPS             2464    // 15fps对应的VTS值
#define C2595_HTS_DEFAULT           2432    // 默认HTS值

#define INVALID_GPIO_PIN            -1  // 无效GPIO标识

// 调试开关
#define DEBUG_AE 0

// 全局变量定义
static u32 reset_gpios[2] = {INVALID_GPIO_PIN, INVALID_GPIO_PIN};    // 复位GPIO数组
static u32 pwdn_gpios[2] = {INVALID_GPIO_PIN, INVALID_GPIO_PIN};     // 掉电GPIO数组
static u32 cur_again = INVALID_GPIO_PIN;                             // 当前模拟增益缓存
static u32 cur_dgain = INVALID_GPIO_PIN;                             // 当前数字增益缓存
static u32 cur_expline = INVALID_GPIO_PIN;                           // 当前曝光行数缓存
static u32 cur_line_length_clk = INVALID_GPIO_PIN;                   // 当前行长度时钟缓存
static u32 cur_vts = 2464;                                           // 当前VTS值（初始15fps）
static u32 line_length_clk = LINE_LENGTH_CLK;                  // 行长度时钟值
static void *iic = NULL;  // I2C设备句柄

// 外部函数声明
extern void *c2595_mipi_get_ae_params();    // 获取AE参数表
extern void *c2595_mipi_get_awb_params();   // 获取AWB参数表
extern void *c2595_mipi_get_iq_params();    // 获取IQ参数表
extern void c2595_mipi_ae_ev_init(u32 fps); // 初始化AE EV表

// 内部函数声明
static void set_again(u32 again);    // 设置模拟增益
static void set_dgain(u32 dgain);    // 设置数字增益
static void set_shutter(u32 texp);   // 设置曝光时间
static void set_vts(u32 vts);        // 设置VTS

// 1280x720分辨率寄存器配置表
static const sensor_reg_t c2595_1280x720[] = {
    {0x3288, 0x50},
    {0x0400, 0x41},
    {0x0401, 0xa5},
    {0x0403, 0x36},
    {0x3885, 0x22},
    {0x3280, 0x28},
    {0x3284, 0xA3},
    {0x3288, 0x40},
    {0x328C, 0x48},
    {0x32aa, 0x05},
    {0x32ab, 0x08},
    {0x3C00, 0x43},
    {0x3C01, 0x03},
    {0x3218, 0x28},
    {0x3805, 0x08},
    {0x3808, 0x16},
    {0x3809, 0x96},
    {0x380a, 0x7d},
    {0x380b, 0xeb},
    {0x380e, 0x0d},
    {0x380c, 0x01},
    {0x0202, 0x04},
    {0x0203, 0x56},
    {0x3108, 0xcf},
    {0x3115, 0x30},
    {0x328b, 0xa9},
    {0x3295, 0x15},
    {0x328d, 0x09}, // 0a
    {0x3293, 0x01},
    {0x32a9, 0xFF},
    {0x3290, 0xb3},
    {0x32ad, 0xFF},
    {0x3216, 0xff},
    {0x3217, 0xff}, // update 0827
    {0x3298, 0x48}, // remove the VFPN at some special again
    {0x3211, 0xFF},
    {0x32af, 0x80}, // connect HVDD to 3287
    {0x3212, 0x4A}, // changed based on tuning results 201111
    {0x3286, 0x05}, // remove H line
    {0x3287, 0x46}, // improve H line
    {0x3881, 0x00}, // FIFO
    {0x0400, 0x47},
    {0x0404, 0x08},
    {0x0405, 0x80},
    {0x0406, 0x02},
    {0x0407, 0x80},
    {0x3403, 0x02},
    {0x3407, 0x07},
    {0x3411, 0x00},
    {0x3412, 0x01},
    {0x3415, 0x01},
    {0x3416, 0x01},
    {0x3500, 0x10},
    {0x3584, 0x02},
    {0xe000, 0x31},
    {0xe001, 0x08},
    {0xe002, 0x4f},
    {0xe00c, 0x31},
    {0xe00d, 0x08},
    {0xe00e, 0xef},
    {0xe018, 0x32},
    {0xe019, 0x93},
    {0xe01a, 0x03},
    {0xe01b, 0x32},
    {0xe01c, 0xa9},
    {0xe01d, 0x10},
    {0xe01e, 0x32},
    {0xe01f, 0xac},
    {0xe020, 0xff},
    {0x0216, 0x03},
    {0x0217, 0xE8},
    {0xe021, 0x32},
    {0xe022, 0x90},
    {0xe023, 0xb6},
    {0xe024, 0x32},
    {0xe025, 0xad},
    {0xe026, 0x9f},
    {0xe027, 0x32},
#if FLIP
    {0x0101, 0x03}, // 上下 左右 都翻转
#else
    {0x0101, 0x02}, // 左右 翻转 hflip
#endif
    {0xe028, 0x11},
    {0xe029, 0x10},
    {0xe02a, 0x32},
    {0xe02b, 0x16},
    {0xe02c, 0x2f},
    {0x3500, 0x00},
    {0x3584, 0x22},
    {0x034c, 0x05},
    {0x034d, 0x00},
    {0x034e, 0x02},
    {0x034f, 0xd0},
    {0x3008, 0x00},
    {0x3009, 0xa8},
    {0x300a, 0x00},
    {0x300b, 0xf8},
    {0x3293, 0x03},
    {0x32a9, 0x3f},
    {0x32ac, 0xFF},
    {0x3290, 0xb6},
    {0x0100, 0x01},
};

// 写入C2595 8位地址寄存器
unsigned char wr_c2595_reg_8bit(u8 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 16位地址寄存器写入函数
unsigned char wr_c2595_reg(u16 regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID & 0xff)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;
}

// 8位地址寄存器读取函数
unsigned char rd_c2595_reg_8bit(u8 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(C2595_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
        ret = 0;
        goto __rend;
    }

    delay(C2595_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(C2595_I2C_DELAY_US);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

// 16位地址寄存器读取函数
unsigned char rd_c2595_reg(u16 regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID >> 8)) {
        ret = 0;
        goto __rend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID & 0xff)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

// sensor api
static u32 c2595_frame_rate = C2595_FPS_30;  // 当前帧率设置
void c2595_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;

    c2595_mipi_set_output_size(width, height, frame_freq);

    // 写入基础寄存器配置表（1280x720分辨率配置）
    for (i = 0; i < sizeof(c2595_1280x720) / sizeof(sensor_reg_t); i++) {
        wr_c2595_reg(c2595_1280x720[i].addr, c2595_1280x720[i].val);
    }

    // 根据帧率设置VTS和HTS时序参数
    c2595_frame_rate = *frame_freq;
    if (c2595_frame_rate == C2595_FPS_30) {
        // 30fps时序配置
        wr_c2595_reg(C2595_VTS_HIGH_REG, (C2595_VTS_30FPS >> 8) & 0xFF);
        wr_c2595_reg(C2595_VTS_LOW_REG, C2595_VTS_30FPS & 0xFF);
        wr_c2595_reg(C2595_HTS_HIGH_REG, (C2595_HTS_DEFAULT >> 8) & 0xFF);
        wr_c2595_reg(C2595_HTS_LOW_REG, C2595_HTS_DEFAULT & 0xFF);
    } else if (c2595_frame_rate == C2595_FPS_15) {
        // 15fps时序配置
        wr_c2595_reg(C2595_VTS_HIGH_REG, (C2595_VTS_15FPS >> 8) & 0xFF);
        wr_c2595_reg(C2595_VTS_LOW_REG, C2595_VTS_15FPS & 0xFF);
        wr_c2595_reg(C2595_HTS_HIGH_REG, (C2595_HTS_DEFAULT >> 8) & 0xFF);
        wr_c2595_reg(C2595_HTS_LOW_REG, C2595_HTS_DEFAULT & 0xFF);
    }

    // 初始化AE参数表
    c2595_mipi_ae_ev_init(*frame_freq);

    // 重置当前参数缓存
    cur_again = INVALID_GPIO_PIN;
    cur_dgain = INVALID_GPIO_PIN;
    cur_expline = INVALID_GPIO_PIN;
    cur_line_length_clk = INVALID_GPIO_PIN;

    return;
}

// 设置输出尺寸
s32 c2595_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}

// 电源控制
s32 c2595_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

// 检查C2595传感器ID
s32 c2595_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i;

    wr_c2595_reg(device_id_h, 0x00);
    for (i = 0; i < 3; i++) {
        rd_c2595_reg(device_id_h, &pid);
        rd_c2595_reg(device_id_l, &ver);
    }

    printf("C2595 Sensor PID \n");
    put_u8hex(pid);
    put_u8hex(ver);
    printf("\n");

    if (pid != device_value_h || ver != device_value_l) {
        printf("----not C2595 sensor-----\n");
        return -1;
    }
    printf("----hello C2595 sensor-----\n");
    return 0;
}

// C2595传感器复位
void c2595_mipi_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

    gpio_direction_output(reset_gpio, 0);
    delay_ms(1);
    gpio_direction_output(reset_gpio, 1);
    gpio_direction_output(pwdn_gpio, 0);
    delay_ms(1);
}

static u8 cur_sensor_type = 0xFF;  // 当前传感器类型
// 检查C2595传感器
s32 c2595_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{
    if (!iic) {
        iic = dev_open("iic0", &_hw_iic);
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    c2595_mipi_reset(isp_dev);

    if (0 != c2595_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}

// C2595传感器初始化
void resetStatic();
s32 c2595_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    c2595_mipi_config_SENSOR(width, height, format, frame_freq);

    wr_c2595_reg(C2595_STREAM_CTRL_REG, C2595_STREAM_ON);

    return 0;
}

// 模拟增益配置表
static const u16 c2595_again_thresholds[] = {  // 模拟增益阈值表（1024=1x增益）
    1024, 1088, 1152, 1216, 1280, 1344, 1408, 1472,
    1536, 1600, 1664, 1728, 1792, 1856, 1920, 1984,
    2048, 2176, 2304, 2432, 2560, 2688, 2816, 2944,
    3072, 3200, 3328, 3456, 3584, 3712, 3840, 3968,
    4096, 4352, 4608, 4864, 5120, 5376, 5632, 5888,
    6144, 6400, 6656, 6912, 7168, 7424, 7680, 7936,
    8192
};

// 模拟增益寄存器配置表（对应6个寄存器的值）
static const uint8_t c2595_again_reg_table[49][6] = {
    {0x50, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x51, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x52, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x53, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x54, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x55, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x56, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x57, 0x4c, 0x9f, 0x15, 0x1a, 0x12},
    {0x58, 0x4c, 0xaf, 0x15, 0x1a, 0x12},
    {0x59, 0x4c, 0xaf, 0x15, 0x1a, 0x12},
    {0x5a, 0x4c, 0xaf, 0x15, 0x1a, 0x12},
    {0x5b, 0x4e, 0xaf, 0x15, 0x1a, 0x12},
    {0x5c, 0x4d, 0xaf, 0x15, 0x1a, 0x12},
    {0x5d, 0x4d, 0xaf, 0x15, 0x1a, 0x12},
    {0x5e, 0x4d, 0xaf, 0x15, 0x1a, 0x12},
    {0x5f, 0x4c, 0xaf, 0x15, 0x1a, 0x12},
    {0x60, 0x4e, 0xaf, 0x15, 0x1a, 0x12},
    {0x62, 0x4e, 0xaf, 0x15, 0x1a, 0x12},
    {0x64, 0x4e, 0xaf, 0x15, 0x1a, 0x12},
    {0x66, 0x4e, 0xaf, 0x15, 0x1a, 0x12},
    {0x68, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x6a, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x6c, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x6e, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x70, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x72, 0x3e, 0xaf, 0x15, 0x1a, 0x12},
    {0x74, 0x3d, 0xaf, 0x15, 0x1a, 0x12},
    {0x76, 0x3d, 0xaf, 0x15, 0x1a, 0x12},
    {0x78, 0x2d, 0xbf, 0x15, 0x1a, 0x12},
    {0x7a, 0x2d, 0xbf, 0x15, 0x1a, 0x12},
    {0x7c, 0x2c, 0xb0, 0x15, 0x1a, 0x12},
    {0x7e, 0x2d, 0xb0, 0x15, 0x1a, 0x12},
    {0x20, 0x4d, 0xb0, 0x15, 0x1a, 0x12},
    {0x22, 0x4d, 0xb0, 0x15, 0x1a, 0x12},
    {0x24, 0x4d, 0xb0, 0x15, 0x1a, 0x12},
    {0x26, 0x4d, 0xb0, 0x15, 0x1a, 0x12},
    {0x28, 0x3c, 0xb0, 0x15, 0x1a, 0x12},
    {0x2a, 0x3d, 0xb0, 0x15, 0x1a, 0x12},
    {0x2c, 0x3c, 0xb0, 0x15, 0x1a, 0x12},
    {0x2e, 0x3d, 0xb0, 0x15, 0x1a, 0x12},
    {0x30, 0x3d, 0xb0, 0x15, 0x1a, 0x12},
    {0x32, 0x3d, 0xb0, 0x15, 0x1a, 0x12},
    {0x34, 0x3e, 0xb0, 0x15, 0x1a, 0x12},
    {0x36, 0x3e, 0xb0, 0x15, 0x1a, 0x12},
    {0x38, 0x2e, 0xb0, 0x15, 0x1a, 0x12},
    {0x3a, 0x2e, 0xb0, 0x15, 0x1a, 0x12},
    {0x3c, 0x2d, 0xb0, 0x15, 0x1a, 0x12},
    {0x3e, 0x2d, 0xb0, 0x15, 0x1a, 0x12},
    {0x3f, 0x2f, 0xb0, 0x15, 0x1a, 0x12},
};

// 设置模拟增益
static void set_again(u32 gain)
{
    if (cur_again == gain) {
        return;
    }

    cur_again = gain;

    u8 index;

    // 边界值处理
    if (gain <= C2595_AGAIN_MIN) {
        index = 0;
    } else if (gain >= C2595_AGAIN_MAX) {
        index = 48;
    } else {
        // 二分查找优化：先粗略定位区间
        u8 left, right;
        if (gain < 4608) {
            left = 0;
            right = 32;
        } else {
            left = 32;
            right = 48;
        }

        // 进一步缩小查找范围
        u8 mid = (left + right) >> 1;
        if (gain < c2595_again_thresholds[mid]) {
            right = mid;
        } else {
            left = mid;
        }

        // 线性查找最终匹配的增益档位
        index = left;
        while (index < right && gain >= c2595_again_thresholds[index + 1]) {
            index++;
        }
    }

    // 写入6个模拟增益寄存器
    wr_c2595_reg(C2595_AGAIN_REG_32A9, c2595_again_reg_table[index][0]);
    wr_c2595_reg(C2595_AGAIN_REG_32AC, c2595_again_reg_table[index][1]);
    wr_c2595_reg(C2595_AGAIN_REG_32AD, c2595_again_reg_table[index][2]);
    wr_c2595_reg(C2595_AGAIN_REG_3211, c2595_again_reg_table[index][3]);
    wr_c2595_reg(C2595_AGAIN_REG_3216, c2595_again_reg_table[index][4]);
    wr_c2595_reg(C2595_AGAIN_REG_3217, c2595_again_reg_table[index][5]);
}

// 设置数字增益
static void set_dgain(u32 dgain)
{
    if (cur_dgain == dgain) {
        return;
    }

    cur_dgain = dgain;

    // 数字增益范围限制（256-4095, 对应1x-16x）
    // if (dgain < C2595_DGAIN_MIN) {
    //     dgain = C2595_DGAIN_MIN;
    // }

    if (dgain > C2595_DGAIN_MAX) {
        dgain = C2595_DGAIN_MAX;
    }

    // 12位数字增益分高低字节写入（高4位+低8位）
    u8 high = (dgain >> 8) & 0x0F;
    u8 low  = dgain & 0xFF;

    wr_c2595_reg(C2595_DGAIN_HIGH_REG, high);
    wr_c2595_reg(C2595_DGAIN_LOW_REG, low);
}

// 计算模拟增益和数字增益分配
static void calc_gain(u32 gain, u32 *_again, u32 *_dgain)
{
    // ISP增益值转换为传感器增益值
    gain = gain * GAIN_CONVERT_FACTOR / GAIN_CONVERT_BASE;

    // 优先使用模拟增益（噪声更低）
    if (gain <= C2595_AGAIN_MAX) {
        *_again = gain;
        *_dgain = GAIN_CONVERT_BASE;  // 数字增益保持基准值
    } else {
        // 模拟增益达到最大值后, 剩余增益分配给数字增益
        *_again = C2595_AGAIN_MAX;
        *_dgain = gain * GAIN_CONVERT_BASE / C2595_AGAIN_MAX;
    }
}

// 设置VTS（垂直总行数/帧长）
static void set_vts(u32 vts)
{
    if (cur_vts == vts) {
        return;
    }

    // VTS范围限制（影响帧率：fps = PCLK*1000000/(HTS*VTS)）
    if (vts > C2595_MAX_VTS) {
        vts = C2595_MAX_VTS;
    }

    cur_vts = vts;

    // VTS寄存器16位分高低字节写入
    wr_c2595_reg(C2595_VTS_HIGH_REG, (vts >> 8) & 0xFF);
    wr_c2595_reg(C2595_VTS_LOW_REG, vts & 0xFF);
}

// 设置曝光时间
static void set_shutter(u32 texp)
{
    if (cur_expline == texp) {
        return;
    }

    cur_expline = texp;

    // 曝光时间寄存器16位分高低字节写入
    wr_c2595_reg(C2595_EXPOSURE_HIGH_REG, (texp >> 8) & 0xFF);
    wr_c2595_reg(C2595_EXPOSURE_LOW_REG, texp & 0xFF);

    // VTS控制逻辑
    u32 new_vts = texp + C2595_VTS_MARGIN;
    // set_vts(new_vts);
    if (new_vts <= 1360) {
        set_vts(1360);
    } else {
        if (new_vts >= 5200)
        {
            new_vts = 5200;
        }
        set_vts(new_vts);
    }
}

// 计算曝光参数
u32 c2595_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp;
    u32 texp_align;
    u32 ratio;

    // 将微秒曝光时间转换为行数（基于像素时钟和行长度）
#if C2595_FPS_VARIABLE
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#else
    texp = exp_time_us * PCLK / LINE_LENGTH_CLK;
#endif

    // 最小曝光行数限制
    if (texp < C2595_MIN_EXPOSURE_LINES) {
        texp = C2595_MIN_EXPOSURE_LINES;
    }

    // 计算实际对应的曝光时间（用于增益补偿）
    texp_align = texp * LINE_LENGTH_CLK / PCLK;

    // 如果实际曝光时间不足, 通过增益补偿
    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << RATIO_SHIFT_BITS) / texp_align;
    } else {
        ratio = (1 << RATIO_SHIFT_BITS);  // 无需补偿
    }

    // 填充ISP曝光参数结构体
    shutter->ae_exp_line = texp;
    shutter->ae_gain = (gain * ratio) >> RATIO_SHIFT_BITS;
    shutter->ae_exp_clk = 0;

    return 0;
}

// 设置传感器曝光参数
u32 c2595_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    u32 again, dgain;

    // 分解总增益为模拟增益和数字增益
    calc_gain(shutter->ae_gain, &again, &dgain);

    // 应用增益和曝光参数到传感器
    set_again(again);
    set_dgain(dgain);
    set_shutter(shutter->ae_exp_line);

#if DEBUG_AE
    // 计算实际曝光时间（用于调试显示）
    u32 exp_time_us = (shutter->ae_exp_line * LINE_LENGTH_CLK) / PCLK;

    // 寄存器回读验证
    u8 again_regs[6], dgain_high, dgain_low, itime_high, itime_low;
    rd_c2595_reg(C2595_AGAIN_REG_32A9, &again_regs[0]);
    rd_c2595_reg(C2595_AGAIN_REG_32AC, &again_regs[1]);
    rd_c2595_reg(C2595_AGAIN_REG_32AD, &again_regs[2]);
    rd_c2595_reg(C2595_AGAIN_REG_3211, &again_regs[3]);
    rd_c2595_reg(C2595_AGAIN_REG_3216, &again_regs[4]);
    rd_c2595_reg(C2595_AGAIN_REG_3217, &again_regs[5]);
    rd_c2595_reg(C2595_DGAIN_HIGH_REG, &dgain_high);
    rd_c2595_reg(C2595_DGAIN_LOW_REG, &dgain_low);
    rd_c2595_reg(C2595_EXPOSURE_HIGH_REG, &itime_high);
    rd_c2595_reg(C2595_EXPOSURE_LOW_REG, &itime_low);

    u16 dgain_readback = ((dgain_high & 0x0F) << 8) | dgain_low;
    u16 itime_readback = (itime_high << 8) | itime_low;

    // AE参数总览输出
    // printf("AE: gain=%d again=%d dgain=%d exp_lines=%d exp_time_us=%d\n",
    //            shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us);
    // 寄存器验证详细信息
    printf("AE: gain=%d again=%d dgain=%d exp_lines=%d exp_time_us=%d \nread: again[%02X,%02X,%02X,%02X,%02X,%02X=%d] dgain[%02X,%02X=%d] itime[%02X,%02X=%d]\n\n",
               shutter->ae_gain, again, dgain, shutter->ae_exp_line, exp_time_us,
               again_regs[0], again_regs[1], again_regs[2], again_regs[3], again_regs[4], again_regs[5], again,
               dgain_high, dgain_low, dgain_readback, itime_high, itime_low, itime_readback);
#endif
    return 0;
}

// 传感器休眠（关闭视频流）
void c2595_mipi_sleep()
{
}

// 传感器唤醒（开启视频流）
void c2595_mipi_wakeup()
{
}

// 写寄存器接口（外部调用）
void c2595_mipi_wr_reg(u16 addr, u16 val)
{
    wr_c2595_reg(addr, (u8)val);
}

// 读寄存器接口（外部调用）
u16 c2595_mipi_rd_reg(u16 addr)
{
    u8 val;
    rd_c2595_reg(addr, &val);
    return val;
}

// 传感器设备注册
REGISTER_CAMERA(c2595_mipi) = {
    .logo               = "C2595",
    .isp_dev            = ISP_DEV_NONE,
#if FLIP
    .in_format          = SEN_IN_FORMAT_RGGB,
#else
    .in_format          = SEN_IN_FORMAT_GRBG,
#endif
    .out_format         = ISP_OUT_FORMAT_RAW,
    .mbus_type          = SEN_MBUS_CSI2,
    .mbus_config        = SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
#ifdef OUT_33_3FPS
    .fps                = 30,
    .real_fps           = (u32)(33.333333 * 65536),
#else
    .fps                = 15, // 30 // 15
#endif

    .sen_size           = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},
    .isp_size           = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},

#ifdef OUT_33_3FPS
    .cap_fps            = 30,
#else
    .cap_fps            = 15, // 30 // 15
#endif
    .sen_cap_size       = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},
    .isp_cap_size       = {C2595_MIPI_OUTPUT_W, C2595_MIPI_OUTPUT_H},

    .ops                = {
        .avin_fps           = NULL,
        .avin_valid_signal  = NULL,
        .avin_mode_det      = NULL,
        .sensor_check       = c2595_mipi_check,
        .init               = c2595_mipi_init,
        .set_size_fps       = c2595_mipi_set_output_size,
        .power_ctrl         = c2595_mipi_power_ctl,

        .get_ae_params      = c2595_mipi_get_ae_params,
        .get_awb_params     = c2595_mipi_get_awb_params,
        .get_iq_params      = c2595_mipi_get_iq_params,

        .sleep              = c2595_mipi_sleep,
        .wakeup             = c2595_mipi_wakeup,
        .write_reg          = c2595_mipi_wr_reg,
        .read_reg           = c2595_mipi_rd_reg,
    }
};
#ifndef __C2595_MIPI_H__
#define __C2595_MIPI_H__

#include "typedef.h"

#define C2595_MIPI_OUTPUT_W    1280
#define C2595_MIPI_OUTPUT_H    720

#define C2595_FPS_VARIABLE    0


s32 c2595_mipi_set_output_size(u16 *width, u16 *height, u8 *freq);
s32 c2595_mipi_power_ctl(u8 isp_dev, u8 is_work);

s32 c2595_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);


void c2595_mipi_sleep();
void c2595_mipi_wakeup();
void c2595_mipi_wr_reg(u16 addr, u16 val);
u16 c2595_mipi_rd_reg(u16 addr);


#endif 
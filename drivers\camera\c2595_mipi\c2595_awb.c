#include "isp_alg.h"
#include "c2595_awb.h"

/**
 * @brief 自动白平衡自定义权重数组
 * 
 * 8x8的权重矩阵，用于定义图像不同区域在AWB计算中的重要性。
 * 所有值设为1表示各区域权重相等，ISP会平均考虑整个图像的白平衡信息。
 * 可以通过调整不同位置的权重值来强调或忽略特定区域的颜色信息。
 */
static u8 awb_weight_cust[] = {
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1,
    1, 1, 1, 1, 1, 1, 1, 1
};

/**
 * @brief AWB灰度世界算法第一组参数配置
 * 
 * 灰度世界算法基于假设：在一个有着大量色彩变化的图像里，
 * R、G、B三个分量的平均值会趋于同一个灰度值Gray。
 * 
 * @param wp_th 白点阈值，用于判断像素是否为白点的亮度门限
 * @param r_th R通道阈值，红色分量的判断门限
 * @param g_th G通道阈值，绿色分量的判断门限  
 * @param b_th B通道阈值，蓝色分量的判断门限
 * @param wp_ratio_th_numerator 白点比例阈值分子
 * @param wp_ratio_th_denominator 白点比例阈值分母
 * @param rgain_min R增益最小值(256对应1.0倍增益)
 * @param rgain_max R增益最大值(3072对应12倍增益)
 * @param bgain_min B增益最小值
 * @param bgain_max B增益最大值
 * @param ev_th 曝光值阈值，用于判断场景亮度
 */
static isp_awb_gw1_params_t awb_gw1_params = {
    .wp_th = 50,
    .r_th = 128, // 235
    .g_th = 128, // 235
    .b_th = 128, // 235

    .wp_ratio_th_numerator = 1,
    .wp_ratio_th_denominator = 32,

    .rgain_min = 256,
    .rgain_max = 3072,
    .bgain_min = 256,
    .bgain_max = 3072,

    .ev_th = 2048,
};

/**
 * @brief AWB灰度世界算法第二组参数配置
 * 
 * 用于AWB算法的时间域滤波和增益限制。
 * 
 * @param prev_w 前一帧权重，用于时间域滤波的历史帧权重
 * @param new_w 当前帧权重，用于时间域滤波的当前帧权重
 * @param rgain_min R增益最小值限制
 * @param rgain_max R增益最大值限制
 * @param bgain_min B增益最小值限制
 * @param bgain_max B增益最大值限制
 */
static isp_awb_gw2_params_t awb_gw2_params = {
    .prev_w = 3,
    .new_w = 1,

    .rgain_min = 256,
    .rgain_max = 3072,
    .bgain_min = 256,
    .bgain_max = 3072,
};

/**
 * @brief 高色温区域描述矩阵(HCT - High Color Temperature)
 * 
 * 8x8矩阵，定义高色温光源(如日光、闪光灯等)下的白点分布权重。
 * 数值越大表示该区域在高色温判断中的权重越高。
 * 0表示不参与计算，16表示最高权重，8表示中等权重。
 */
static u8 awb_zone_hct_desc[] = {
    16, 16, 16,  8,  8,  0,  0,  0,
    16, 16, 16, 16,  8,  8,  0,  0,
    16, 16, 16, 16, 16,  8,  8,  0,
    8, 16, 16, 16, 16, 16,  8,  8,
    8,  8, 16, 16, 16, 16, 16,  8,
    0,  8,  8, 16, 16, 16, 16, 16,
    0,  0,  8,  8, 16, 16, 16, 16,
    0,  0,  0,  8,  8, 16, 16, 16,
};

/**
 * @brief 中色温区域描述矩阵(MCT - Medium Color Temperature)
 * 
 * 8x8矩阵，定义中色温光源(如荧光灯、LED灯等)下的白点分布权重。
 * 主要集中在矩阵的中间区域，对应中等色温的颜色分布特征。
 */
static u8 awb_zone_mct_desc[] = {
    0,  0,  0,  0,  0,  0,  0,  0,
    0,  0,  0,  0,  0,  0,  0,  0,
    16, 16, 16, 16, 16, 16, 16,  0,
    16, 16, 16, 16, 16, 16, 16, 16,
    16, 16, 16, 16, 16, 16, 16, 16,
    0, 16, 16, 16, 16, 16, 16, 16,
    0,  0,  0,  0,  0,  0,  0,  0,
    0,  0,  0,  0,  0,  0,  0,  0,
};

/**
 * @brief 低色温区域描述矩阵(LCT - Low Color Temperature)
 * 
 * 8x8矩阵，定义低色温光源(如白炽灯、蜡烛等)下的白点分布权重。
 * 分布模式与高色温相反，主要集中在矩阵的另一侧区域。
 */
static u8 awb_zone_lct_desc[] = {
    16, 16, 16, 16, 16,  0,  0,  0,
    16, 16, 16, 16, 16, 16,  0,  0,
    16, 16, 16, 16, 16, 16, 16,  0,
    16, 16, 16, 16, 16, 16, 16, 16,
    16, 16, 16, 16, 16, 16, 16, 16,
    0, 16, 16, 16, 16, 16, 16, 16,
    0,  0, 16, 16, 16, 16, 16, 16,
    0,  0,  0, 16, 16, 16, 16, 16,

};

/**
 * @brief 绿色区域描述矩阵
 * 
 * 8x8矩阵，用于识别和处理绿色植物等特殊场景。
 * 在户外拍摄时，大量绿色植物可能影响白平衡判断，
 * 此矩阵用于在这种场景下提供更准确的白平衡校正。
 */
static u8 awb_zone_green_desc[] = {
    0,  0,  0,  0,  0,  0,  0,  0,
    0,  0,  0,  0,  0,  0,  0, 16,
    0,  0,  0,  0, 16, 16, 16, 16,
    16, 16, 16, 16, 16, 16, 16, 16,
    16, 16, 16, 16, 16, 16, 16, 16,
    16, 16, 16, 16, 16,  0,  0,  0,
    16, 16,  0,  0,  0,  0,  0,  0,
    16,  0,  0,  0,  0,  0,  0,  0,
};

/**
 * @brief 高色温区域配置结构体
 * 
 * 定义高色温光源的R/G和B/G比值范围，用于识别日光、闪光灯等高色温场景。
 * 
 * @param type 区域类型标识
 * @param rg_min R/G比值最小值(153对应0.6)
 * @param bg_min B/G比值最小值(192对应0.75)
 * @param rg_max R/G比值最大值(205对应0.8)
 * @param bg_max B/G比值最大值(256对应1.0)
 * @param desc 指向高色温区域描述矩阵
 */
static isp_awb_zone_t hct_zone = {
    .type = ISP_AWB_ZONE_HCT,

    .rg_min = 153, //0.6
    .bg_min = 192, //0.75

    .rg_max = 205, //0.8
    .bg_max = 256, //1

    //16x16 array
    .desc = awb_zone_hct_desc,
};

/**
 * @brief 中色温区域配置结构体
 * 
 * 定义中色温光源的R/G和B/G比值范围，用于识别荧光灯、LED等中色温场景。
 * 
 * @param type 区域类型标识
 * @param rg_min R/G比值最小值(205对应0.8)
 * @param bg_min B/G比值最小值(166对应0.65)
 * @param rg_max R/G比值最大值(243对应0.95)
 * @param bg_max B/G比值最大值(205对应0.8)
 * @param desc 指向中色温区域描述矩阵
 */
static isp_awb_zone_t mct_zone = {
    .type = ISP_AWB_ZONE_MCT,

    .rg_min = 205, //0.8
    .bg_min = 166, //0.65

    .rg_max = 243, //0.95
    .bg_max = 205, //0.8

    //16x16 array
    .desc = awb_zone_mct_desc,
};

/**
 * @brief 低色温区域配置结构体
 * 
 * 定义低色温光源的R/G和B/G比值范围，用于识别白炽灯、蜡烛等低色温场景。
 * 
 * @param type 区域类型标识
 * @param rg_min R/G比值最小值(253对应0.95)
 * @param bg_min B/G比值最小值(128对应0.5)
 * @param rg_max R/G比值最大值(333对应1.3)
 * @param bg_max B/G比值最大值(166对应0.65)
 * @param desc 指向低色温区域描述矩阵
 */
static isp_awb_zone_t lct_zone = {
    .type = ISP_AWB_ZONE_LCT,

    .rg_min = 253, //0.95
    .bg_min = 128, //0.5

    .rg_max = 333, //1.3
    .bg_max = 166, //0.65

    //16x16 array
    .desc = awb_zone_lct_desc,
};

/**
 * @brief AWB白点权重数组
 * 
 * 定义不同亮度级别的白点在AWB计算中的权重。
 * 数组索引对应不同的亮度范围，数值表示该亮度下白点的可信度。
 * 较暗区域权重为0(不可信)，较亮区域权重逐渐增加到16(最可信)。
 */
static u8 awb_wp_weights[ISP_AWB_CT1_WP_WEIGHT_SIZE] = {0, 0, 0, 0, 0, 0, 0, 0, 8, 10, 12, 14, 16, 16, 16, 16};

/**
 * @brief AWB色温算法第一组参数配置
 * 
 * 基于色温区域的白平衡算法参数，通过分析不同色温区域的特征来判断光源类型。
 * 
 * @param zones 色温区域数组，包含高、中、低色温三个区域的配置
 * @param wp_weights 白点权重数组指针
 * @param y_min 亮度最小值，低于此值的像素不参与AWB计算
 * @param y_max 亮度最大值，高于此值的像素不参与AWB计算
 * @param hct_ev_th 高色温曝光值阈值，用于判断是否为高色温场景
 * @param lct_ev_th 低色温曝光值阈值，用于判断是否为低色温场景
 * @param prev_w 前一帧权重，用于时间域平滑
 * @param new_w 当前帧权重，用于时间域平滑
 * @param ev_th 曝光值阈值，对应EV=6的光照条件
 */
static isp_awb_ct1_params_t awb_ct1_params = {
    .zones = {&hct_zone, &mct_zone, &lct_zone},
    .wp_weights = awb_wp_weights,

    .y_min = 30,
    .y_max = 235,

    .hct_ev_th = (1 << 14) * 16,
    .lct_ev_th = (1 << 10) * 16,
    .prev_w = 3,
    .new_w = 1,

    .ev_th = 1054, // ev = 6
};

/**
 * @brief C2595传感器AWB参数主配置结构体
 * 
 * 包含C2595 MIPI传感器的完整AWB配置参数，整合了多种AWB算法和场景参数。
 * 
 * @param awb_alg_type AWB算法类型选择
 * @param awb_scene_type AWB场景类型选择
 * @param awb_win_type AWB窗口类型选择
 * @param awb_init_gain 初始增益值，R/G/B均设为1.0倍
 * @param awb_scene_gains 不同场景下的预设增益值数组:
 *        [0] 日光场景: R=1138, B=1264
 *        [1] 荧光灯场景: R=1327, B=1100  
 *        [2] 白炽灯场景: R=768, B=2104
 *        [3] 闪光灯场景: R=1327, B=1100
 *        [4] 阴天场景: R=1012, B=1682
 * @param awb_win_cust_weights 自定义窗口权重数组指针
 * @param awb_ct_weights 色温权重数组指针(未使用)
 * @param awb_gw1_params 灰度世界算法第一组参数指针
 * @param awb_gw2_params 灰度世界算法第二组参数指针
 * @param awb_ct1_params 色温算法第一组参数指针
 * @param awb_ct2_params 色温算法第二组参数指针(未使用)
 */
static isp_awb_params_t c2595_mipi_awb_params = {
    .awb_alg_type = AWB_ALG_TYPE,
    .awb_scene_type = AWB_SCENE_TYPE,
    .awb_win_type = AWB_WIN_TYPE,

    .awb_init_gain =
    {.r_gain = ISP_AWB_ONE, .g_gain = ISP_AWB_ONE, .b_gain = ISP_AWB_ONE},

    .awb_scene_gains =
    {
        { .r_gain = 1138, .g_gain = ISP_AWB_ONE, .b_gain = 1264 },
        { .r_gain = 1327, .g_gain = ISP_AWB_ONE, .b_gain = 1100 },
        { .r_gain =  768, .g_gain = ISP_AWB_ONE, .b_gain = 2104 },
        { .r_gain = 1327, .g_gain = ISP_AWB_ONE, .b_gain = 1100 },
        { .r_gain = 1012, .g_gain = ISP_AWB_ONE, .b_gain = 1682 }
    },

    .awb_win_cust_weights = awb_weight_cust,

    .awb_ct_weights = NULL,

    .awb_gw1_params = &awb_gw1_params,
    .awb_gw2_params = &awb_gw2_params,
    .awb_ct1_params = &awb_ct1_params,
    .awb_ct2_params = NULL,
};

/**
 * @brief 获取C2595传感器AWB参数配置
 * 
 * 此函数是C2595传感器驱动向ISP系统提供AWB参数的接口函数。
 * ISP系统在初始化时会调用此函数获取传感器特定的AWB配置参数，
 * 包括各种算法参数、场景增益值、色温区域定义等。
 * 
 * @return void* 指向c2595_mipi_awb_params结构体的指针
 * 
 * @note 此函数通常在传感器初始化阶段被ISP系统调用
 * @note 返回的参数将被ISP用于自动白平衡算法的执行
 */
void *c2595_mipi_get_awb_params()
{
    return (void *)&c2595_mipi_awb_params;
}

#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "gc1054_mipi.h"
#include "isp_alg.h"
#include "app_config.h"


static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

extern void *GC1054_get_ae_params();
extern void *gc1054_get_awb_params();
extern void *gc1054_get_iq_params();

extern void gc1054_ae_ev_init(u32 fps);

//static u32 sensor_id;

#define WRCMD 0x42
#define RDCMD 0x43

#define GC1054_ROTALL_180 0

typedef struct {
    u8 addr;
    u8 value;
} Sensor_reg_ini;

Sensor_reg_ini GC1054_INI_REG[] = {
    //MCLK=24M  PCLK=39M	HD=1732  VD=750   Row_time=44.41us
    /////////////////////////////////////////////////////
    //////////////////////	 SYS   //////////////////////
    /////////////////////////////////////////////////////
    0xf2, 0x00,
    0xf6, 0x00,
    0xfc, 0x04,
    0xf7, 0x01,
    0xf8, 0x0c,
    0xf9, 0x06,
    0xfa, 0x80,
    0xfc, 0x0e,
/////////,/////////////////////////////////
/////   A,NALOG & CISCTL   ////////////////
/////////,/////////////////////////////////
    0xfe, 0x00,
    0x03, 0x02,
    0x04, 0xa6,
    0x05, 0x02, //HB
    0x06, 0x07,
    0x07, 0x00, //VB
    0x08, 0x0a,
    0x09, 0x00,
    0x0a, 0x04, //row start
    0x0b, 0x00,
    0x0c, 0x00, //col start
    0x0d, 0x02,
    0x0e, 0xd4, //height 724
    0x0f, 0x05,
    0x10, 0x08, //width 1288
    0x17, 0xc0,
    0x18, 0x02,
    0x19, 0x08,
    0x1a, 0x18,
    0x1d, 0x12,
    0x1e, 0x50,
    0x1f, 0x80,
    0x21, 0x30,
    0x23, 0xf8,
    0x25, 0x10,
    0x28, 0x20,
    0x34, 0x08, //data low
    0x3c, 0x10,
    0x3d, 0x0e,
    0xcc, 0x8e,
    0xcd, 0x9a,
    0xcf, 0x70,
    0xd0, 0xa9,
    0xd1, 0xc5,
    0xd2, 0xed, //data high
    0xd8, 0x3c, //dacin offset
    0xd9, 0x7a,
    0xda, 0x12,
    0xdb, 0x50,
    0xde, 0x0c,
    0xe3, 0x60,
    0xe4, 0x78,
    0xfe, 0x01,
    0xe3, 0x01,
    0xe6, 0x10, //ramps offset
/////////,/////////////////////////////////
/////////,//   ISP   //////////////////////
/////////,/////////////////////////////////
    0xfe, 0x01,
    0x80, 0x50,
    0x88, 0x73,
    0x89, 0x03,
    0x90, 0x01,
    0x92, 0x02, //crop win 2<=y<=4
    0x94, 0x03, //crop win 2<=x<=5
    0x95, 0x02, //crop win height
    0x96, 0xd0,
    0x97, 0x05, //crop win width
    0x98, 0x00,
/////////,/////////////////////////////////
/////////,//   BLK   //////////////////////
/////////,/////////////////////////////////
    0xfe, 0x01,
    0x40, 0x22,
    0x43, 0x03,
    0x4e, 0x3c,
    0x4f, 0x00,
    0x60, 0x00,
    0x61, 0x80,
/////////,/////////////////////////////////
/////////,//   GAIN   /////////////////////
/////////,/////////////////////////////////
    0xfe, 0x01,
    0xb0, 0x48,
    0xb1, 0x01,
    0xb2, 0x00,
    0xb6, 0x00,
    0xfe, 0x02,
    0x01, 0x00,
    0x02, 0x01,
    0x03, 0x02,
    0x04, 0x03,
    0x05, 0x04,
    0x06, 0x05,
    0x07, 0x06,
    0x08, 0x0e,
    0x09, 0x16,
    0x0a, 0x1e,
    0x0b, 0x36,
    0x0c, 0x3e,
    0x0d, 0x56,
    0xfe, 0x02,
    0xb0, 0x00, //col_gain[11:8]
    0xb1, 0x00,
    0xb2, 0x00,
    0xb3, 0x11,
    0xb4, 0x22,
    0xb5, 0x54,
    0xb6, 0xb8,
    0xb7, 0x60,
    0xb9, 0x00, //col_gain[12]
    0xba, 0xc0,
    0xc0, 0x20, //col_gain[7:0]
    0xc1, 0x2d,
    0xc2, 0x40,
    0xc3, 0x5b,
    0xc4, 0x80,
    0xc5, 0xb5,
    0xc6, 0x00,
    0xc7, 0x6a,
    0xc8, 0x00,
    0xc9, 0xd4,
    0xca, 0x00,
    0xcb, 0xa8,
    0xcc, 0x00,
    0xcd, 0x50,
    0xce, 0x00,
    0xcf, 0xa1,
/////////,/////////////////////////////////
/////////,   DARKSUN   ////////////////////
/////////,/////////////////////////////////
    0xfe, 0x02,
    0x54, 0xf7,
    0x55, 0xf0,
    0x56, 0x00,
    0x57, 0x00,
    0x58, 0x00,
    0x5a, 0x04,
/////////,/////////////////////////////////
/////////,///   DD   //////////////////////
/////////,/////////////////////////////////
    0xfe, 0x04,
    0x81, 0x8a,
/////////,/////////////////////////////////
/////////,//	 MIPI	/////////////////////
/////////,/////////////////////////////////
    0xfe, 0x03,
    0x01, 0x03,
    0x02, 0x11,
    0x03, 0x90,
    0x10, 0x90,
    0x11, 0x2b,
    0x12, 0x40, //lwc 1280*5/4
    0x13, 0x06,
    0x15, 0x02, //0x06
    0x21, 0x02,
    0x22, 0x02,
    0x23, 0x08,
    0x24, 0x02,
    0x25, 0x10,
    0x26, 0x04,
    0x29, 0x02,
    0x2a, 0x02,
    0x2b, 0x04,
    0xfe, 0x00,

};


static void *iic = NULL;


unsigned char wrGC1054Reg(unsigned char regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }
__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);

    return ret;

}

unsigned char rdGC1054Reg(unsigned char regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, RDCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;

}


/*************************************************************************************************
    sensor api
*************************************************************************************************/


void GC1054_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    for (i = 0; i < sizeof(GC1054_INI_REG) / sizeof(Sensor_reg_ini); i++) {
        wrGC1054Reg(GC1054_INI_REG[i].addr, GC1054_INI_REG[i].value);
    }


    if (*frame_freq == 25) {
        wrGC1054Reg(0xfe, 0x00);
        wrGC1054Reg(0x08, 0xa5); //0x9c
    } else if (*frame_freq == 30) {
        wrGC1054Reg(0xfe, 0x00);
        wrGC1054Reg(0x08, 0x14);
    }
    return;
}


s32 GC1054_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}


s32 GC1054_power_ctl(u8 isp_dev, u8 is_work)
{

    return 0;
}

s32 GC1054_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i ;
    for (i = 0; i < 5; i++) {
        rdGC1054Reg(0xf0, &pid);
        rdGC1054Reg(0xf1, &ver);
        printf("%s  0x%x 0x%x\n", __FUNCTION__,  pid, ver);
//		sensor_id = (pid << 8) | ver;
        if (pid == 0x10 && ver == 0x54) {
            puts("\n hello GC1054-----\n");
            return 0;
        }
    }
    puts("\n----not GC1054-----\n");
    return -1;
}

void gc1054_reset(u8 isp_dev)
{
    u32 reset_gpio;
    u32 pwdn_gpio;
    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }
    /*printf("pwdn_gpio=%d\n", pwdn_gpio);*/
    gpio_direction_output(pwdn_gpio, 0);
    delay(20000);
    gpio_direction_output(reset_gpio, 0);
    delay(20000);
    gpio_direction_output(reset_gpio, 1);
    delay(20000);
}

void GC1054_change_fps(u8 sence, u16 lv)
{
    printf("%s: %d %d\n", __FUNCTION__, sence, lv);
}

static u8 cur_sensor_type = 0xff;

s32 GC1054_check(u8 isp_dev, u32 _reset_gpio, u32 pwdn_gpio)
{
    puts("\n\n GC1054_check \n\n");

    if (!iic) {
        iic = dev_open("iic0", &_hw_iic);
        /* iic = dev_open("swiic0", &_sw_iic); */
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }

    printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = _reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

    gc1054_reset(isp_dev);

    puts("gc1054_id_check\n");
    if (0 != GC1054_ID_check()) {

        dev_close(iic);
        iic = NULL;

        return -1;
    }

    cur_sensor_type = isp_dev;

    return 0;
}



s32 GC1054_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\n gc1054_init22 \n\n");

    GC1054_config_SENSOR(width, height, format, frame_freq);

    return 0;
}

//pclk 48M
u32 gc1054_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 text_align, ratio;
    u32 line = exp_time_us * 39 / 1726;

    //gain = (gain - 1054) * 70 / 100 + 1054;
    if (!line) {
        line = 1;
    }

    text_align = line * 1726 / 39;
    ratio = exp_time_us * 1024 / text_align;
    if (ratio < 1024) {
        ratio = 1024;
    }
    shutter->ae_exp_line =  line;
    shutter->ae_gain = (gain >> 5) * (ratio >> 5);
    shutter->ae_exp_clk = 0;
    //printf("exp_time_us = %d\n",exp_time_us);
    return 0;

}

#define GC1054_ANALOG_GAIN_0   1054  // 1
#define GC1054_ANALOG_GAIN_1   1454  // 1.64
#define GC1054_ANALOG_GAIN_2   2037  // 1.86
#define GC1054_ANALOG_GAIN_3   2918  // 3.22
#define GC1054_ANALOG_GAIN_4   4126  // 3.67
#define GC1054_ANALOG_GAIN_5   5908  // 6.22
#define GC1054_ANALOG_GAIN_6   8253  // 7.27
#define GC1054_ANALOG_GAIN_7   11806 // 12.11x
#define GC1054_ANALOG_GAIN_8   16506 // 17.95x
#define GC1054_ANALOG_GAIN_9   23859 // 24.84x
#define GC1054_ANALOG_GAIN_10  33351 //36.55x

#define  GC1054_DGAIN_PRECISE  64
static u32 gc1054_gain_steps[] = {
    GC1054_ANALOG_GAIN_0,
    GC1054_ANALOG_GAIN_1,
    GC1054_ANALOG_GAIN_2,
    GC1054_ANALOG_GAIN_3,
    GC1054_ANALOG_GAIN_4,
    GC1054_ANALOG_GAIN_5,
    GC1054_ANALOG_GAIN_6,
    GC1054_ANALOG_GAIN_7,
    GC1054_ANALOG_GAIN_8,
    GC1054_ANALOG_GAIN_9,
    GC1054_ANALOG_GAIN_10,
};


static void decompose_gain(int total_gain/*q10*/, u32 *a_gain, u32 *d_gain)
{
    int i = 0;
    int size;


    size = sizeof(gc1054_gain_steps) / sizeof(u32);

    for (i = 0; i < size; i++) {
        if (gc1054_gain_steps[i] > total_gain) {
            break;
        }
    }

    i = i - 1;
    if (i < 0) {
        i = 0;
    }

    *a_gain = i;
    *d_gain = GC1054_DGAIN_PRECISE * total_gain / gc1054_gain_steps[i];

}

static u8 gc1054_shutter_cmd[8][3] = {
    {0x42, 0xfe, 0x01},  //again
    {0x42, 0xb6, 0x00},  //again
    {0x42, 0xb1, 0x00},  //dgainL
    {0x42, 0xb2, 0x00},  //dgainH
    {0x42, 0xb0, 0x00},  //ggaim
    {0x42, 0xfe, 0x00},  //again
    {0x42, 0x04, 0x00},  //exptime L
    {0x42, 0x03, 0x00},   //exptime H
} ;


u32 gc1054_set_shutter(isp_ae_shutter_t *shutter)
{
    u32 gain;
    u32 again, dgain;
    int i;
    static u8 init  = 0;

    gain = (shutter->ae_gain);

    decompose_gain(gain, &again, &dgain);

    //printf("time=%d, again=%d, dgain=%d\n", shutter->ae_exp_line, again, dgain);
    gc1054_shutter_cmd[0][2] = 0x01 ;
    gc1054_shutter_cmd[1][2] = again ;
    gc1054_shutter_cmd[2][2] = dgain >> 6 ;

    gc1054_shutter_cmd[3][2] = (dgain << 2) & 0xfc;

    gc1054_shutter_cmd[4][2] = 0x48 ;
    gc1054_shutter_cmd[5][2] = 0x00 ;

    gc1054_shutter_cmd[6][2] = shutter->ae_exp_line & 0xFF ;
    gc1054_shutter_cmd[7][2] = (shutter->ae_exp_line >> 8) & 0x1F;

    for (i = 0; i < 8; i++) {
        wrGC1054Reg(gc1054_shutter_cmd[i][1], gc1054_shutter_cmd[i][2]);
    }

    return 0;
}

void GC1054_sleep()
{
    wrGC1054Reg(0xf6, 0x01);
}

void GC1054_wakeup()
{
    wrGC1054Reg(0xf6, 0x00);
}

void GC1054_W_Reg(u16 addr, u16 val)
{
    //printf("update reg%x with %x\n", addr, val);
    wrGC1054Reg((u8)addr, (u8)val);
}

u16 GC1054_R_Reg(u16 addr)
{
    u8 val;
    rdGC1054Reg((u8)addr, &val);
    return val;
}


REGISTER_CAMERA(GC1054) = {
    .logo 				= 	"GC1054M",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			= 	SEN_IN_FORMAT_RGGB,
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_CSI2,
    .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
    .fps         		= 	25,

    .sen_size 			= 	{GC1054_INPUT_W, GC1054_INPUT_H},
    .isp_size 			= 	{GC1054_OUTPUT_W, GC1054_OUTPUT_H},

    .cap_fps         		= 	25,
    .sen_cap_size 			= 	{GC1054_INPUT_W, GC1054_INPUT_H},
    .isp_cap_size 			= 	{GC1054_OUTPUT_W, GC1054_OUTPUT_H},

    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	GC1054_check,
        .init 		        = 	GC1054_init,
        .set_size_fps 		=	GC1054_set_output_size,
        //.get_mipi_clk       =   gc1054_get_clk,
        .power_ctrl         =   GC1054_power_ctl,

        .get_ae_params  	=	GC1054_get_ae_params,
        .get_awb_params 	=	gc1054_get_awb_params,
        .get_iq_params 	    =	gc1054_get_iq_params,

        .sleep 		        =	GC1054_sleep,
        .wakeup 		    =	GC1054_wakeup,
        .write_reg 		    =	GC1054_W_Reg,
        .read_reg 		    =	GC1054_R_Reg,
    }
};



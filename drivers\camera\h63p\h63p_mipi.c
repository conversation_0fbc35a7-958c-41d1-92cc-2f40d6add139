#ifndef H63P_MIPI_C
#define  H63P_MIPI_C


#include "iic.h"
#include "isp_dev.h"
#include "gpio.h"
#include "h63p_mipi.h"
#include "isp_alg.h"
#include "app_config.h"

#define VBLANK_30FPS    0x27 // 0x25
#define VBLANK_25FPS    0xfe
#define VBLANK_20FPS    0x250
#define VBLANK_16FPS    0x3f5

#define FRAME_W  1920//1920//1600//1920//1600//
#define FRAME_H  750//900//750//750//
#define H63P_FPS  20 //25//30//30//

#define H63P  1

#define INPUT_CLK       24
static u8  H63P_WRCMD =      0x80;
static u8  H63P_RDCMD =      0x81;

static u32 cur_expline = -1;
static u32 cur_again = -1;
static u32 cur_dgain = -1;
static u32 cur_expclk = -1;
static u32 cur_reg_idx = -1;
static u32 cur_vblank = -1;
static u32 next_reg_idx = -1;



static u32 reset_gpios[2] = {-1, -1};
static u32 pwdn_gpios[2] = {-1, -1};

static u8 cur_sensor_type = 0xff;

 bool SensorIdx =0;

extern int g_printFlag;


extern void *h63p_mipi_get_ae_params();
extern void *h63p_mipi_get_awb_params();
extern void *h63p_mipi_get_iq_params();
extern void h63p_mipi_ae_ev_init(u32 fps);

typedef struct {
    u8 addr;
    u8 value;
} Sensor_reg_ini;

struct stVrefh {
    u16 index;
    u16 th_low;
    u16 th_high;
    u8 vrefh;
};
#define EXTERNAL_LDO_1V5 1

//?????VDDIO???3.3V??????????1
//?????VDDIO???1.8V??????????0
#define	VDDIO_VOLTAGE_3V3 0
#define DSC_ADDR_BASE 0x0400

u8 h63p_read_reg[] = {0x12,0x48,0x48,0x0E,0x0F,0x10,0x11,0x57,0x58,0x61,0x46,0x0D,0x20,0x21,0x22,0x23,0x24,
0x25,0x26,0x27,0x28,0x29,0x2A,0x2B,0x2C,0x2D,0x2E,0x2F,0x41,0x42,0x47,0x76,0x77,0x80,0xAF,0x8A,0xA6,0x8D,
0xAB,0x1D,0x1E,0x6C,0x9E,0x6E,0x70,0x71,0x72,0x73,0x74,0x78,0x89,0x6B,0x86,0x9C,0x3A,0x3B,0x3C,0x3D,0x3E,
0x31,0x32,0x33,0x34,0x35,0x56,0x59,0x85,0x64,0x8F,0xA4,0xA7,0xA9,0x45,0x5B,0x5C,0x5D,0x5E,0x63,0x65,0x66,
0x67,0x68,0x69,0x6A,0x7A,0xA5,0x94,0x13,0x96,0xB7,0x4A,0xB5,0xA1,0xA3,0xB1,0x93,0x7E,0x50,0x49,0x8E,0x7F,
0x0C,0xBC,0x82,0x19,0x1F,0x1B,0x12,

};

// H63P
static Sensor_reg_ini h63p_mipi_INI_REG[] = {

    #if 0
	{0x12,0x40},
	{0x48,0x85},
	{0x48,0x05},
	{0x0E,0x11},
	{0x0F,0x84},
	{0x10,0x1E},
	{0x11,0x80},
	{0x57,0x60},
	{0x58,0x18},
	{0x61,0x10},
	{0x46,0x08},
	{0x0D,0xA0},
	{0x20,0x20},
	{0x21,0x03},
	{0x22,0xEE},
	{0x23,0x02},
	{0x24,0x80},
	{0x25,0xD0},
	{0x26,0x22},
	{0x27,0x8B},
	{0x28,0x15},
	{0x29,0x02},
	{0x2A,0x80},
	{0x2B,0x12},
	{0x2C,0x00},
	{0x2D,0x00},
	{0x2E,0xBA},
	{0x2F,0x60},
	{0x41,0x84},
	{0x42,0x02},
	{0x47,0x46},
	{0x76,0x40},
	{0x77,0x06},
	{0x80,0x01},
	{0xAF,0x22},
	{0x8A,0x00},
	{0xA6,0x00},
	{0x8D,0x49},
	{0xAB,0x00},
	{0x1D,0x00},
	{0x1E,0x04},
	{0x6C,0x50},
	{0x9E,0xF8},
	{0x6E,0x2C},
	{0x70,0x8C},
	{0x71,0x6D},
	{0x72,0x6A},
	{0x73,0x46},
	{0x74,0x02},
	{0x78,0x8E},
	{0x89,0x01},
	{0x6B,0x20},
	{0x86,0x40},
	{0x9C,0xE1},
	{0x3A,0xAC},
	{0x3B,0x18},
	{0x3C,0x5D},
	{0x3D,0x80},
	{0x3E,0x6E},
	{0x31,0x07},
	{0x32,0x14},
	{0x33,0x12},
	{0x34,0x1C},
	{0x35,0x1C},
	{0x56,0x12},
	{0x59,0x20},
	{0x85,0x14},
	{0x64,0xD2},
	{0x8F,0x90},
	{0xA4,0x87},
	{0xA7,0x80},
	{0xA9,0x48},
	{0x45,0x01},
	{0x5B,0xA0},
	{0x5C,0x6C},
	{0x5D,0x44},
	{0x5E,0x81},
	{0x63,0x0F},
	{0x65,0x12},
	{0x66,0x43},
	{0x67,0x79},
	{0x68,0x00},
	{0x69,0x78},
	{0x6A,0x28},
	{0x7A,0x66},
	{0xA5,0x03},
	{0x94,0xC0},
	{0x13,0x81},
	{0x96,0x84},
	{0xB7,0x4A},
	{0x4A,0x01},
	{0xB5,0x0C},
	{0xA1,0x0F},
	{0xA3,0x40},
	{0xB1,0x00},
	{0x93,0x00},
	{0x7E,0x4C},
	{0x50,0x02},
	{0x49,0x10},
	{0x8E,0x40},
	{0x7F,0x56},
	{0x0C,0x00},
	{0xBC,0x11},
	{0x82,0x00},
	{0x19,0x20},
	{0x1F,0x10},
	{0x1B,0x4F},
	{0x12,0x00},
	#else

/*
	Project=131	;H63P
Width=1280
Height=720
disWidth=1280
disHeight=720
FrameWidth=1920
FrameHeight=750
H_pad=0
V_pad=0
SrOutputFormats=1
Interface=1
HDR_Mode=0
MclkRate=24.000
DVPClkRate=43.20000
SensorLineMode=0
MipiImgVc=0
MipiImgVc1=1
LVDSformat=0
PixelRate=43.20000		;SysClk = 43.20000
MipiClkRate=216.00000


	*/

	{0x12,0x40},
{0x48,0x85},
{0x48,0x05},
{0x0E,0x11},
{0x0F,0x84},
{0x10,0x24},
{0x11,0x80},
{0x57,0x60},
{0x58,0x18},
{0x61,0x10},
{0x46,0x08},
{0x0D,0xA0},
{0x20,0xC0},
{0x21,0x03},
{0x22,0xEE},
{0x23,0x02},
{0x24,0x80},
{0x25,0xD0},
{0x26,0x22},
{0x27,0xD6},
{0x28,0x15},
{0x29,0x02},
{0x2A,0xCB},
{0x2B,0x12},
{0x2C,0x00},
{0x2D,0x00},
{0x2E,0xBA},
{0x2F,0x60},
{0x41,0x84},
{0x42,0x02},
{0x47,0x46},
{0x76,0x40},
{0x77,0x06},
{0x80,0x01},
{0xAF,0x22},
{0x8A,0x00},
{0xA6,0x00},
{0xAB,0x00},
{0x8D,0x49},
{0x1D,0x00},
{0x1E,0x04},
{0x6C,0x50},
{0x9E,0xF8},
{0x6E,0x2C},
{0x70,0x8C},
{0x71,0x6D},
{0x72,0x6A},
{0x73,0x46},
{0x74,0x02},
{0x78,0x8E},
{0x89,0x01},
{0x6B,0x20},
{0x86,0x40},
{0x9C,0xE1},
{0x3A,0xAC},
{0x3B,0x18},
{0x3C,0x5D},
{0x3D,0x80},
{0x3E,0x6E},
{0x31,0x07},
{0x32,0x14},
{0x33,0x12},
{0x34,0x1C},
{0x35,0x1C},
{0x56,0x12},
{0x59,0x20},
{0x85,0x14},
{0x64,0xD2},
{0x8F,0x90},
{0xA4,0x87},
{0xA7,0x80},
{0xA9,0x48},
{0x45,0x01},
{0x5B,0xA0},
{0x5C,0x6C},
{0x5D,0x44},
{0x5E,0x81},
{0x63,0x0F},
{0x65,0x12},
{0x66,0x43},
{0x67,0x79},
{0x68,0x00},
{0x69,0x78},
{0x6A,0x28},
{0x7A,0x66},
{0xA5,0x03},
{0x94,0xC0},
{0x13,0x81},
{0x96,0x84},
{0xB7,0x4A},
{0x4A,0x01},
{0xB5,0x0C},
{0xA1,0x0F},
{0xA3,0x40},
{0xB1,0x00},
{0x93,0x00},
{0x7E,0x4C},
{0x50,0x02},
{0x49,0x10},
{0x8E,0x40},
{0x7F,0x56},
{0x0C,0x00},
{0xBC,0x11},
{0x82,0x00},
{0x19,0x20},
{0x1F,0x10},
{0x1B,0x4F},
{0x12,0x00},
#endif
};


static Sensor_reg_ini H63P_MIPI_INI_REG_BEFORE[] = {
    #if 0
{0x12,0x40},
{0x48,0x85},
{0x48,0x05},
{0x0E,0x11},
{0x0F,0x84},
{0x10,0x1E},
{0x11,0x80},
{0x57,0x60},
{0x58,0x18},
{0x61,0x10},
{0x46,0x08},
{0x0D,0xA0},
{0x20,0x20},
{0x21,0x03},
{0x22,0xEE},
{0x23,0x02},
{0x24,0x80},
{0x25,0xD0},
{0x26,0x22},
{0x27,0x8B},
{0x28,0x15},
{0x29,0x02},
{0x2A,0x80},
{0x2B,0x12},
{0x2C,0x00},
{0x2D,0x00},
{0x2E,0xBA},
{0x2F,0x60},
{0x41,0x84},
{0x42,0x02},
{0x47,0x46},
{0x76,0x40},
{0x77,0x06},
{0x80,0x01},
{0xAF,0x22},
{0x8A,0x00},
{0xA6,0x00},
{0x8D,0x49},
{0xAB,0x00},
{0x1D,0x00},
{0x1E,0x04},
{0x6C,0xD0},   //0101 0000
{0x9E,0xF8},
{0x6E,0x2C},
{0x70,0x8C},
{0x71,0x6D},
{0x72,0x6A},
{0x73,0x46},
{0x74,0x02},
{0x78,0x8E},
{0x89,0x01},
{0x6B,0x20},
{0x86,0x40},
{0x9C,0xE1},
{0x3A,0xAC},
{0x3B,0x18},
{0x3C,0x5D},
{0x3D,0x80},
{0x3E,0x6E},
{0x31,0x07},
{0x32,0x14},
{0x33,0x12},
{0x34,0x1C},
{0x35,0x1C},
{0x56,0x12},
{0x59,0x20},
{0x85,0x14},
{0x64,0xD2},
{0x8F,0x90},
{0xA4,0x87},
{0xA7,0x80},
{0xA9,0x48},
{0x45,0x01},
{0x5B,0xA0},
{0x5C,0x6C},
{0x5D,0x44},
{0x5E,0x81},
{0x63,0x0F},
{0x65,0x12},
{0x66,0x43},
{0x67,0x79},
{0x68,0x00},
{0x69,0x78},
{0x6A,0x28},
{0x7A,0x66},
{0xA5,0x03},
{0x94,0xC0},
{0x13,0x81},
{0x96,0x84},
{0xB7,0x4A},
{0x4A,0x01},
{0xB5,0x0C},
{0xA1,0x0F},
{0xA3,0x40},
{0xB1,0x00},
{0x93,0x00},
{0x7E,0x4C},
{0x50,0x02},
{0x49,0x10},
{0x8E,0x40},
{0x7F,0x56},
{0x0C,0x00},
{0xBC,0x11},
{0x82,0x00},
{0x19,0x20},
{0x1F,0x10},
{0x1B,0x4F},
{0x12,0x00},
  #else
{0x12,0x40},
{0x48,0x85},
{0x48,0x05},
{0x0E,0x11},
{0x0F,0x84},
{0x10,0x24},
{0x11,0x80},
{0x57,0x60},
{0x58,0x18},
{0x61,0x10},
{0x46,0x08},
{0x0D,0xA0},
{0x20,0xC0},
{0x21,0x03},
{0x22,0xEE},
{0x23,0x02},
{0x24,0x80},
{0x25,0xD0},
{0x26,0x22},
{0x27,0xD6},
{0x28,0x15},
{0x29,0x02},
{0x2A,0xCB},
{0x2B,0x12},
{0x2C,0x00},
{0x2D,0x00},
{0x2E,0xBA},
{0x2F,0x60},
{0x41,0x84},
{0x42,0x02},
{0x47,0x46},
{0x76,0x40},
{0x77,0x06},
{0x80,0x01},
{0xAF,0x22},
{0x8A,0x00},
{0xA6,0x00},
{0xAB,0x00},
{0x8D,0x49},
{0x1D,0x00},
{0x1E,0x04},
{0x6C,0x50},
{0x9E,0xF8},
{0x6E,0x2C},
{0x70,0x8C},
{0x71,0x6D},
{0x72,0x6A},
{0x73,0x46},
{0x74,0x02},
{0x78,0x8E},
{0x89,0x01},
{0x6B,0x20},
{0x86,0x40},
{0x9C,0xE1},
{0x3A,0xAC},
{0x3B,0x18},
{0x3C,0x5D},
{0x3D,0x80},
{0x3E,0x6E},
{0x31,0x07},
{0x32,0x14},
{0x33,0x12},
{0x34,0x1C},
{0x35,0x1C},
{0x56,0x12},
{0x59,0x20},
{0x85,0x14},
{0x64,0xD2},
{0x8F,0x90},
{0xA4,0x87},
{0xA7,0x80},
{0xA9,0x48},
{0x45,0x01},
{0x5B,0xA0},
{0x5C,0x6C},
{0x5D,0x44},
{0x5E,0x81},
{0x63,0x0F},
{0x65,0x12},
{0x66,0x43},
{0x67,0x79},
{0x68,0x00},
{0x69,0x78},
{0x6A,0x28},
{0x7A,0x66},
{0xA5,0x03},
{0x94,0xC0},
{0x13,0x81},
{0x96,0x84},
{0xB7,0x4A},
{0x4A,0x01},
{0xB5,0x0C},
{0xA1,0x0F},
{0xA3,0x40},
{0xB1,0x00},
{0x93,0x00},
{0x7E,0x4C},
{0x50,0x02},
{0x49,0x10},
{0x8E,0x40},
{0x7F,0x56},
{0x0C,0x00},
{0xBC,0x11},
{0x82,0x00},
{0x19,0x20},
{0x1F,0x10},
{0x1B,0x4F},
{0x12,0x00},

 #endif

};


static void *iic = NULL;

unsigned char wrh63p_mipiReg(unsigned char regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, H63P_WRCMD)) {
        ret = 0;
        goto __wend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        goto __wend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        goto __wend;
    }

__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}

unsigned char wrh63p_mipiReg2(unsigned char regID, unsigned char regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);
    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, 0x80)) {
        ret = 0;
		 puts("1 err\n");
        goto __wend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX, regID)) {
        ret = 0;
        puts("2 err\n");

        goto __wend;
    }

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regDat)) {
        ret = 0;
        puts("3 err\n");

        goto __wend;
    }

__wend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;
}


unsigned char rdh63p_mipiReg(unsigned char regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, H63P_WRCMD)) {
        ret = 0;
        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID)) {
         ret = 0;
         goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, H63P_RDCMD)) {
        ret = 0;
         goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat)) {
        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;

}

unsigned char rdH63p_MIPIReg2(unsigned char regID, unsigned char *regDat)
{
    u8 ret = 1;

    dev_ioctl(iic, IIC_IOCTL_START, 0);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, 0x80))
	{
        ret = 0;
  		puts("4 \n");

        goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_STOP_BIT, regID))
	{
         ret = 0;
  puts("5\n");

         goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_TX_WITH_START_BIT, 0x81))
	{
        ret = 0;
   puts("6\n");

         goto __rend;
    }

    delay(10);

    if (dev_ioctl(iic, IIC_IOCTL_RX_WITH_STOP_BIT, (u32)regDat))
	{
  puts("7\n");

        ret = 0;
        goto __rend;
    }
__rend:

    dev_ioctl(iic, IIC_IOCTL_STOP, 0);
    return ret;

}

/*************************************************************************************************
 sensor api
 *************************************************************************************************/
void h63p_mipi_config_SENSOR(u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    u32 i;
    u16 dsc_addr;
    u8 pid;


	u8 r_reg_val;

    printf("h63p pid = %x\n", pid);

    for (i = 0; i < sizeof(h63p_mipi_INI_REG) / sizeof(Sensor_reg_ini); i++)
	{
	 	if(SensorIdx == 0)
        	wrh63p_mipiReg(h63p_mipi_INI_REG[i].addr, h63p_mipi_INI_REG[i].value);
	 	else
	 		wrh63p_mipiReg2(H63P_MIPI_INI_REG_BEFORE[i].addr, H63P_MIPI_INI_REG_BEFORE[i].value);
    }

	delay(40000);
#if 0
	printf("======================== h63p read reg  START ==================\n");
	for(i = 0; i < 87; i++)
	{
		rdh63p_mipiReg(h63p_read_reg[i], &r_reg_val);

		printf("h63p_read_reg[%d] 0x%x = 0x%x \n", i, h63p_read_reg[i], r_reg_val);
	}

	printf("======================== h63p read reg END ==================\n");
#endif
	#if 1
    if(SensorIdx == 0)
        {
     if (*frame_freq == 25) {
        wrh63p_mipiReg2(0x22, 0x84);
        wrh63p_mipiReg2(0x23, 0x03);
    }
    if (*frame_freq == 5) {
        wrh63p_mipiReg2(0x22, 0x94);
        wrh63p_mipiReg2(0x23, 0x11);
    }

    if (*frame_freq == 10) {
        wrh63p_mipiReg2(0x22, 0xca);
        wrh63p_mipiReg2(0x23, 0x8);
    }
    if (*frame_freq == 15) {
        wrh63p_mipiReg2(0x22, 0xdc);
        wrh63p_mipiReg2(0x23, 0x5);
    }
    if (*frame_freq == 20) {
        wrh63p_mipiReg2(0x22, 0x65);
        wrh63p_mipiReg2(0x23, 0x4);
    }
    }
    else{
           if (*frame_freq == 25) {

        wrh63p_mipiReg2(0x22, 0x84);
        wrh63p_mipiReg2(0x23, 0x03);
        printf("H63P 25fps\n");
    } else {
        wrh63p_mipiReg2(0x22, 0xee);
        wrh63p_mipiReg2(0x23, 0x02);
        printf("H63P 30fps\n");
    }
     }
     #endif // 1

    h63p_mipi_ae_ev_init(*frame_freq);

    return;
}

s32 h63p_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq)
{
    return 0;
}

s32 h63p_mipi_power_ctl(u8 isp_dev, u8 is_work)
{
    return 0;
}

void h63p_mipi_xclk_set(u8 isp_dev)
{

}


s32 h63p_mipi_ID_check(void)
{
    u8 pid = 0x00;
    u8 ver = 0x00;
    u8 i;
    if (!iic) {
        iic = dev_open("iic0", 0);
        if (!iic) {
            return -1;
        }

    }
     for (i = 0; i < 3; i++) {
		if(SensorIdx == 0)
	 	{
	 		puts("Sensor ID AAAA\n");
        	rdh63p_mipiReg(0x0a, &pid);
        	rdh63p_mipiReg(0x0b, &ver);
	 	}
	 	else
	 	{
	 		puts("Sensor ID BBBBBB\n");
        	rdH63p_MIPIReg2(0x0a, &pid);
        	rdH63p_MIPIReg2(0x0b, &ver);
	 	}

        puts("Sensor ID \n");

        printf("pid = %x; ver = %x;\n", pid, ver);
        puts("\n");
        //printf("ADD = %x\n", H63_WRCMD);

    }


    if (pid != 0x08 || ver != 0x48) {
        puts("\n----not h63p_mipi-----\n");
        return -1;
    }
    puts("\n----hello h63p_mipi-----\n");
    return 0;
}

static int iniOnce = 0;
void h63p_mipi_reset(u8 isp_dev)
{
    puts("h63p_mipi reset \n");

    u32 reset_gpio;
    u32 pwdn_gpio;

    if (isp_dev == ISP_DEV_0) {
        reset_gpio = reset_gpios[0];
        pwdn_gpio = pwdn_gpios[0];
    } else {
        reset_gpio = reset_gpios[1];
        pwdn_gpio = pwdn_gpios[1];
    }

	if (iniOnce == 0)
	{
		iniOnce = 1;
    	gpio_direction_output(reset_gpio, 0);
    	delay(40000);
    	gpio_direction_output(reset_gpio, 0);
 		delay(40000);
  		delay(40000);


		//gpio_direction_output(IO_PORTH_06, 1);
		//delay(40000);

		printf("=============== H63P_MIPI_reset========================\n");

		SensorIdx= FALSE;

		return ;
	}

	SensorIdx = !SensorIdx;

// 	if(SensorIdx)
// 	{
// 		//??t??
// //		gpio_direction_output(IO_PORTH_02, 1);
// //		delay(40000);
// //		gpio_direction_output(IO_PORTH_02, 0);
// //		delay(40000);
// //		gpio_direction_output(IO_PORTH_02, 1);
// //		delay(40000);


// 		gpio_direction_output(IO_PORTH_02, 1);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_06, 0);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_06, 1);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_06, 0);
// 		delay(40000);

// 	}
// 	else
// 	{
// 		//???t??
// 		gpio_direction_output(IO_PORTH_06, 1);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_02, 0);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_02, 1);
// 		delay(40000);
// 		gpio_direction_output(IO_PORTH_02, 0);
// 		delay(40000);
// 	}


}

s32 h63p_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio)
{
    printf("\n\nh63p_mipi_check reset pin :%d\n\n", reset_gpio);
    if (!iic) {
        if (isp_dev == ISP_DEV_0) {
            iic = dev_open("iic0", 0);
        } else {
            iic = dev_open("iic1", 0);
        }
        if (!iic) {
            return -1;
        }
    } else {
        if (cur_sensor_type != isp_dev) {
            return -1;
        }
    }
    printf("\n\n isp_dev =%d\n\n", isp_dev);

    reset_gpios[isp_dev] = reset_gpio;
    pwdn_gpios[isp_dev] = pwdn_gpio;

   h63p_mipi_reset(isp_dev);

    if (0 !=h63p_mipi_ID_check()) {
        dev_close(iic);
        iic = NULL;
        return -1;
    }

    cur_sensor_type = isp_dev;


    return 0;
}


s32 h63p_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq)
{
    puts("\n\nh63p_mipi_init \n\n");

    cur_expline = -1;
    cur_again = -1;
    cur_dgain = -1;
    cur_expclk = -1;
    cur_reg_idx = -1;
    cur_vblank = -1;
    next_reg_idx = -1;

   h63p_mipi_config_SENSOR(width, height, format, frame_freq);
    return 0;
}



static void set_again(u32 again)
{
    if (cur_again == again) {
        return;
    }
    cur_again = again;
#if 0
    wrh63p_mipiReg(0x00, again);
#else
    if (SensorIdx == 0)
    {
    	wrh63p_mipiReg(0xC4, 0x00);
    	wrh63p_mipiReg(0xC5, again);
    }
    else
    {
    	wrh63p_mipiReg2(0xC4, 0x00);
    	wrh63p_mipiReg2(0xC5, again);
    }
#endif
    return;
}

static void set_dgain(u32 dgain)
{
    if (cur_dgain == dgain) {
        return;
    }
    cur_dgain = dgain;
}

//q10
static void calc_gain(u32 gain, u32 *again, u32 *dgain)
{
    u16 temp = 0;
    u8 pga_h = 0;
    u8 pga_l = 0;
    u8 i = 0;
    temp = (gain)>>10;
    if(temp == 0)
    {
        *dgain = 0;
        *again =  pga_h<<4 |pga_l;
        return ;
    }
    if(temp>=128*2)
    {
        pga_h = 7;
        pga_l = 16-1;
        *dgain=0;
        *again =  pga_h<<4 |pga_l;
        return;
    }

    for(i=7;i>=0;i--)
    {
        if(temp&BIT(i))
        {
            pga_h = i;
            pga_l = (gain-BIT(i)*1024)/BIT(i)/64;
            *dgain = 0;
            *again =  pga_h<<4 |pga_l;
            /* printf("isp gain =%d, add=%d ,pag_h=%d ,pag_l=%d  \n",gain,BIT(i)*1024+BIT(i)* pga_l*64, BIT(i),pga_l);  */
            return ;
        }
    }

}





static void set_shutter(u32 texp, u32 texp_mck)
{
    if( cur_expline ==texp)
        return ;
//    printf("texp %d \n",texp );
    if(texp > FRAME_H-1)
        texp = FRAME_H -1;
#if 0
    wrh63p_mipiReg(0x01,texp&0xff);

    wrh63p_mipiReg(0x02,(texp>>8)&0xff);
#else//???????????
    if (SensorIdx == 0)
    {
    wrh63p_mipiReg(0xC0, 0x01);
    wrh63p_mipiReg(0xC1,texp&0xff);

    wrh63p_mipiReg(0xC2, 0x02);
    wrh63p_mipiReg(0xC3,(texp>>8)&0xff);
    }
    else
    {
    wrh63p_mipiReg2(0xC0, 0x01);
    wrh63p_mipiReg2(0xC1,texp&0xff);

    wrh63p_mipiReg2(0xC2, 0x02);
    wrh63p_mipiReg2(0xC3,(texp>>8)&0xff);
    }
   // wrh63_mipiReg(0x1F, 0x80);   //group write enable

#endif
    cur_expline=texp;

}


u32 h63p_mipi_calc_shutter(isp_ae_shutter_t *shutter, u32 exp_time_us, u32 gain)
{
    u32 texp =  0;
    u32 texp_align = 0;
    u32 ratio = 0;
    texp =exp_time_us*FRAME_H*H63P_FPS/1000000;
    texp_align = (texp) *1000*1000/ (FRAME_H*H63P_FPS);
    if (texp_align < exp_time_us) {
        ratio = (exp_time_us) * (1 << 10) / texp_align;

    } else {
        ratio = (1 << 10);
    }

    shutter->ae_exp_line =  texp;
    shutter->ae_gain = (gain * ratio) >> 10;
    shutter->ae_exp_clk = 0;
    return 0;

}



u32 h63p_mipi_set_shutter(isp_ae_shutter_t *shutter)
{
    u32 again, dgain;
    calc_gain(shutter->ae_gain, &again, &dgain);

    set_again(again);
    set_dgain(dgain);

    set_shutter(shutter->ae_exp_line, shutter->ae_exp_clk);
        if (SensorIdx == 0)
   		 wrh63p_mipiReg(0x1F, 0x80);   //group write enable
	 else
	 	wrh63p_mipiReg2(0x1F, 0x80);
        return 0;
}


void h63p_mipi_sleep()
{

}

void h63p_mipi_wakeup()
{

}

void h63p_mipi_W_Reg(u16 addr, u16 val)
{
    wrh63p_mipiReg((u8) addr, (u8) val);
}

u16 h63p_mipi_R_Reg(u16 addr)
{
    u8 val;
    if (SensorIdx == 0)
    	rdh63p_mipiReg((u8) addr, &val);
    else
	rdH63p_MIPIReg2((u8) addr, &val);
    return val;
}



REGISTER_CAMERA(h63p_mipi) = {
    .logo 				= 	"H63PM",
    .isp_dev 			= 	ISP_DEV_NONE,
    .in_format 			=   SEN_IN_FORMAT_BGGR,  //	SEN_IN_FORMAT_BGGR
    .out_format 		= 	ISP_OUT_FORMAT_YUV,
    .mbus_type          =   SEN_MBUS_CSI2,
    .mbus_config        =   SEN_MBUS_DATA_WIDTH_10B | SEN_MBUS_CSI2_1_LANE,
    .fps         		= 	H63P_FPS, // 30,

    .sen_size 			= 	{H63P_MIPI_OUTPUT_W, H63P_MIPI_OUTPUT_H},
    .isp_size 			= 	{1200/*H63P_MIPI_OUTPUT_W*/, 704/*H63P_MIPI_OUTPUT_H*/},

    .cap_fps         		= 	H63P_FPS,
    .sen_cap_size 			= 	{H63P_MIPI_OUTPUT_W, H63P_MIPI_OUTPUT_H},
    .isp_cap_size 			= 	{1200/*H63P_MIPI_OUTPUT_W*/, 704/*H63P_MIPI_OUTPUT_H*/},

    .ops                =   {
        .avin_fps           =   NULL,
        .avin_valid_signal  =   NULL,
        .avin_mode_det      =   NULL,
        .sensor_check 		= 	h63p_mipi_check,
        .init 		        = 	h63p_mipi_init,
        .set_size_fps 		=	h63p_mipi_set_output_size,
        .power_ctrl         =  h63p_mipi_power_ctl,

        .get_ae_params 	    =	h63p_mipi_get_ae_params,
        .get_awb_params 	=	h63p_mipi_get_awb_params,
        .get_iq_params 	    =	h63p_mipi_get_iq_params,

        .sleep 		        =	h63p_mipi_sleep,
        .wakeup 		    =	h63p_mipi_wakeup,
        .write_reg 		    =	h63p_mipi_W_Reg,
        .read_reg 		    =	h63p_mipi_R_Reg,
    }
};


#endif


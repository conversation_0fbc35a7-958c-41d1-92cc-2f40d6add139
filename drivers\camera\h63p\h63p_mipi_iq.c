#include "isp_alg.h"
#include "h63p_mipi_iq.h"
static u8 gamma_curve[256] = {
#if 1
    0,   5,   9,  13,  16,  18,  21,  23,  25,  28,  30,  32,  34,  35,  37,  39,
    41,  42,  44,  46,  47,  49,  51,  52,  54,  55,  57,  58,  59,  61,  62,  64,
    65,  66,  68,  69,  70,  72,  73,  74,  75,  77,  78,  79,  80,  82,  83,  84,
    85,  86,  87,  89,  90,  91,  92,  93,  94,  95,  96,  98,  99, 100, 101, 102,
    103, 104, 105, 106, 107, 108, 109, 110, 111, 112, 113, 114, 115, 116, 117, 118,
    119, 120, 121, 121, 122, 123, 124, 125, 126, 127, 128, 129, 130, 131, 132, 132,
    133, 134, 135, 136, 137, 138, 138, 139, 140, 141, 142, 143, 143, 144, 145, 146,
    146, 147, 148, 149, 149, 150, 151, 152, 152, 153, 154, 155, 155, 156, 157, 157,
    158, 159, 159, 160, 161, 162, 162, 163, 164, 164, 165, 166, 166, 167, 168, 168,
    169, 169, 170, 171, 171, 172, 173, 173, 174, 175, 175, 176, 177, 177, 178, 178,
    179, 180, 180, 181, 182, 182, 183, 184, 184, 185, 185, 186, 187, 187, 188, 189,
    189, 190, 191, 191, 192, 193, 193, 194, 195, 195, 196, 197, 197, 198, 199, 199,
    200, 201, 201, 202, 203, 203, 204, 205, 206, 206, 207, 208, 209, 209, 210, 211,
    212, 212, 213, 214, 215, 215, 216, 217, 218, 219, 219, 220, 221, 222, 223, 224,
    224, 225, 226, 227, 228, 229, 230, 231, 232, 232, 233, 234, 235, 236, 237, 238,
    239, 240, 241, 242, 243, 244, 245, 246, 247, 248, 249, 251, 252, 253, 254, 255
#else
    0,   1,   2,   3,   4,   6,   9,  12,  15,  19,  24,  28,  34,  39,  44,  50,
    55,  60,  64,  68,  72,  76,  79,  82,  85,  88,  91,  93,  95,  98, 100, 102,
    104, 106, 108, 109, 111, 113, 114, 116, 118, 119, 121, 122, 124, 125, 127, 128,
    129, 131, 132, 134, 135, 136, 138, 139, 140, 141, 143, 144, 145, 146, 148, 149,
    150, 151, 152, 154, 155, 156, 157, 158, 159, 161, 162, 163, 164, 165, 166, 167,
    168, 169, 170, 171, 172, 173, 174, 176, 177, 178, 179, 180, 180, 181, 182, 183,
    184, 185, 186, 187, 188, 189, 190, 191, 191, 192, 193, 194, 195, 196, 196, 197,
    198, 199, 199, 200, 201, 202, 202, 203, 204, 204, 205, 206, 206, 207, 207, 208,
    209, 209, 210, 210, 211, 211, 212, 212, 213, 213, 214, 214, 215, 215, 216, 216,
    217, 217, 218, 218, 219, 219, 220, 220, 221, 221, 222, 222, 223, 223, 224, 224,
    225, 225, 226, 226, 227, 227, 228, 228, 229, 229, 230, 230, 231, 231, 232, 232,
    233, 233, 233, 234, 234, 235, 235, 236, 236, 236, 237, 237, 238, 238, 239, 239,
    239, 240, 240, 240, 241, 241, 242, 242, 242, 243, 243, 243, 244, 244, 244, 245,
    245, 245, 246, 246, 246, 246, 247, 247, 247, 248, 248, 248, 248, 249, 249, 249,
    249, 250, 250, 250, 250, 251, 251, 251, 251, 251, 252, 252, 252, 252, 252, 253,
    253, 253, 253, 253, 253, 254, 254, 254, 254, 254, 254, 254, 255, 255, 255, 255,
#endif
};

static s16 hsv_lut[] = {
#if 0
    0, 256,  //0
    0, 256,  //15
    0, 256,  //30
    2, 350,  //45
    3, 400,  //60
    5, 400,  //75
    5, 400,  //90
    5, 400,  //105
    5, 400,  //120
    5, 400,  //135
    5, 400,  //150
    3, 350,  //165
    0, 256,  //180
    0, 256,  //195
    0, 256,  //210
    0, 256,  //225
    0, 256,  //240
    0, 256,  //255
    0, 256,  //270
    0, 256,  //285
    0, 256,  //300
    0, 256,  //315
    0, 256,  //330
    0, 256   //345
#else
    0, 256,  //0
    0, 256,  //15
    0, 256,  //30
    50, 500,  //45
    50, 500,  //60
    50, 500,  //75
    50, 500,  //90
    50, 500,  //105
    50, 500,  //120
    50, 500,  //135
    50, 500,  //150
    0, 256,  //165
    0, 256,  //180
    0, 256,  //195
    0, 256,  //210
    0, 256,  //225
    0, 256,  //240
    0, 256,  //255
    0, 256,  //270
    0, 256,  //285
    0, 256,  //300
    0, 256,  //315
    0, 256,  //330
    0, 256   //345
#endif
};

static isp_iq_params_t h63p_mipi_iq_params = {
    .blc = {
        0, 0, 0, 0,
    },

    .lsc = {
        0, 0, 0, 0, 0, 0, 0, 0,
    },
    .wdr = {0, 0},
    .drc_hist = {300, 30, 128, 192,  0, 100, 0, 100, 0, 100, 0, 100},
    .ltmo = {0, 0, 250},

    .adj = {
        0x100, 0x100, 0x100, 0, 0, 0, 512, 0,
        15, 30, 200, 230, 50, 0 // chroma supress
    },

    .gamma = NULL,//gamma_curve,


    .ccm = {
        0x100,   0x00,   0x00,   0x00,
        0x00,   0x100,   0x00,   0x00,
        0x00,    0x00,   0x100,   0x00,
    },

    .hsv = {
        0, 0, 256, 5, 15, 1, NULL
    },


    .bnr =   {
        1, 15, 0, 12, 20,
        {384, 384, 256, 127, 127, 127, 127},
        {64, 96, 127, 127, 127, 127, 127}
    },
    .dpc =   {(3 << 4), (4 << 4), (5 << 4)},
    .tnr =   {1, 20, 48, 5, 0, 25, 2, 1023, 1023},
    .nr =    {
        1, 1, 23, 48, 1, 0,
        960, 540, 39, 15, 15 //radial compesation
    },
    .shp =   {
        1, 15, 1, 127, 50, 50, 50, 50, 8, 32, 8, 32, 8, 8, 20, 1, 10,
        {30, 50, 80, 127, 127, 127, 127, 127},
        //{-1, -1, -1, -1, -2, -2, -1, -2, 32},
        //{0, -1, -2, -1, 0, 2, -2, 2, 8},
        {0, 0, 0, 0, 0, 0, -2, 1, 2},
        {0, 0, -2, 0, 0, 1, 0, 0, 2},
        0, 0, 1, 15, 15
    },
    .cnr =   {1, 255, 78},

    .md_wms = {100, 130, 180, 250, 400},
    .md_level = 3,
};

void *h63p_mipi_get_iq_params()
{
    return (void *)&h63p_mipi_iq_params;
}

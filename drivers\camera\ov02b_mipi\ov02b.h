#ifndef _OV02B_MIPI_H_
#define _OV02B_MIPI_H_

#include "typedef.h"
#include "isp_dev.h"
#include "isp_alg.h"

// 定义OV02B输出分辨率
#define OV02B_MIPI_OUTPUT_W 1280    //1280*720
#define OV02B_MIPI_OUTPUT_H 720

#define OV02B_FPS_VARIABLE    0

// 函数声明
s32 ov02b_mipi_check(u8 isp_dev, u32 reset_gpio, u32 pwdn_gpio);
s32 ov02b_mipi_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);
s32 ov02b_mipi_set_output_size(u16 *width, u16 *height, u8 *frame_freq);
s32 ov02b_mipi_power_ctl(u8 isp_dev, u8 is_work);

void ov02b_mipi_sleep();
void ov02b_mipi_wakeup();
void ov02b_mipi_wr_reg(u16 addr, u16 val);
u16 ov02b_mipi_rd_reg(u16 addr);

#endif 
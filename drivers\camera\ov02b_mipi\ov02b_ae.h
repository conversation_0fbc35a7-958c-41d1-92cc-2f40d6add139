#ifndef __OV02B_MIPI_AE_H__
#define __OV02B_MIPI_AE_H__

#include "typedef.h"

#define AE_MAX_EV                   (9102000)    // 最大曝光值，用于非常明亮环境下的曝光控制
#define AE_MIN_EV                   (200)          // 最小曝光值，用于非常暗环境下的曝光控制
#define AE_INIT_EV                  ((1 << 10) * 16) // 初始曝光值，系统启动时的默认曝光设置，等于16384

#define AE_CURVE_TYPE               AE_CURVE_50HZ // 曝光曲线类型，50Hz对应国内交流电频率，防止屏幕闪烁

#define AE_TARGET_LUMA              50           // 目标亮度值，AE算法会尝试将图像平均亮度调整到这个值
 
#define AE_PHASE_COMP               3            // 相位补偿值，调整AE算法中的相位计算

#define AE_LUMA_SMOOTH_NUM          3            // 亮度平滑帧数，对多帧亮度取平均，减少闪烁

#define AE_CONVER_H                 8            // 高亮收敛速度，值越大收敛越快(从暗到亮)
#define AE_CONVER_L                 8            // 低亮收敛速度，值越大收敛越快(从亮到暗)

#define AE_DIVER_H                  8            // 高亮发散阈值，大于此值开始发散调整(过亮时)
#define AE_DIVER_L                  8            // 低亮发散阈值，小于此值开始发散调整(过暗时)

#define AE_RATIO_MAX_H              2048         // 最大高亮调整比例，限制单次调整幅度
#define AE_RATIO_MAX_L              512          // 最大低亮调整比例，限制单次调整幅度

#define AE_RATIO_MAX2_H             1086         // 次级高亮调整比例，微调时使用
#define AE_RATIO_MAX2_L             970          // 次级低亮调整比例，微调时使用

#define AE_RATIO_SLOPE              450          // 调整比例斜率，控制AE响应曲线斜率

#define AE_EXPLINE_TH               0            // 曝光行阈值，控制何时切换曝光模式

#define AE_INIT_WEIGHT_TYPE         AE_WIN_WEIGHT_AVG // 初始权重类型，AVG表示全画面平均测光

typedef struct {
    s32 ev;
    u32 exp_time;
    u32 gain;
} ae_param_t;

typedef struct {
    ae_param_t *param;
    u16 number;
} ae_table_t;

#endif 
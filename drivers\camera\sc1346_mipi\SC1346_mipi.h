#ifndef __SC1346_DVP_H__
#define __SC1346_DVP_H__

#include "typedef.h"

#define SC1346_MIPI_OUTPUT_W    1280
#define SC1346_MIPI_OUTPUT_H    720

s32 SC1346_dvp_set_output_size(u16 *width, u16 *height, u8 *freq);
s32 SC1346_dvp_power_ctl(u8 isp_dev, u8 is_work);

s32 SC1346_dvp_init(u8 isp_dev, u16 *width, u16 *height, u8 *format, u8 *frame_freq);


void SC1346_dvp_sleep();
void SC1346_dvp_wakeup();
void SC1346_dvp_W_Reg(u16 addr, u16 val);
u16 SC1346_dvp_R_Reg(u16 addr);


#endif




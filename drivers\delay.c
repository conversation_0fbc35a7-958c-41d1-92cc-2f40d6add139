#include "delay.h"
#include "jtime.h"
#include "clock.h"
void mdelay(u32 ms)
{
    for (u32 i = 0; i < ms; i++) {
        udelay(1000);
    }
}
void udelay(u32 usec)
{
    JL_TIMER0->CON = BIT(14);
    JL_TIMER0->PRD = RC_CLOCK * usec / 1000000 / 4; //1us
    JL_TIMER0->CON = BIT(14) | (0b0001 << 4) | (0b0011 << 10) | BIT(0);//选择RC时钟源，并4分频
    JL_TIMER0->CNT = 0;
    while (!(JL_TIMER0->CON & BIT(15)));
    JL_TIMER0->CON = BIT(14);
}

void delay(volatile u32 t)
{
    while (t--) {
        asm("nop");
    }
}
void delay_ms(u32 ms)
{
    delay(3000 * ms);
}

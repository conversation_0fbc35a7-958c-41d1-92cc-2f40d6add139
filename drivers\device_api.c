#include "device.h"
#include "printf.h"

#include "gpio.h"
#include "iic.h"

extern struct dev_node device_node_begin[];
extern struct dev_node device_node_end[];


#define list_for_each_device(p) \
    for (p = device_node_begin; p < device_node_end; p++)


int devices_init()
{
    int err;
    struct dev_node *p;
    const struct device_operations *ops;
    /* printf("0x%x\n",device_table); */
    _devices_init();

    list_for_each_device(p) {
        ops = p->ops;
        if (ops && ops->init) {
            err = ops->init(p, p->priv_data);
            if (err) {
                ASSERT(0, "device: %s init_err: %d\n", p->name, err);
            }
        }
    }
    return 0;
}


void *dev_open(const char *name, void *arg)
{
    int i;
    int err;
    struct dev_node *p;
    struct device *device = NULL;

    device = _dev_open(name, arg);
    if (device) {
        printf("open %s sucess\n", name);
        return  device;
    }


    list_for_each_device(p) {
        if (!strcmp(p->name, name)) {
            err = p->ops->open(p, &device, arg);
            if (err) {
                return NULL;
            }

            device->ops = p->ops;
            atomic_inc(&device->ref);

            return device;
        }
    }

    return NULL;
}

bool dev_online(const char *name)
{
    struct dev_node *p;


    list_for_each_device(p) {
        if (!strcmp(p->name, name)) {
            if (p->ops->online) {
                return p->ops->online(p);
            }
            break;
        }
    }

    return false;
}

#if 0

int dev_read(void *_device, void *buf, u32 len)
{
    int rlen = 0;
    struct device *device = (struct device *)_device;

    if (device->ops->read) {
        rlen = device->ops->read(device, buf, len, 0);
    }

    return rlen;
}


int dev_write(void *_device, void *buf, u32 len)
{
    int wlen = 0;
    struct device *device = (struct device *)_device;

    if (device->ops->write) {
        wlen = device->ops->write(device, buf, len, 0);
    }

    return wlen;
}



int dev_ioctl(void *_device, int cmd, u32 arg)
{
    struct device *device = (struct device *)_device;

    if (device->ops->ioctl) {
        return device->ops->ioctl(device, cmd, arg);
    }

    return -EINVAL;
}


int dev_close(void *_device)
{
    struct device *device = (struct device *)_device;

    if (atomic_dec_and_test(&device->ref)) {
        if (device->ops->close) {
            return device->ops->close(device);
        }
    }

    return 0;
}

int dev_bulk_read(void *_device, void *buf, u32 sector, u32 sector_num)
{
    int rlen = 0;
    struct device *device = (struct device *)_device;
    if (device->ops->bulk_read) {
        rlen = device->ops->bulk_read(device, buf, sector_num, sector);
    }
    return rlen;
}

int dev_bulk_write(void *_device, void *buf, u32 sector, u32 sector_num)
{
    int wlen = 0;
    struct device *device = (struct device *)_device;
    if (device->ops->bulk_write) {
        wlen = device->ops->bulk_write(device, buf, sector_num, sector);
    }
    return wlen;
}


int dev_init(char *name, void *data)
{
    int err;
    struct dev_node *p;
    const struct device_operations *ops;

    list_for_each_device(p) {
        ops = p->ops;
        if ((strcmp(name, p->name) == 0) && ops) {
            err = ops->init(p, data);
            if (err) {
                ASSERT(0, "device: %s init_err: %d\n", p->name, err);
            }
        }
    }

    return 0;
}

int dev_uninit(char *name)
{
    int err;
    struct dev_node *p;
    const struct device_operations *ops;

    list_for_each_device(p) {
        ops = p->ops;
        if ((strcmp(name, p->name) == 0) && ops) {
            err = ops->uninit(p);
            if (err) {
                ASSERT(0, "device: %s init_err: %d\n", p->name, err);
            }
        }
    }

    return 0;
}

#endif


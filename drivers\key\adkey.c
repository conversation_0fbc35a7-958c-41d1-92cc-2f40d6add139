#include "app_config.h"
#include "adkey.h"
#include "key_driver.h"
#include "gpio.h"

#ifdef CONFIG_ADKEY_ENABLE

static struct adkey_platform_data *__this = NULL;


static int adkey_init(void *_key, void *arg)
{
    struct key_driver *key = _key;
    __this = (struct adkey_platform_data *)arg;
    if (!__this) {
        return -1;
    }

    gpio_direction_input(__this->io);
    gpio_set_pull_down(__this->io, 0);
    gpio_set_pull_up(__this->io, 1);
    gpio_set_die(__this->io, 0);

    adc_scan_open(__this->ad_channel);

    key->prev_value = 0;
    key->last_key = KEY_NONE;
    key->base_cnt = __this->base_cnt;
    key->long_cnt = __this->long_cnt;
    key->hold_cnt = __this->hold_cnt;
    key->scan_time = __this->scan_time;

    return 0;
}

static u16 adkey_get_value(void *_key)
{
    struct key_driver *key = _key;
    int i;
    struct adkey_value_table *table = &__this->table;
    u16 key_value = adc_scan_getvalue(__this->ad_channel);

    /* printf("adkey: %x\n", adkey_scan.value); */

    for (i = 0; i < ADKEY_MAX_NUM; i++) {
        if (key_value <= table->ad_value[i]) {
            /* printf("adkey %d", i); */
            return table->key_value[i];
        }
    }

    return KEY_NONE;
}

static const struct key_driver_ops adkey_driver_ops = {
    .init 		= adkey_init,
    .get_value 	= adkey_get_value,
};

REGISTER_KEY_DRIVER(adkey_driver) = {
    .name = "adkey",
    .ops = &adkey_driver_ops,
};

#endif

#include "iokey.h"
#include "key_driver.h"
#include "gpio.h"


#if CONFIG_IOKEY_ENABLE
static struct iokey_platform_data *__this = NULL;


static int iokey_init(void *_key, void *arg)
{
    struct key_driver *key = _key;
    __this = (struct iokey_platform_data *)arg;
    if (!__this) {
        return -1;
    }

    for (int i = 0; i < IOKEY_MAX_NUM; i++) {
        gpio_direction_input(__this->table.io[i]);
        gpio_set_pull_down(__this->table.io[i], 0);
        gpio_set_pull_up(__this->table.io[i], 1);
        gpio_set_die(__this->table.io[i], 1);
    }

    key->prev_value = 0;
    key->last_key = KEY_NONE;
    key->base_cnt = __this->base_cnt;
    key->long_cnt = __this->long_cnt;
    key->hold_cnt = __this->hold_cnt;
    key->scan_time = __this->scan_time;

    return 0;
}

static u16 iokey_get_value(void *_key)
{
    struct key_driver *key = _key;
    struct iokey_value_table *table = &__this->table;

    for (int i = 0; i < IOKEY_MAX_NUM; i++) {
        if (!gpio_read(table->io[i])) {
            return table->key_value[i];
        }
    }
    return KEY_NONE;
}

static const struct key_driver_ops iokey_driver_ops = {
    .init 		= iokey_init,
    .get_value 	= iokey_get_value,
};

REGISTER_KEY_DRIVER(iokey_driver) = {
    .name = "iokey",
    .ops = &iokey_driver_ops,
};

#endif

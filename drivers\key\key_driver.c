#include "key_driver.h"
#include "app_msg.h"
#include "hw_timer.h"


int __attribute__((weak)) key_event_remap(struct sys_msg *msg)
{
    return true;
}

static void key_driver_scan(void *_key)
{
    u8 value;
    struct sys_msg msg = {0};
    struct key_driver *key = (struct key_driver *)_key;

    msg.type = SYS_MSG_EVENT;

    value = key->ops->get_value(key);
    if (value != key->prev_value && key->base_cnt) {
        key->filter_cnt = 0;
        key->prev_value = value;
        return;
    }
    if (key->filter_cnt < key->base_cnt) {
        key->filter_cnt++;
        return;
    }

    if (value == key->last_key) {
        if (value == KEY_NONE) {
            return;
        }

        if (++key->press_cnt == key->long_cnt) {
            msg.event = MSG_KEY_LONG;
        } else if (key->press_cnt == key->hold_cnt) {
            msg.event = MSG_KEY_HOLD;
            key->press_cnt = key->long_cnt;
        } else {
            return;
        }
        msg.value = value;
    } else {
        if (value != KEY_NONE) {
            key->press_cnt = 0;
            key->last_key = value;
            return;
        }
        if (key->press_cnt >= key->long_cnt) {
            msg.event = MSG_KEY_UP;
        } else {
            msg.event = MSG_KEY_CLICK;
        }
        msg.value = key->last_key;
        key->last_key = KEY_NONE;
    }

    printf("key: %d %d\n", msg.event, msg.value);
    if (key_event_remap(&msg)) {
        sys_msg_post(&msg);
    }
}




static int key_driver_init(struct dev_node *node,  void *arg)
{
    int err;
    struct key_driver *key;

    list_for_each_key_dirver(key) {
        if (!strcmp(key->name, node->name)) {
            if (key->ops->init) {
                err = key->ops->init(key, arg);
                ASSERT(err == 0, "key_driver_init faild\n");
            }
            sys_timer_add(key, key_driver_scan, key->scan_time);
        }
    }


    return 0;
}

const struct device_operations key_dev_ops = {
    .init = key_driver_init,
};



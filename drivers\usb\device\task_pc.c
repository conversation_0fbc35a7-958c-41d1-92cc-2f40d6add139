/**
 * @file task_pc.c
 * @brief 从机模式
 * <AUTHOR>
 * @version 1.0.0
 * @date 2020-02-29
 */
#define USB_DBG_LEV 0
#include "generic/includes.h"
#include "core.h"
#include "app_config.h"
#include "app_msg.h"
#include "usb/usb.h"
#include "usb/usb_config.h"
#include "usb/device/usb_stack.h"
#include "usb/device/uac_audio.h"
#include "usb/device/msd.h"
#include "usb/device/slave_uvc.h"
#include "usb/device/descriptor.h"
#include "park_det.h"
#include "video.h"
#include "clock.h"
#include "efuse.h"
#include "cpu.h"
#include "isp_scenes.h"

#ifdef USE_FLASH_CFG_EN
#include "e2bApi.h"
#endif

#if TCFG_USB_SLAVE_ENABLE

static usb_dev usbfd = 0;//SEC(.usb_g_bss);

#if TCFG_USB_SLAVE_MSD_ENABLE
static u8 msd_in_task;
static u8 msd_run_reset;
static struct usb_device_t *g_usb_device;
#endif

struct ispt_param {
    u8 *buf;
    u32 buf_size;
    void *(*get_video_device)(void);
    //void *usb;
};

static const u8 sDeviceDescriptor[] = { //<Device Descriptor
    USB_DT_DEVICE_SIZE,      // bLength: Size of descriptor
    USB_DT_DEVICE,       // bDescriptorType: Device
    0x10, 0x01,     // bcdUSB: USB 1.1
    0x00,       // bDeviceClass: none
    0x00,       // bDeviceSubClass: none
    0x00,       // bDeviceProtocol: none
    EP0_SETUP_LEN,//EP0_LEN,      // bMaxPacketSize0: 8/64 bytes
    'J', 'L',     // idVendor: 0x4a4c - JL
    'U', 'A',     // idProduct: chip id
    0x00, 0x01,     // bcdDevice: version 1.0
    0x01,       // iManufacturer: Index to string descriptor that contains the string <Your Name> in Unicode
    0x02,       // iProduct: Index to string descriptor that contains the string <Your Product Name> in Unicode
    0x00,       // iSerialNumber: none
    0x01        // bNumConfigurations: 1
};
static const u8 videoStringDescriptor[] = {
    32,         //该描述符的长度为32字节
    0x03,       //字符串描述符的类型编码为0x03
    'D', 0x00, //d
    'V', 0x00, //v
    '2', 0x00, //2
    '0', 0x00, //0
    ' ', 0x00, //

    'U', 0x00, //u
    'S', 0x00, //s
    'B', 0x00, //b
    ' ', 0x00, //
    'C', 0x00, //c
    'A', 0x00, //a
    'M', 0x00, //m
    'E', 0x00, //e
    'R', 0x00, //r
    'A', 0x00, //a
};

const u8 AudioStringDescriptor[20] = {
    20,         //该描述符的长度为20字节
    0x03,       //字符串描述符的类型编码为0x03
    'U', 0x00, //U
    'S', 0x00, //S
    'B', 0x00, //B
    ' ', 0x00, //
    'A', 0x00, //A
    'u', 0x00, //u
    'd', 0x00, //d
    'i', 0x00, //i
    'o', 0x00, //o
};

static void get_device_descriptor(u8 *ptr)
{
#ifdef USE_FLASH_CFG_EN
    u8 *user_descriptor = get_eeprom_usb_dev_desc();
#endif

#ifdef USE_FLASH_CFG_EN
    if (user_descriptor[0] == USB_DT_DEVICE_SIZE) {
        memcpy(ptr, user_descriptor, USB_DT_DEVICE_SIZE);
    } else {
#endif
        memcpy(ptr, sDeviceDescriptor, USB_DT_DEVICE_SIZE);
        printf(">>>>=usb mode:%d\n", usb_full_speed_mode());
        if (!usb_full_speed_mode()) {
            // bcdUSB: USB 2.0
            ptr[2] = 0x00;
            ptr[3] = 0x02;
        }
#ifdef USE_FLASH_CFG_EN
    }
#endif
}
static void uvc_set_camera_reso(void);

static void go_mask_usb_update(void)
{
    strcpy((char *)(0x20000 - 0x10), "usb_update_mode");//usb uprgade
    JL_CLOCK->PWR_CON |= BIT(4);
    while (1);
}
static void go_uboot_usb_update(void)
{
    strcpy((char *)(0x20000 - 0x10), "usb_uboot_mode");//usb uprgade
    JL_CLOCK->PWR_CON |= BIT(4);
    while (1);
}
static u8 isp_tools_buf[1352 * 4] ALIGNE(4); /* 与isp效果buf复用 */
u8 *get_isp_tool_buf(void)
{
    return isp_tools_buf;
}
static int uvc_device_mode(usb_dev usb_id, const u32 class, u8 class_index)
{
#if TCFG_USB_SLAVE_UVC_ENABLE
    if ((class & VIDEO_CLASS) == VIDEO_CLASS) {
        printf("add desc uvc");
        usb_add_desc_config(usb_id, class_index++, uvc_desc_config);
        user_uvc_register(usb_id, NULL);

    }
#endif
#if TCFG_USB_SLAVE_MSD_ENABLE
    if ((class & MASSSTORAGE_CLASS) == MASSSTORAGE_CLASS) {
        printf("add desc msd");
        usb_add_desc_config(usb_id, class_index++, msd_desc_config);
        msd_register(usb_id);
    }
#endif

    return 0;
}
static void go_usb_debug(void)
{
    static struct uvc_video_data uvc_video_info;
    struct sys_msg video_msg = {0};
    usb_stop();
    usb_start(MASSSTORAGE_CLASS);
    set_isp_debug_state(1);

#ifndef CONFIG_USE_USER_UVC_DRIVER_EN  // 使用mask uvc 驱动
    usb_device_mode(usbfd, MASSSTORAGE_CLASS | VIDEO_CLASS);
#else
    //使用外部 uvc 驱动
    uvc_device_mode(usbfd, VIDEO_CLASS, 1);
#endif
    uvc_set_camera_reso(); //设置分辨率

    struct ispt_param isp_cfg = {0};
    extern void *get_video_device();
    isp_cfg.get_video_device = get_video_device;
    isp_cfg.buf = get_isp_tool_buf() + 1024;
    isp_cfg.buf_size = 2 * 1024;
    void isp_tool_init(struct ispt_param * cfg);
    isp_tool_init(&isp_cfg);

}


static int uvc_processing_unit_response(struct uvc_unit_ctrl *ctl_req)
{
    /* printf("=======>> request : 0x%x, unit : 0x%x\n", ctl_req->request, ctl_req->unit); */
    /* put_buf(ctl_req->data, ctl_req->len); */
    int value = 0;
    int msg[32];
    int err = 0;

    if (ctl_req->unit == 0x0a && ctl_req->request == 0x01) {
        const u8 upgd[] = {'u', 'p', 'g', 'd'};
        const u8 upgc[] = {'u', 'p', 'g', 'c'};
        static u32 pos = 0;
        //UPGRADE
        /* printf_buf(ctl_req->data, ctl_req->len); */
        /* printf("in pos %d\n", pos); */
        /* printf("ctl_req->data[0] 0x%x, upgd[pos] 0x%x %c\n", ctl_req->data[0], upgd[pos], upgd[pos]); */
        if (ctl_req->data[0] == upgd[pos]) {
            pos++;
            if (pos == sizeof(upgd)) {
                pos = 0;
                puts("<<<<<<enter upgrade>>>>>>>>\n");
                if (get_chip_version()) {
                    go_mask_usb_update();
                } else {
                    //A版
                    go_uboot_usb_update();
                }
                return 1;
            }
        } else if (ctl_req->data[0] == upgc[pos]) {
            pos++;
            if (pos == sizeof(upgc)) {
                pos = 0;
                puts("<<<<<<enter debug>>>>>>>>\n");
                go_usb_debug();
                return 1;
            }
        } else {
            pos = 0;
        }
    } else if (ctl_req->unit == 0x01/*0xaa*/ && ctl_req->request == 0x01) {
        switch (ctl_req->request) {
         case 0x01:
       ////    printf("<<<<<<enter11111:%d>>>>>>>>\n",ctl_req->data[0]);
          //// extern int user_flush_isp_cfg(u8 parm);
           if(ctl_req->data[0] ==1)
           {
              //// switch scancode
              printf("<<<<<<switch scancode mode>>>>>>>>\n");
              user_flush_isp_cfg(1);
           }else if(ctl_req->data[0] ==0)
           {
             printf("<<<<<<switch normal mode>>>>>>>>\n");
             user_flush_isp_cfg(0);
           }
            break;
        }
    } else if (ctl_req->unit == 0x01) {
        //parking
        if (ctl_req->request == 0x81 || ctl_req->request == 0x87) {
        ////    puts("<<<<<<enter222222>>>>>>>>\n");
            value = 3 ;////get_parking_detect_value();
            memcpy(ctl_req->data, &value, ctl_req->len);
/*by frank 20221217
           value = get_parking_detect_value();

#ifdef USE_FLASH_CFG_EN
            if (value == 0) {
                value = get_eeprom_unpark_cmd();
            } else {
                if (get_eeprom_park_cmd()) {
                    value = get_eeprom_park_cmd();
                }
            }
#endif

            memcpy(ctl_req->data, &value, ctl_req->len);
*/
            return 0;
        } else {
            ctl_req->len = 0;
            return 1;
        }
    }

    return 1;
}
u8 is_support_720_mode(void)
{
    struct uvc_reso_info *reso_info = (struct uvc_reso_info *)&board_jpg_fmt;
    struct uvc_reso *reso;
    for (u8 i = 0; i < reso_info->num; i++) {
        reso = &reso_info->reso[i];
        if (reso->width == 1280 && reso->height == 720) {
            return 1;
        }
    }
    return 0;
}
#if TCFG_USB_SLAVE_MSD_ENABLE
void usb_routine()
{
    msd_in_task = 1;
    USB_MassStorage(g_usb_device);
    if (msd_run_reset) {
        msd_reset(g_usb_device, 0);
        msd_run_reset = 0;
    }
    msd_in_task = 0;
}

static void usb_msd_wakeup(struct usb_device_t *usb_device)
{
    struct sys_msg msg = {0};
    g_usb_device = usb_device;
    msg.type = SYS_MSG_EVENT;
    msg.event = MSG_USB_TASK;
    sys_msg_post(&msg);
}
static void usb_msd_reset_wakeup(struct usb_device_t *usb_device, u32 itf_num)
{
    g_usb_device = usb_device;
    if (msd_in_task) {
        msd_run_reset = 1;
    } else {
#if TCFG_USB_SLAVE_MSD_ENABLE
        msd_reset(usb_device, 0);
#endif
    }
}
#endif

static int user_setup(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static void uvc_set_camera_reso(void)
{
#if TCFG_USB_SLAVE_MSD_ENABLE
#if (defined SDC_EN)
    msd_ex_register_disk("sd0", NULL);
#endif

    msd_set_wakeup_handle(usb_msd_wakeup);
    msd_set_reset_wakeup_handle(usb_msd_reset_wakeup);
#endif

#if TCFG_USB_SLAVE_UVC_ENABLE
    extern int uvc_video_open(int idx, int fmt, int frame_id, int fps, u16 width, u16 height);
    extern int uvc_video_req_buf(int idx, void *buf, u32 len, u32 * frame_end);
    extern int uvc_video_req_buf2(int idx, void **buf, u32 len, u32 * frame_end);
    extern int uvc_video_close(int idx);

    struct usb_camera camera = {0};
    camera.video_open = uvc_video_open;
    camera.video_reqbuf = uvc_video_req_buf;
    camera.video_reqbuf2 = uvc_video_req_buf2;
    camera.video_close = uvc_video_close;
    camera.processing_unit_response = uvc_processing_unit_response;
#if UVC_FORMAT_MJPG
    camera.jpg_info = &board_jpg_fmt;
#endif
#ifdef CONFIG_YUYV_ENABLE
#if UVC_FORMAT_I420 || UVC_FORMAT_YUY2
    camera.yuv_info = &board_yuv_fmt;
#endif
#endif
#ifndef CONFIG_USE_USER_UVC_DRIVER_EN
    uvc_set_camera_info(usbfd, &camera);
#else
    user_uvc_set_camera_info(usbfd, &camera);
#endif
#endif
}

const u8 *usb_get_string_desc(u32 id)
{
    const u8 *pstr = NULL;
#if TCFG_USB_SLAVE_UVC_ENABLE
    if (id == VIDEO_STR_INDEX) {
#ifdef USE_FLASH_CFG_EN
        u8 *user_string_descriptor = get_eeprom_usb_string_desc();
        if (user_string_descriptor[0] != 0) {
            pstr = user_string_descriptor;
        } else {
#endif
            pstr = videoStringDescriptor;
#ifdef USE_FLASH_CFG_EN
        }
#endif
    }
#endif
#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
    if (id == MIC_STR_INDEX || id == SPEAKER_STR_INDEX) {
        pstr = AudioStringDescriptor;
    }
#endif
    return pstr;
}
static int user_setup(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 tmp = 0;
    u32 tx_len = 0;
    u8 *tx_payload = usb_get_setup_buffer(usb_device);
    u32 recip = req->bRequestType & USB_RECIP_MASK;
    struct sys_msg msg = {0};

    /* printf_buf(req, sizeof(struct usb_ctrlrequest)); */

    switch (req->bRequest) {
    case USB_REQ_GET_DESCRIPTOR: {
        switch (HIBYTE(req->wValue)) {
        case USB_DT_STRING:
            if (LOBYTE(req->wValue) == 0) { //langid
                /* get_language_str(tx_payload); */
            } else if (LOBYTE(req->wValue) == 1) {//Manufacturer
                /* get_manufacture_str(tx_payload); */
            } else if (LOBYTE(req->wValue) == 2) {//product
                /* get_product_str(tx_payload); */
            } else if (LOBYTE(req->wValue) == 3) {//serial
                /* get_iserialnumber_str(tx_payload); */
            } else if (LOBYTE(req->wValue) == 0xee) {//string_ee
                /* get_string_ee(tx_payload); */
            } else {
                u8 *str_desc = (u8 *)usb_get_string_desc(LOBYTE(req->wValue));
                if (str_desc != NULL) {
                    memcpy(tx_payload, str_desc, str_desc[0]);
                } else {
                    puts(">> error string desc\n");
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    tx_payload[0] = 0;
                }
                //list_foreach
                tx_len = tx_payload[0];
            }
            break;
        case USB_DT_DEVICE:
            get_device_descriptor(tx_payload);
#if 1
            {
                int i;
                int counter = 0;
                for (i = 0; i < 8; i++) {
                    //为了兼容windows7等老电脑，配置成不同类时应该使用不同pid
                    if (usb_device->wDeviceClass & BIT(i)) {
                        tx_payload[11] += i + 1;
                        counter++;
                    }
                }
                //为了兼容macOS，必须设成复合设备，否则macbook会不认摄像头
                /* if (counter > 1) { */
                if (1) {
                    tx_payload[0x4] = 0xef; //device class should be Multi-interface Function 0xEF
                    tx_payload[0x5] = 0x2; //device subclass should be Multi-interface Function 2
                    tx_payload[0x6] = 0x1; //device protocol should be Multi-interface Function 1
                }
            }
#endif
            tx_len = USB_DT_DEVICE_SIZE;
            break;
        case USB_DT_CONFIG:
            tx_len = set_descriptor(0,
                                    usb_device->wDeviceClass,
                                    tx_payload,
                                    1024);
            break;
        }
        if (tx_len) {
            usb_set_data_payload(usb_device, req, tx_payload, tx_len);
            return 1;
        }
    }
    break;
    case USB_REQ_CLEAR_FEATURE:
        if (usb_device->bDeviceStates == USB_CONFIGURED) {
#if !UVC_ISO_MODE
            //Halt Endpoint,
            if (LOBYTE(req->wValue) == 0 && ((LOBYTE(req->wIndex) & 0xf) == UVC_STREAM_EP_IN)) {
                msg.type = SYS_MSG_EVENT;
                msg.event = MSG_UVC_CLOSE;
                sys_msg_post(&msg);
            }
#endif
        }

        break;
    case USB_REQ_SET_CONFIGURATION: {
        u8 bDevConfig = LOBYTE(req->wValue);
        if (bDevConfig == 1) {
            /* start_dynamic_lrc_trim(); //开启usb auto trim */
        }
    }
    break;

    }
    if (recip == USB_RECIP_ENDPOINT) {
        switch (req->bRequestType & USB_TYPE_MASK) {
        case USB_TYPE_CLASS:
            switch (req->bRequest) {
            case UAC_SET_CUR:
#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
                return uac_setup_endpoint(usb_device, req);
#endif
                break;
            }
            break;
        }
    }

#ifdef VGA_CAR_CAM_CASE
    int car_cam_user_setup(struct usb_device_t *usb_device, struct usb_ctrlrequest * req);
    car_cam_user_setup(usb_device, req);
#endif

    //用户uvc扩展单元协议通信
    int uvc_user_extension_unit(struct usb_device_t *usb_device, struct usb_ctrlrequest * req);
    if (recip == USB_RECIP_INTERFACE) {
        switch (req->bRequestType & USB_TYPE_MASK) {
        case USB_TYPE_CLASS:
            switch (HIBYTE(req->wIndex)) {
            case UVC_EXT_TERMINALID1:
            case UVC_EXT_TERMINALID2:
                return uvc_user_extension_unit(usb_device, req);
            }
            break;
        }
    }
    return 0;
}
static int uac_device_mode(usb_dev usb_id, const u32 class, u8 class_index)
{

#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
    if ((class & AUDIO_CLASS) == AUDIO_CLASS) {
        usb_log_info("add audio desc");
        usb_add_desc_config(usb_id, class_index++, uac_audio_desc_config);
        uac_register(usb_id);
    } else if ((class & SPEAKER_CLASS) == SPEAKER_CLASS) {
        usb_log_info("add desc speaker");
        usb_add_desc_config(usb_id, class_index++, uac_spk_desc_config);
        uac_register(usb_id);
    } else if ((class & MIC_CLASS) == MIC_CLASS) {
        usb_log_info("add desc mic");
        usb_add_desc_config(usb_id, class_index++, uac_mic_desc_config);
        uac_register(usb_id);
    }

    usb_var_reinit(usb_id);  //mask usb ep addr ptr
    usb_set_setup_hook(usb_id2device(usb_id), user_setup);
#endif

    return 0;
}
void usb_slave_start()
{
    u32 class_index = 0;
    u32 class = USB_DEVICE_CLASS_CONFIG;

#ifndef CONFIG_USE_USER_UVC_DRIVER_EN  // 使用mask uvc 驱动
    usb_device_mode(usbfd, class);
    uvc_set_camera_reso(); //设置uvc分辨率
    class_index++;
#else
    //使用外部 uvc 驱动
    uvc_device_mode(usbfd, class, class_index);
    uvc_set_camera_reso(); //设置uvc分辨率
    void usb_device_init(usb_dev usb_id);

    /* usb_device_init(usbfd); */
    usb_ex_device_init(usbfd);
    usb_setup_init(usbfd, (void *)get_usb_setup_address(), (u8 *)get_isp_tool_buf()); //临时加大setup buffer,处理复合设备枚举时描述符长度溢出的问题

    usb_var_reinit(usbfd);  //mask usb ep addr ptr
    class_index++;
#endif

#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
    uac_device_mode(usbfd, class, class_index);
#endif

    usb_set_setup_hook(usb_id2device(usbfd), user_setup);

    usb_device_set_class(usb_id2device(usbfd), class);

}

void usb_slave_stop()
{
    u32 class = USB_DEVICE_CLASS_CONFIG;

#if TCFG_USB_SLAVE_MSD_ENABLE
#if (defined SDC_EN)
    msd_ex_unregister_disk("sd0");
#endif
    msd_set_wakeup_handle(NULL);
    msd_set_reset_wakeup_handle(NULL);
#endif

#ifndef CONFIG_USE_USER_UVC_DRIVER_EN  // 使用mask uvc 驱动
    usb_device_mode(usbfd, 0);

#else
#if TCFG_USB_SLAVE_UVC_ENABLE
    user_uvc_release(usbfd);
#endif
#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
    uac_release(usbfd);
#endif
    usb_g_hold(usbfd);
    usb_release(usbfd);
    usb_mdelay(150);  //rom里面uninit函数也是关闭usb之后等150ms
#endif
}

#define USB_ACTIVE_STA_CHECK                0
#define USB_ACTIVE_STA_KEEP_OFFLINE         1
#if defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG) || defined(CONFIG_PRODUCT_S30_7256)
    #define USB_ACTIVE_CHECK_CNT                10
#else
    #define USB_ACTIVE_CHECK_CNT                4
#endif
#define USB_NOACTIVE_CHECK_CNT              2

static u8 usb_active_sta;
static u32 active_chk_cnt;
extern u8 get_usb_bus_state();

void usb_slave_active_monitor(void *arg)
{
    switch (usb_active_sta) {
    case USB_ACTIVE_STA_CHECK:
        if (usb_read_sofpnd(usbfd)) {
            usb_sof_clr_pnd(usbfd);
            active_chk_cnt = 0;
        } else {
            active_chk_cnt++;
        }
        if (active_chk_cnt == USB_ACTIVE_CHECK_CNT) {
            printf("usb is not active, force uninstall\n");
            active_chk_cnt = 0;
            extern void usb_trim_close();
            usb_trim_close();
            usb_slave_stop();
            usb_active_sta = USB_ACTIVE_STA_KEEP_OFFLINE;
        }
        break;
    case USB_ACTIVE_STA_KEEP_OFFLINE:
        active_chk_cnt++;
        if (active_chk_cnt == USB_NOACTIVE_CHECK_CNT) {
            printf("usb re-install for active monitor\n");
            active_chk_cnt = 0;
            usb_slave_start();
            usb_active_sta = USB_ACTIVE_STA_CHECK;
        }
        break;
    }
}

#endif

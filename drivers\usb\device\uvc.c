
#define USB_DBG_LEV 0
#include "generic/includes.h"
#include "app_config.h"

#include "usb/usb.h"
#include "usb/usb_config.h"
#include "usb/device/usb_stack.h"
#include "usb/uvc.h"
#include "usb/device/slave_uvc.h"
#include "isp_scenes.h"
#include "cpu.h"
#include "app_config.h"


#ifdef CONFIG_USE_USER_UVC_DRIVER_EN      //使用外部uvc驱动
#if TCFG_USB_SLAVE_UVC_ENABLE
//#include "video/video_ioctl.h"
//#include "video/video.h"

#define TCFG_UVC_STILL_IMAGE_ENABLE  //静态图像抓拍支持

struct usb_uvc_camera {
    u32 itf_base: 8;
    u32 tx_broken: 1;
    u32 is_new_frame: 1;
    u32 bulk_send: 1;
    u32 format: 1;
    u32 streamon: 1;
    u32 still_image_change: 1;
    u32 reserved: 20;
    u16 width[FMT_MAX_RESO_NUM * 2];
    u16 height[FMT_MAX_RESO_NUM * 2];
    u16 fps[FMT_MAX_RESO_NUM * 2];
    u16 fps2[FMT_MAX_RESO_NUM * 2];
    int (*video_open)(int idx, int fmt, int frame_id, int fps, u16 width, u16 height);
    int (*video_reqbuf)(int idx, void *buf, u32 len, u32 *frame_end);
    int (*video_reqbuf2)(int idx, void **buf, u32 len, u32 *frame_end);
    int (*video_close)(int idx);
    int (*processing_unit_response)(struct uvc_unit_ctrl *ctl_req);
    int (*private_cmd_response)(u16 cmd, u16 data);
    u32 uvc_status;
    u32 last_len;
    u32 usb_packet_size;
    /* u8 *stream_buf; */
};
volatile u8 g_still_image_flag;// sec(.usb_ex_bss);
static struct usb_uvc_camera *uvc_handle;;
static struct usb_uvc_camera _uvc_handle ;//sec_used(.user_ram_bss);

typedef void (*pUVC_SETUP_HOOK)(usb_dev, struct usb_device_t *, struct usb_ctrlrequest *);

static u32 uvc_vc_itf_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static u32 uvc_vs_itf_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static u32 uvc_vc_itf_recv_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static u32 uvc_vs_itf_recv_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static void usb_video_open_camera(usb_dev usb_id, u8 only);
static void usb_video_close_camera(usb_dev usb_id);


void uvc_vc_pu_wBacklightCompensation_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wBrightness_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wContrast_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wGain_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bPowerLineFrequency_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wHue_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wSaturation_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wSharpness_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wGamma_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wWhiteBalanceTemperature_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bWhiteBalanceTemperatureAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_white_balance_component_control_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bWhiteBalanceComponentAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wMultiplierStep_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_wMultiplierLimit_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bHueAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bVideoStandard_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_pu_bStatus_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);

void uvc_vc_it_auto_exposure_mode_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vc_it_exposure_time_absolutive_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);

void uvc_vs_probe_commit_ctl_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
void uvc_vs_still_probe_commit_ctl_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req);

#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
static u8 uvc_intr_ep_buffer[VIDEO_STATUS_TXMAXP] __attribute__((aligned(64)));
#endif

/*------------------------------- uvc descriptor -------------------------------*/
/*********************************************************/
/*
			   Video Class
*/
/*********************************************************/
static const u8 uvc_interface_desc_header[] = {
/// Interface Association Descriptor  IAD descriptor
    USB_DT_INTERFACE_ASSOCIATION_SIZE,       //bLength
    USB_DT_INTERFACE_ASSOCIATION,       //bDescriptorType
    0x00,       //bFirstInterface:0
    0x02,       //bInterfaceCount:0x02
    USB_CLASS_VIDEO,   //bFunctionClass:USB_CLASS_VIDEO
    SC_VIDEO_INTERFACE_COLLECTION,  //bFunctionSubClass:SC_VIDEO_INTERFACE_COLLECTION
    UVC_PC_PROTOCOL_UNDEFINED,      //bFunctionProtocol:Not used. Must be set to UVC_PC_PROTOCOL_UNDEFINED
    VIDEO_STR_INDEX, //iFunction !! must match iInterface in VC Interface descriptor. Index of a string descriptor that describes this interface
};
static const u8 uvc_vc_interface_desc[] = {
///standard VC interface descriptor:   /*0x08*/
    USB_DT_INTERFACE_SIZE,       //Length
    USB_DT_INTERFACE,       //DescriptorType:Inerface
    0x00,       //InterfaceNum:0  !!
    0x00,       //AlternateSetting:0
    0x00,       //NumEndpoint:1 非零端点数目(interrupt endpoint)
    USB_CLASS_VIDEO,   //InterfaceClass:USB_CLASS_VIDEO
    SC_VIDEOCONTROL,     //InterfaceSubClass:SC_VIDEOCONTROL
    UVC_PC_PROTOCOL_UNDEFINED,       //bFunctionProtocol:Not used. Must be set to UVC_PC_PROTOCOL_UNDEFINED
    VIDEO_STR_INDEX,       //iInterface
///class-specific VC interface descriptor  /*0x11*/
    /* 0x0e,    	//Length */
    0x0d,
    USB_DT_CS_INTERFACE,   	//DescriptorType:cs_interface
    VC_HEADER, 	//DescriptorSubType:vc_header subtype
    0x00, 0x01, //bcdUVC:VC Device Class v1.00
    0x4d, 0x00, //TotalLength of class-specific VC interface descriptors
    DW1BYTE(UVC_DWCLOCKFREQUENCY),
    DW2BYTE(UVC_DWCLOCKFREQUENCY),
    DW3BYTE(UVC_DWCLOCKFREQUENCY),
    DW4BYTE(UVC_DWCLOCKFREQUENCY), //dwClockFreq
    /* 0x02,		//InCollection: Num of streaming interfaces */
    /* 0x01,      	//InterfaceNr(1) - VS #1 id VideoStreaming interface 1 belongs to this VideoControl interface */
    /* 0x02,      	//InterfaceNr(2) - VS #2 id VideoStreaming interface 1 belongs to this VideoControl interface */
    0x01,		//InCollection: Num of streaming interfaces
    0x01,      	//InterfaceNr(0) - VS #0 id VideoStreaming interface 1 belongs to this VideoControl interface
};
static const u8 uvc_terminal_desc[] = {
//Video Input Terminal Descriptor
    0x12,       //Length
    USB_DT_CS_INTERFACE,	      	 //DescriptorType:cs_interface
    VC_INPUT_TERMINAL,      	 	 //DescriptorSubType:vc_input_terminal subtype
    UVC_IT_TERMINALID,      		 //bTerminalID:0x01
    LOBYTE(ITT_CAMERA), HIBYTE(ITT_CAMERA),  //TerminalType:itt_camera type
    0x00,       //AssocTerminal
    0x00,       //iTerminal
    0x00, 0x00, 	//wObjectiveFocalLengthMin
    0x00, 0x00, //wObjectiveFocalLengthMax
    0x00, 0x00, //wOcularFocalLength
    0x03,      	//bControlSize
    0x00, 0x00, 0x00, //bmControls

///Processing Unit descriptor
    0x0B,       //Length
    USB_DT_CS_INTERFACE,        //DescriptorType:cs_interface
    VC_PROCESSING_UNIT,        //DescriptorSubType:vc_processing_unit subtype
    UVC_PU_TERMINALID,       //UnitID:0x03
    UVC_IT_TERMINALID,		//SourceID:this input pin of this unit is connected to the input pin of unit with ID 0x01
    0x00, 0x00, //MaxMultiplier
    0x02,       //ControlSize
    0x00, 0x00,   //mControls: Brightness control supported
    0x00,       //iProcessing

///Video Extension Unit descriptor(Camera)
    0x1D,       //Length
    USB_DT_CS_INTERFACE,        //DescriptorType:cs_interface
    VC_EXTENSION_UNIT,        //DescriptorSubType:vc_Extension_unit
    UVC_EXT_TERMINALID1,       //TerminalID:0x04
    UVC_GUID_UNIT_EXTENSION1,//guidExtensionCode

    0x08,//0x03,       //NumControl ??
    0x01,       //NrlnPins
    UVC_PU_TERMINALID,       //SourceID
    0x04,       //ControlSize
    0xff,  		//mControls
    0xff,  		//mControls
    0xff,  		//mControls
    0xff,  		//mControls
    0x00,

#if 0
///Video Extension Unit descriptor(Camera)
    0x1D,       //Length
    USB_DT_CS_INTERFACE,        //DescriptorType:cs_interface
    VC_EXTENSION_UNIT,        //DescriptorSubType:vc_Extension_unit
    UVC_EXT_TERMINALID2,       //TerminalID:0x04
    UVC_GUID_UNIT_EXTENSION2,//guidExtensionCode

    0x08,//0x03,       //NumControl ??
    0x01,       //NrlnPins
    UVC_EXT_TERMINALID1,       //SourceID
    0x04,       //ControlSize
    0xff,  		//mControls
    0xff,  		//mControls
    0xff,  		//mControls
    0xff,  		//mControls
    0x00,
#endif

///Output Terminal descriptor
    0x09,       //Length
    USB_DT_CS_INTERFACE,			 //DescriptorType:cs_interface
    VC_OUTPUT_TERMINAL,				 //DescriptorSubType:vc_output_terminal subtype
    UVC_OT_TERMINALID1,  	    	 //bTerminalID:0x02
    LOBYTE(TT_STREAMING), HIBYTE(TT_STREAMING),//wTerminalType:usb streaming terminal
    0x00,       //AssocTerminal
#if (CONFIG_IS_PRODUCT_S30)
    UVC_EXT_TERMINALID1,       //SourceID: the input pin of this unit is connected to the output pin of unit 3
#else
    UVC_EXT_TERMINALID2,       //SourceID: the input pin of this unit is connected to the output pin of unit 3
#endif
    0x00,       //Terminal
};
static const u8 uvc_standard_intr_endpoint_desc[] = {
///standard Interrupt Endpoint descriptor:
    USB_DT_ENDPOINT_SIZE,       //Length
    USB_DT_ENDPOINT,        //DescriptorType:enp descriptor
    USB_DIR_IN | VIDEO_STATUS_EP_IN,     //Endp_addr:IN endpoint 3
    USB_ENDPOINT_XFER_INT,       //Attributes :Interrupt transfer type
    LOBYTE(VIDEO_STATUS_TXMAXP), HIBYTE(VIDEO_STATUS_TXMAXP), //MaxPacketSize:8byte status packet
    0x4,       //Interval:

///class-specific Interrupt Endpoint descriptor
    0x05,    	//Length
    USB_DT_CS_ENDPOINT,       	//DescriptorType:cs_endpoint descriptor
    USB_ENDPOINT_XFER_INT,     	//DescriptorSubType:ep_interrupt
    LOBYTE(VIDEO_STATUS_TXMAXP), HIBYTE(VIDEO_STATUS_TXMAXP), //MaxPacketSize:8byte status packet
};
static const u8 uvc_vs_interface_desc[] = {
#if UVC_ISO_MODE
///standard VS interface descriptor:(Interface 2, Alternate Setting 1):
    USB_DT_INTERFACE_SIZE,       //Length
    USB_DT_INTERFACE,      //DescriptorType:Inerface
    0x01,       //InterfaceNum:1  !!
    0x00,       //AlternateSetting
    0x00,       //NumEndpoint:1 非零端点数目
    USB_CLASS_VIDEO, //0x0E,       //InterfaceClass:USB_CLASS_VIDEO
    SC_VIDEOSTREAMING, //0x02,       //InterfaceSubClass:SC_VIDEOSTREAMING
    UVC_PC_PROTOCOL_UNDEFINED,       //InterfaceProtocol
    0x00,       //iInterface

#else
///standard VS interface descriptor:(Interface 1, Alternate Setting 0):
    USB_DT_INTERFACE_SIZE,       //Length
    USB_DT_INTERFACE,      //DescriptorType:Inerface
    0x01,       //InterfaceNum:1  !!
    0x00,       //AlternateSetting
    0x01,       //NumEndpoint:1 非零端点数目
    USB_CLASS_VIDEO, //0x0E,       //InterfaceClass:USB_CLASS_VIDEO
    SC_VIDEOSTREAMING, //0x02,       //InterfaceSubClass:SC_VIDEOSTREAMING
    UVC_PC_PROTOCOL_UNDEFINED,       //InterfaceProtocol
    0x00,       //iInterface

///standard VS Iso Video Data Endpoint descriptor:
    USB_DT_ENDPOINT_SIZE,       //Length
    USB_DT_ENDPOINT, //0x05,       //DescriptorType:Endpoint
    USB_DIR_IN | UVC_STREAM_EP_IN,       //EndpointAddr:IN endpoint 2
    USB_ENDPOINT_XFER_BULK,

    LOBYTE(UVC_FIFO_TXMAXP),
    HIBYTE(UVC_FIFO_TXMAXP),//MaxPacketSize:MAX packet size

    0,       //Interval:one frame interval
#endif
};
static const u8 uvc_vs_interface_alternate_desc[] = {
///standard VS interface descriptor:(Interface 2, Alternate Setting 1):
    USB_DT_INTERFACE_SIZE,       //Length
    USB_DT_INTERFACE,      //DescriptorType:Inerface
    0x01,       //InterfaceNum:1  !!
    0x01,       //AlternateSetting
    0x01,       //NumEndpoint:1 非零端点数目
    USB_CLASS_VIDEO, //0x0E,       //InterfaceClass:USB_CLASS_VIDEO
    SC_VIDEOSTREAMING, //0x02,       //InterfaceSubClass:SC_VIDEOSTREAMING
    UVC_PC_PROTOCOL_UNDEFINED,       //InterfaceProtocol
    0x00,       //iInterface

///standard VS Iso Video Data Endpoint descriptor:
    USB_DT_ENDPOINT_SIZE,       //Length
    USB_DT_ENDPOINT, //0x05,       //DescriptorType:Endpoint
    USB_DIR_IN | UVC_STREAM_EP_IN,       //EndpointAddr:IN endpoint 2
    USB_ENDPOINT_SYNC_ASYNC | USB_ENDPOINT_XFER_ISOC,

    LOBYTE(UVC_FIFO_TXMAXP),
#if (CONFIG_IS_PRODUCT_S30)
    HIBYTE(UVC_FIFO_TXMAXP),//MaxPacketSize:MAX packet size
#else
    HIBYTE(UVC_FIFO_TXMAXP) | ((UVC_PKT_SPILT - 1) << 3),//MaxPacketSize:MAX packet size
#endif

    ISO_EP_INTERVAL,       //Interval:one frame interval
};
static const u8 uvc_vs_inf_input_header_desc[] = {
//VS Input Header descriptor
    0x0f,       //Length
    USB_DT_CS_INTERFACE,      	//DescriptorType:cs_interface
    VS_INPUT_HEADER,    	//DescriptorSubType:VS_INPUT_HEADER
    1,  		//Numformats:One format descriptor follows
    0, 0, //TotalLength of class-specific VS interface descriptors

    USB_DIR_IN | UVC_STREAM_EP_IN,     	//endpoint_addr: addr of iso endpoint used for video data
    0x00,		//info :no dynamic format change supported
    UVC_OT_TERMINALID1,      	//TerminalLink: this videostreaming interface supplies terminal ID 4(Output Terminal)
    2,		//StillCaptureMethod :Method2
    1,  		//TriggerSupport:support
    0,  		//TriggerUsage:none
    0x02,      	//ControlSize
    0x00,      	//Controls:No VideoStreaming specific controls are supported
    0x00,      	//Controls:No VideoStreaming specific controls are supported
};
#if UVC_FORMAT_MJPG
static const u8 uvc_mjpeg_format_desc[] = {
///class-specific VS Format descriptor
    0x0b,    	//Length
    USB_DT_CS_INTERFACE,      	//DescriptorType:cs_interface
    VS_FORMAT_MJPEG,      	//DescriptorSubType:VS_FORMAT_MJPEG
    0x01,  		//FormatIndex:first format descriptor

    0x05,		//NumFrameDescriptors: Three frame descriptor for this format follows

    0x00,     	//flags: NO uses fixed size samples
    0x01,		//defaultFrameIndex :1
    0,      	//AspectRatioX: non interlaced stream 16:9 = 1280 * 720
    0,  		//AspectRatioY :non interlaced stream 16:9 = 1280 * 720
    0x00,  		//InterfaceFlags:non interlaced stream
    0x00,  		//CopyProtect:
};
static const u8 uvc_mjpeg_frame_desc[] = {
///class-specific VS Frame descriptor
    0x1e,// + 4,    	//Length
    USB_DT_CS_INTERFACE,      	//DescriptorType:cs_interface
    VS_FRAME_MJPEG,      	//DescriptorSubType:VS_FRAME_MJPEG
    0x01,  		//FrameIndex:Num 1 Frame descriptor
    0x00,		//bmCapabilities:
    LOBYTE(MJPG_WWIDTH_0), HIBYTE(MJPG_WWIDTH_0),
    LOBYTE(MJPG_WHEIGHT_0), HIBYTE(MJPG_WHEIGHT_0),

    DW1BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 40),
    DW2BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 40),
    DW3BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 40),
    DW4BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 40),      	//MinBitRate: min bit rate in bits/s

    DW1BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 240),
    DW2BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 240),
    DW3BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 240),
    DW4BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 240), 		//MaxBitRate :max bit rate in bits/s

    DW1BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 2),
    DW2BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 2),
    DW3BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 2),
    DW4BYTE(MJPG_WWIDTH_0 *MJPG_WHEIGHT_0 * 2), //MaxVideoFrameBufSIZE : in bytes一帧数据或者still帧的最大空间

    DW1BYTE(FRAME_FPS),
    DW2BYTE(FRAME_FPS),
    DW3BYTE(FRAME_FPS),
    DW4BYTE(FRAME_FPS), //DefaultFrameInterval : 33333300ns(30fps) 默认的帧间隔

    0x01,		//FrameIntervalType: discrete frame interval,
    DW1BYTE(FRAME_FPS),
    DW2BYTE(FRAME_FPS),
    DW3BYTE(FRAME_FPS),
    DW4BYTE(FRAME_FPS),

    /* DW1BYTE(FRAME_FPS * 2), */
    /* DW2BYTE(FRAME_FPS * 2), */
    /* DW3BYTE(FRAME_FPS * 2), */
    /* DW4BYTE(FRAME_FPS * 2), */
};
#endif

static const u8 uvc_yuv_still_image_frame_desc[] = {
    14,
    USB_DT_CS_INTERFACE, //0x24,      	//DescriptorType:cs_interface
    UVC_VS_STILL_IMAGE_FRAME, //0x03,       //DescriptorSubType:UVC_VS_STILL_IMAGE_FRAME
    0x0,
    0x2,
    LOBYTE(640), HIBYTE(640),
    LOBYTE(480), HIBYTE(480),
    LOBYTE(320), HIBYTE(320),
    LOBYTE(240), HIBYTE(240),
    /* LOBYTE(1280), HIBYTE(1280), */
    /* LOBYTE(720), HIBYTE(720), */
    0x0,

};
static const u8 uvc_still_image_frame_desc[] = {
    0x16,
    USB_DT_CS_INTERFACE, //0x24,      	//DescriptorType:cs_interface
    UVC_VS_STILL_IMAGE_FRAME, //0x03,       //DescriptorSubType:UVC_VS_STILL_IMAGE_FRAME
    0x0,
    0x3,
    LOBYTE(1280), HIBYTE(1280),
    LOBYTE(720), HIBYTE(720),
    LOBYTE(640), HIBYTE(640),
    LOBYTE(480), HIBYTE(480),
    LOBYTE(320), HIBYTE(320),
    LOBYTE(240), HIBYTE(240),
    0x4,
    0x01,
    0x05,
    0x0A,
    0x14,

};

#if UVC_FORMAT_YUY2 || UVC_FORMAT_NV12 || UVC_FORMAT_I420
static const u8 uvc_yuv_format_desc[] = {
    0x1B,//bLength
    USB_DT_CS_INTERFACE,//bDescriptorType
    VS_FORMAT_UNCOMPRESSED,//bDescriptorSubtype
    2, //bFormatIndex
    5,//bNumFrameDescriptors
#if UVC_FORMAT_YUY2
    UVC_GUID_FORMAT_YUY2,
    0x10,//bBitsPerPixel
#elif UVC_FORMAT_NV12
    UVC_GUID_FORMAT_NV12,
    0x0c,//bBitsPerPixel
#elif UVC_FORMAT_I420
    UVC_GUID_FORMAT_I420,
    0x0c,//bBitsPerPixel
#endif

    1,//bDefaultFrameIndex
    0,//bAspectRatioX
    0,//bAspectRatioY
    0,//bmInterlaceFlags
    0,//bCopyProtect
};
static const u8 uvc_yuv_frame_desc[] = {
///class-specific VS Frame descriptor                /*0xE0*/                /*0x8C*/
    0x1E,// + 4,    	//Length
    USB_DT_CS_INTERFACE, //0x24,      	//DescriptorType:cs_interface
    VS_FRAME_UNCOMPRESSED, //0x05,     	//DescriptorSubType:VS_FRAME_MJPEG
    0x01,  		//FrameIndex:Num 1 Frame descriptor
    0x00,		//bmCapabilities:
    LOBYTE(YUV_WWIDTH_0), HIBYTE(YUV_WWIDTH_0),
    LOBYTE(YUV_WHEIGHT_0), HIBYTE(YUV_WHEIGHT_0),

    DW1BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW2BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW3BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW4BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),      	//MinBitRate: min bit rate in bits/s

    DW1BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW2BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW3BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8),
    DW4BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2 * 30 * 8), 		//MaxBitRate :max bit rate in bits/s

#if UVC_FORMAT_YUY2
    DW1BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2),
    DW2BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2),
    DW3BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2),
    DW4BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 2), //0x00, 0x58, 0x02, 0x00, 		//MaxVideoFrameBufSIZE : in bytes一帧数据或者still帧的最大空间
#elif UVC_FORMAT_NV12
    DW1BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW2BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW3BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW4BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
#elif UVC_FORMAT_I420
    DW1BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW2BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW3BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
    DW4BYTE(YUV_WWIDTH_0 *YUV_WHEIGHT_0 * 3 / 2),
#endif

    DW1BYTE(FRAME_FPS),
    DW2BYTE(FRAME_FPS),
    DW3BYTE(FRAME_FPS),
    DW4BYTE(FRAME_FPS), 		//DefaultFrameInterval : 33333300ns(30fps) 默认的帧间隔
    0x01,		//FrameIntervalType: discrete frame interval,支持下面的四种帧间隔

    DW1BYTE(FRAME_FPS),
    DW2BYTE(FRAME_FPS),
    DW3BYTE(FRAME_FPS),
    DW4BYTE(FRAME_FPS),

    /* DW1BYTE(FRAME_FPS * 2), */
    /* DW2BYTE(FRAME_FPS * 2), */
    /* DW3BYTE(FRAME_FPS * 2), */
    /* DW4BYTE(FRAME_FPS * 2), */
};
#endif
static const u8 uvc_color_matching_desc[] = {
///Video Color Matching descriptor:          /*0x178*/               /*0x11C*/
    0x06,       //Length
    USB_DT_CS_INTERFACE, //0x24,      	//DescriptorType:cs_interface
    VS_COLORFORMAT, //0x0d,       //DescriptorSubType:VS_ColorFormat
    0x01,       //ColorPrimaries 0: Unspecified (Image characteristics unknown) 1: BT.709, sRGB (default) 2: BT.470-2 (M) 3: BT.470-2 (B, G) 4: SMPTE 170M 5: SMPTE 240M 6-255: Reserved
    0x01,       //TransferCharacter 0: Unspecified (Image characteristics unknown) 1: BT.709 (default) 2: BT.470-2 M 3: BT.470-2 B, G
    0x04,       //MatrixCoeffcients
};
static u8 uvc_slave_probe_commit_control[26] = {
    0x00, 0x00,                     //bmHint
    0x01,                           //FormatIndex
    0x01,                           //FrameIndex
    DW1BYTE(FRAME_FPS),
    DW2BYTE(FRAME_FPS),
    DW3BYTE(FRAME_FPS),
    DW4BYTE(FRAME_FPS), //dwFrameInterval Frame interval in 100 ns units.
    0x00, 0x00,                     //KeyFrameRate
    0x00, 0x00,                     //PFrameRate
    0x00, 0x00,                     //CompQuanlity
    0x00, 0x00,                     //CompWindowsSize
    0x00, 0x00, //0x32,0x00,                      //Delay
    DW1BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW2BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW3BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW4BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),  //MaxVideoFrameSize
#if UVC_ISO_MODE
    DW1BYTE(UVC_FIFO_TXMAXP),
    DW2BYTE(UVC_FIFO_TXMAXP),
    DW3BYTE(UVC_FIFO_TXMAXP),
    DW4BYTE(UVC_FIFO_TXMAXP), //MaxPayLoadTransferSize
#else
    DW1BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW2BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW3BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW4BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2), //MaxPayLoadTransferSize
#endif
};
static u8 uvc_slave_stillprobe_commit_control[11] = {
    0x01,
    0x01,
    0x01,
    DW1BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW2BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW3BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW4BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
#if UVC_ISO_MODE
    DW1BYTE(UVC_FIFO_TXMAXP),
    DW2BYTE(UVC_FIFO_TXMAXP),
    DW3BYTE(UVC_FIFO_TXMAXP),
    DW4BYTE(UVC_FIFO_TXMAXP), //MaxPayLoadTransferSize
#else
    DW1BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW2BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW3BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2),
    DW4BYTE(MJPG_WHEIGHT_0 *MJPG_WWIDTH_0 * 2), //MaxPayLoadTransferSize
#endif
};
static const u8 videoStringDescriptor[] = {
    32,         //该描述符的长度为32字节
    0x03,       //字符串描述符的类型编码为0x03
    'D', 0x00, //d
    'V', 0x00, //v
    '2', 0x00, //2
    '0', 0x00, //0
    ' ', 0x00, //

    'U', 0x00, //u
    'S', 0x00, //s
    'B', 0x00, //b
    ' ', 0x00, //
    'C', 0x00, //c
    'A', 0x00, //a
    'M', 0x00, //m
    'E', 0x00, //e
    'R', 0x00, //r
    'A', 0x00, //a
};
static void uvc_reset_handle(struct usb_device_t *usb_device, u32 itf)
{
    usb_dev usb_id = usb_device2id(usb_device);
    u8 *ep_buffer = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
#if UVC_ISO_MODE
    u8 uvc_pkg_spilt = (uvc_handle->format == 0) ? 1 : 3;
    /* usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_ISOC, 1, ep_buffer, (uvc_handle->usb_packet_size | ((UVC_PKT_SPILT - 1) << 11))); */
    usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_ISOC, 1, ep_buffer, (uvc_handle->usb_packet_size | ((uvc_pkg_spilt - 1) << 11)));
    if (uvc_handle) {
        uvc_handle->tx_broken = 1;
        uvc_handle->last_len = 0;
    }
#else
    usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_BULK, 1, ep_buffer, (UVC_FIFO_TXMAXP));
    uvc_handle->bulk_send = 0;
#endif
}

u32 uvc_desc_config(usb_dev usb_id, u8 *ptr, u32 *inf)
{
    int i;
    u8 *tptr = ptr;
    u16 width, height;
    u8 *frame_num;
    int mjpg_fmt_enable;
    int yuv_fmt_enable;
    u32 total_len = 0;
    u8 *tptr_vs;
    u8 fmt_idx = 0;
    u32 fps;
    u32 fps1;
    u32 fps2;

    if (!uvc_handle || !ptr || !inf) {
        return 0;
    }
#if UVC_FORMAT_MJPG
    if (uvc_handle->width[0] && uvc_handle->height[0]) {
        mjpg_fmt_enable = 1;
    } else {
        mjpg_fmt_enable = 0;
    }
#else
    mjpg_fmt_enable = 0;
#endif
#if UVC_FORMAT_YUY2 || UVC_FORMAT_NV12 || UVC_FORMAT_I420
    if (uvc_handle->width[FMT_MAX_RESO_NUM] && uvc_handle->height[FMT_MAX_RESO_NUM]) {
        yuv_fmt_enable = 1;
    } else {
        yuv_fmt_enable = 0;
    }
#else
    yuv_fmt_enable = 0;
#endif
    usb_log_info("video interface num: %d", *inf + 1);

    memcpy(tptr, (u8 *)uvc_interface_desc_header, sizeof(uvc_interface_desc_header));
    tptr[2] = *inf;
    tptr += sizeof(uvc_interface_desc_header);

    memcpy(tptr, (u8 *)uvc_vc_interface_desc, sizeof(uvc_vc_interface_desc));
    tptr[2] = *inf;
    tptr[21] = *inf + 1;
#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
    tptr[4] =  1;
#endif
    total_len = 0x0d + sizeof(uvc_terminal_desc);
    tptr[14] = LOBYTE(total_len);
    tptr[15] = HIBYTE(total_len);
    tptr += sizeof(uvc_vc_interface_desc);

    memcpy(tptr, (u8 *)uvc_terminal_desc, sizeof(uvc_terminal_desc));
#if VS_IT_CONTROLS_ENABLE
    tptr[15] = 0x0A;
    tptr[16] = 0x00;
    tptr[17] = 0x00;
#endif
#if VS_PU_CONTROLS_ENABLE
    tptr[0x12 + 8] = 0x7B;
    tptr[0x12 + 9] = 0x07;
#endif
    tptr += sizeof(uvc_terminal_desc);

#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
    memcpy(tptr, (u8 *)uvc_standard_intr_endpoint_desc, sizeof(uvc_standard_intr_endpoint_desc));
    tptr += sizeof(uvc_standard_intr_endpoint_desc);
#endif

    memcpy(tptr, (u8 *)uvc_vs_interface_desc, sizeof(uvc_vs_interface_desc));
    tptr[2] = *inf + 1;
    tptr += sizeof(uvc_vs_interface_desc);

    memcpy(tptr, (u8 *)uvc_vs_inf_input_header_desc, sizeof(uvc_vs_inf_input_header_desc));
    if (mjpg_fmt_enable && yuv_fmt_enable) {
        tptr[12] = 1;
    }
    tptr_vs = tptr;
    tptr += sizeof(uvc_vs_inf_input_header_desc);
    total_len = sizeof(uvc_vs_inf_input_header_desc);

#if UVC_FORMAT_MJPG
    if (mjpg_fmt_enable) {
        memcpy(tptr, (u8 *)uvc_mjpeg_format_desc, sizeof(uvc_mjpeg_format_desc));
        tptr[3] = ++fmt_idx;
        frame_num = &tptr[4];
        *frame_num = 0;
        total_len += sizeof(uvc_mjpeg_format_desc);
        tptr += sizeof(uvc_mjpeg_format_desc);
        for (i = 0; i < FMT_MAX_RESO_NUM; i++) {
            if (uvc_handle->width[i] == 0 || uvc_handle->height[i] == 0) {
                break;
            }
            (*frame_num)++;
            memcpy(tptr, (u8 *)uvc_mjpeg_frame_desc, sizeof(uvc_mjpeg_frame_desc));

            tptr[3] = i + 1;
            width = uvc_handle->width[i];
            height = uvc_handle->height[i];
            fps1 = uvc_handle->fps[i];
            fps2 = uvc_handle->fps2[i];
            fps = fps1;
            if (fps1 == fps2) {
                fps2 = 0;
                fps1 = (fps1 == 0) ? 25 : fps1;
            } else if (fps1 < fps2) {
                fps1 = fps2;
                fps2 = fps;
            }
            if (fps2) {
                tptr[0] += 4;
            }

            tptr[5] = LOBYTE(width);
            tptr[6] = HIBYTE(width);
            tptr[7] = LOBYTE(height);
            tptr[8] = HIBYTE(height);

            tptr[9]  = DW1BYTE(width * height * 2 * 15 * 8);
            tptr[10] = DW2BYTE(width * height * 2 * 15 * 8);
            tptr[11] = DW3BYTE(width * height * 2 * 15 * 8);
            tptr[12] = DW4BYTE(width * height * 2 * 15 * 8); //MinBitRate

            tptr[13] = DW1BYTE(width * height * 2 * 30 * 8);
            tptr[14] = DW2BYTE(width * height * 2 * 30 * 8);
            tptr[15] = DW3BYTE(width * height * 2 * 30 * 8);
            tptr[16] = DW4BYTE(width * height * 2 * 30 * 8);  //MaxBitRate

            tptr[17] = DW1BYTE(width * height * 2);
            tptr[18] = DW2BYTE(width * height * 2);
            tptr[19] = DW3BYTE(width * height * 2);
            tptr[20] = DW4BYTE(width * height * 2);  //MaxVideoFrameBufSize

            //set Frameinterval
            tptr[21] = DW1BYTE(10000000 / fps1);
            tptr[22] = DW2BYTE(10000000 / fps1);
            tptr[23] = DW3BYTE(10000000 / fps1);
            tptr[24] = DW4BYTE(10000000 / fps1); //DefaultFrameInterval
            if (fps2) {
                tptr[25] = 2;
            }
            tptr[26] = DW1BYTE(10000000 / fps1);
            tptr[27] = DW2BYTE(10000000 / fps1);
            tptr[28] = DW3BYTE(10000000 / fps1);
            tptr[29] = DW4BYTE(10000000 / fps1); //dwFrameInterval[0]

            if (fps2) {
                tptr[30] = DW1BYTE(10000000 / fps2);
                tptr[31] = DW2BYTE(10000000 / fps2);
                tptr[32] = DW3BYTE(10000000 / fps2);
                tptr[33] = DW4BYTE(10000000 / fps2); //dwFrameInterval[1]
            }
            /* tptr += sizeof(uvc_mjpeg_frame_desc); */
            /* total_len += sizeof(uvc_mjpeg_frame_desc); */
            total_len += tptr[0];//sizeof(uvc_mjpeg_frame_desc);
            tptr += tptr[0];//sizeof(uvc_mjpeg_frame_desc);
        }

#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
        memcpy(tptr, (u8 *)uvc_still_image_frame_desc, sizeof(uvc_still_image_frame_desc));
        tptr += sizeof(uvc_still_image_frame_desc);
        total_len += sizeof(uvc_still_image_frame_desc);
#endif
    }
#endif
#if UVC_FORMAT_YUY2 || UVC_FORMAT_NV12 || UVC_FORMAT_I420
    if (yuv_fmt_enable) {
        memcpy(tptr, (u8 *)uvc_yuv_format_desc, sizeof(uvc_yuv_format_desc));
        tptr[3] = ++fmt_idx;
        frame_num = &tptr[4];
        *frame_num = 0;
        tptr += sizeof(uvc_yuv_format_desc);
        total_len += sizeof(uvc_yuv_format_desc);
        for (i = 0; i < FMT_MAX_RESO_NUM; i++) {
            if (uvc_handle->width[FMT_MAX_RESO_NUM + i] == 0 ||
                uvc_handle->height[FMT_MAX_RESO_NUM + i] == 0) {
                break;
            }
            (*frame_num)++;
            memcpy(tptr, (u8 *)uvc_yuv_frame_desc, sizeof(uvc_yuv_frame_desc));
            tptr[3] = i + 1;

            width = uvc_handle->width[FMT_MAX_RESO_NUM + i];
            height = uvc_handle->height[FMT_MAX_RESO_NUM + i];
            fps1 = uvc_handle->fps[FMT_MAX_RESO_NUM + i];
            fps2 = uvc_handle->fps2[FMT_MAX_RESO_NUM + i];
            fps = fps1;
            if (fps1 == fps2) {
                fps2 = 0;
                fps1 = (fps1 == 0) ? 25 : fps1;
            } else if (fps1 < fps2) {
                fps1 = fps2;
                fps2 = fps;
            }
            if (fps2) {
                tptr[0] += 4;
            }
            tptr[5] = LOBYTE(width);
            tptr[6] = HIBYTE(width);
            tptr[7] = LOBYTE(height);
            tptr[8] = HIBYTE(height);

#if UVC_FORMAT_YUY2
            tptr[9]  = DW1BYTE(width * height * 2 * 15 * 8);
            tptr[10] = DW2BYTE(width * height * 2 * 15 * 8);
            tptr[11] = DW3BYTE(width * height * 2 * 15 * 8);
            tptr[12] = DW4BYTE(width * height * 2 * 15 * 8);  //MinBitRate

            tptr[13] = DW1BYTE(width * height * 2 * 30 * 8);
            tptr[14] = DW2BYTE(width * height * 2 * 30 * 8);
            tptr[15] = DW3BYTE(width * height * 2 * 30 * 8);
            tptr[16] = DW4BYTE(width * height * 2 * 30 * 8);  //MaxBitRate

            tptr[17] = DW1BYTE(width * height * 2);
            tptr[18] = DW2BYTE(width * height * 2);
            tptr[19] = DW3BYTE(width * height * 2);
            tptr[20] = DW4BYTE(width * height * 2);  //MaxVideoFrameBufSize
#elif UVC_FORMAT_NV12
            tptr[9]  = DW1BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[10] = DW2BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[11] = DW3BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[12] = DW4BYTE(width * height * 3 / 2 * 15 * 8);  //MinBitRate

            tptr[13] = DW1BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[14] = DW2BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[15] = DW3BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[16] = DW4BYTE(width * height * 3 / 2 * 30 * 8);  //MaxBitRate

            tptr[17] = DW1BYTE(width * height * 3 / 2);
            tptr[18] = DW2BYTE(width * height * 3 / 2);
            tptr[19] = DW3BYTE(width * height * 3 / 2);
            tptr[20] = DW4BYTE(width * height * 3 / 2);  //MaxVideoFrameBufSize
#elif UVC_FORMAT_I420
            tptr[9]  = DW1BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[10] = DW2BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[11] = DW3BYTE(width * height * 3 / 2 * 15 * 8);
            tptr[12] = DW4BYTE(width * height * 3 / 2 * 15 * 8);  //MinBitRate

            tptr[13] = DW1BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[14] = DW2BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[15] = DW3BYTE(width * height * 3 / 2 * 30 * 8);
            tptr[16] = DW4BYTE(width * height * 3 / 2 * 30 * 8);  //MaxBitRate

            tptr[17] = DW1BYTE(width * height * 3 / 2);
            tptr[18] = DW2BYTE(width * height * 3 / 2);
            tptr[19] = DW3BYTE(width * height * 3 / 2);
            tptr[20] = DW4BYTE(width * height * 3 / 2);  //MaxVideoFrameBufSize
#endif
            //set Frameinterval
            tptr[21] = DW1BYTE(10000000 / fps1);
            tptr[22] = DW2BYTE(10000000 / fps1);
            tptr[23] = DW3BYTE(10000000 / fps1);
            tptr[24] = DW4BYTE(10000000 / fps1); //DefaultFrameInterval
            if (fps2) {
                tptr[25] = 2;
            }

            tptr[26] = DW1BYTE(10000000 / fps1);
            tptr[27] = DW2BYTE(10000000 / fps1);
            tptr[28] = DW3BYTE(10000000 / fps1);
            tptr[29] = DW4BYTE(10000000 / fps1); //dwFrameInterval[1]
            if (fps2) {
                tptr[30] = DW1BYTE(10000000 / fps2);
                tptr[31] = DW2BYTE(10000000 / fps2);
                tptr[32] = DW3BYTE(10000000 / fps2);
                tptr[33] = DW4BYTE(10000000 / fps2); //dwFrameInterval[1]
            }

            /* tptr += sizeof(uvc_yuv_frame_desc); */
            /* total_len += sizeof(uvc_yuv_frame_desc); */
            tptr += tptr[0];
            total_len += tptr[0];
        }

#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
        memcpy(tptr, (u8 *)uvc_yuv_still_image_frame_desc, sizeof(uvc_yuv_still_image_frame_desc));
        tptr += sizeof(uvc_yuv_still_image_frame_desc);
        total_len += sizeof(uvc_yuv_still_image_frame_desc);
#endif
    }
#endif

    memcpy(tptr, (u8 *)uvc_color_matching_desc, sizeof(uvc_color_matching_desc));
    tptr += sizeof(uvc_color_matching_desc);
    total_len += sizeof(uvc_color_matching_desc);

    //fill uvc_vs_inf_input_header_desc
    tptr_vs[3] = fmt_idx;
    tptr_vs[4] = LOBYTE(total_len);
    tptr_vs[5] = HIBYTE(total_len);

#if UVC_ISO_MODE
    memcpy(tptr, (u8 *)uvc_vs_interface_alternate_desc, sizeof(uvc_vs_interface_alternate_desc));

    tptr[13] = LOBYTE(uvc_handle->usb_packet_size);
    tptr[14] = HIBYTE(uvc_handle->usb_packet_size) | ((UVC_PKT_SPILT - 1) << 3);
    tptr[2] = *inf + 1;
    tptr += sizeof(uvc_vs_interface_alternate_desc);
#endif

    if (usb_set_interface_hander(usb_id, *inf, uvc_vc_itf_handle) != *inf) {
        ASSERT(0, "uvc set vc interface handle fail");
    }
    if (usb_set_interface_hander(usb_id, *inf + 1, uvc_vs_itf_handle) != *inf + 1) {
        ASSERT(0, "uvc set vs interface handle fail")
    }
    if (usb_set_reset_hander(usb_id, *inf, uvc_reset_handle) != *inf) {
        ASSERT(0, "uvc set reset handle fail")
    }
    uvc_handle->itf_base = *inf;
    *inf += 2;

#ifdef USE_FLASH_CFG_EN
#include "e2bApi.h"
    u8 *user_config_descriptor = get_eeprom_usb_config_desc();
    if (user_config_descriptor) {
        u16 desc_len = 0;
        memcpy(&desc_len, &user_config_descriptor[2], 2);
        if (desc_len) {
            memcpy(tptr, user_config_descriptor, desc_len);
            return desc_len;
        }
    }
#endif

    return (u32)(tptr - ptr);
}

const u8 *uvc_get_string()
{
    return videoStringDescriptor;
}

/*------------------------------- uvc setup -------------------------------*/
static u32 uvc_vc_itf_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    usb_dev usb_id = usb_device2id(usb_device);
    usb_device->wDataLength = req->wLength;
    usb_device->setup_ptr = usb_device->setup_buffer;
    usb_device->bDataOverFlag = 0;

    usb_log_info("%x %x %x %x\n", req->bRequest,
                 req->wIndex,
                 req->wLength,
                 req->wValue);

    usb_set_setup_phase(usb_device, USB_EP0_STAGE_SETUP);

    switch (req->bRequestType & USB_TYPE_MASK) {
    case USB_TYPE_STANDARD:
        usb_log_info("standard request\n");
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;

    case USB_TYPE_CLASS:
        if (LOBYTE(req->wIndex) == uvc_handle->itf_base) {  //vc interface class specificed request
            usb_log_info("vc interface request\n");
            switch (HIBYTE(req->wIndex)) {
#if VS_IT_CONTROLS_ENABLE
            case UVC_IT_TERMINALID:
                usb_device->wDataLength = req->wLength;
#if 0
                goto __req_default;
#endif
                switch (HIBYTE(req->wValue)) {
                case CT_SCANNING_MODE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_AE_MODE_CONTROL:
                    uvc_vc_it_auto_exposure_mode_handle(usb_id, usb_device, req);
                    break;
                case CT_AE_PRIORITY_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_EXPOSURE_TIME_ABSOLUTE_CONTROL:
                    uvc_vc_it_exposure_time_absolutive_handle(usb_id, usb_device, req);
                    break;
                case CT_EXPOSURE_TIME_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_FOCUS_ABSOLUTE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_FOCUS_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_FOCUS_AUTO_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_IRIS_ABSOLUTE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_IRIS_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_ZOOM_ABSOLUTE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_ZOOM_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_PANTILT_ABSOLUTE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_PANTILT_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_ROLL_ABSOLUTE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_ROLL_RELATIVE_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                case CT_PRIVACY_CONTROL:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                    break;
                default:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                }
                break;
#endif
#if VS_PU_CONTROLS_ENABLE
            case UVC_PU_TERMINALID:
                usb_device->wDataLength = req->wLength;
                if (uvc_handle->processing_unit_response && req->bRequest != UVC_SET_CUR) {
                    struct uvc_unit_ctrl unit_ctrl_req;
                    unit_ctrl_req.request = req->bRequest;
                    unit_ctrl_req.unit = HIBYTE(req->wValue);
                    unit_ctrl_req.data = usb_device->setup_ptr;
                    unit_ctrl_req.len = req->wLength;
                    int err = uvc_handle->processing_unit_response(&unit_ctrl_req);
                    if (!err) {
                        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                        if (unit_ctrl_req.len != req->wLength) {
                            if (unit_ctrl_req.len < req->wLength) {
                                usb_device->wDataLength = unit_ctrl_req.len;
                                usb_device->bDataOverFlag = 1;
                            }
                        }
                        break;
                    }
                }
                switch (HIBYTE(req->wValue)) {
                case PU_BACKLIGHT_COMPENSATION_CONTROL:  //背光对比
                    uvc_vc_pu_wBacklightCompensation_handle(usb_id, usb_device, req);
                    break;
                case PU_BRIGHTNESS_CONTROL:  //亮度
                    uvc_vc_pu_wBrightness_handle(usb_id, usb_device, req);
                    break;
                case PU_CONTRAST_CONTROL: //对比度
                    uvc_vc_pu_wContrast_handle(usb_id, usb_device, req);
                    break;
                case PU_GAIN_CONTROL:
                    uvc_vc_pu_wGain_handle(usb_id, usb_device, req);
                    break;
                case PU_POWER_LINE_FREQUENCY_CONTROL:
                    uvc_vc_pu_bPowerLineFrequency_handle(usb_id, usb_device, req);
                    break;
                case PU_HUE_CONTROL: //色调
                    uvc_vc_pu_wHue_handle(usb_id, usb_device, req);
                    break;
                case PU_SATURATION_CONTROL: //饱和度
                    uvc_vc_pu_wSaturation_handle(usb_id, usb_device, req);
                    break;
                case PU_SHARPNESS_CONTROL: //清晰度，锐度
                    uvc_vc_pu_wSharpness_handle(usb_id, usb_device, req);
                    break;
                case PU_GAMMA_CONTROL: //伽马
                    uvc_vc_pu_wGamma_handle(usb_id, usb_device, req);
                    break;
                case PU_WHITE_BALANCE_TEMPERATURE_CONTROL: //白平衡
                    uvc_vc_pu_wWhiteBalanceTemperature_handle(usb_id, usb_device, req);
                    break;
                case PU_WHITE_BALANCE_TEMPERATURE_AUTO_CONTROL:
                    uvc_vc_pu_bWhiteBalanceTemperatureAuto_handle(usb_id, usb_device, req);
                    break;
                case PU_WHITE_BALANCE_COMPONENT_CONTROL:
                    uvc_vc_pu_white_balance_component_control_handle(usb_id, usb_device, req);
                    break;
                case PU_WHITE_BALANCE_COMPONENT_AUTO_CONTROL:
                    uvc_vc_pu_bWhiteBalanceComponentAuto_handle(usb_id, usb_device, req);
                    break;
                case PU_DIGITAL_MULTIPLIER_CONTROL:
                    uvc_vc_pu_wMultiplierStep_handle(usb_id, usb_device, req);
                    break;
                case PU_DIGITAL_MULTIPLIER_LIMIT_CONTROL:
                    uvc_vc_pu_wMultiplierLimit_handle(usb_id, usb_device, req);
                    break;
                case PU_HUE_AUTO_CONTROL:
                    uvc_vc_pu_bHueAuto_handle(usb_id, usb_device, req);
                    break;
                case PU_ANALOG_VIDEO_STANDARD_CONTROL:
                    uvc_vc_pu_bVideoStandard_handle(usb_id, usb_device, req);
                    break;
                case PU_ANALOG_LOCK_STATUS_CONTROL:
                    uvc_vc_pu_bStatus_handle(usb_id, usb_device, req);
                    break;
                default:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                }
                break;
#endif
            case UVC_EXT_TERMINALID1:
            case UVC_EXT_TERMINALID2:
                switch (req->bRequest) {
                case UVC_GET_CUR:
                    *(u16 *)usb_device->setup_buffer = 0x0001;
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_GET_MIN:
                    *(u16 *)usb_device->setup_buffer = 0x0008;
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_GET_MAX:
                    *(u16 *)usb_device->setup_buffer = 0x0010;
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_GET_RES:
                case UVC_GET_DEF:
                    *(u16 *)usb_device->setup_buffer = 0x0001;
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_GET_INFO:
                    usb_device->setup_buffer[0] = 0x03;
                    usb_device->wDataLength = 1;
                    if (usb_device->wDataLength < req->wLength) {
                        usb_device->bDataOverFlag = 1;
                    }
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_GET_LEN:
                    if (HIBYTE(req->wValue) == 2 || HIBYTE(req->wValue) == 3) {
                        *(u16 *)usb_device->setup_buffer = 0x0002;
                    } else {
                        *(u16 *)usb_device->setup_buffer = 0x0004;
                    }
                    usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
                    break;
                case UVC_SET_CUR:
                    goto __req_default;
                    break;
                default:
                    usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
                }
                break;
            default:
                usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
            }
        } else {
            usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        }
        break;

    case USB_TYPE_VENDOR:
        goto __req_default;
        break;
    default:
        usb_log_info("requeset type dose not exist\n");
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }

    if ((req->bRequestType & 0x80) == 0) {
        usb_set_setup_recv(usb_id2device(usb_id), uvc_vc_itf_recv_handle);
    }
    //usb_log_info("%d\n", req->wLength);
    return 0;

__req_default:
    if (req->bRequest & 0x80) {  //UVC_GET_XXX
        memset(usb_device->setup_ptr, 0, req->wLength);
        usb_device->wDataLength = 0;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
    } else if (req->bRequest == UVC_SET_CUR) {
        usb_set_setup_recv(usb_id2device(usb_id), uvc_vc_itf_recv_handle);
        usb_device->wDataLength = req->wLength;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
        uvc_handle->uvc_status = 0;
    }
    return 0;
}

static u32 uvc_vs_itf_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    usb_dev usb_id = usb_device2id(usb_device);
    u8 *probe_commit = uvc_slave_probe_commit_control;
    u8 *stillprobe_commit = uvc_slave_stillprobe_commit_control;
    u32 frame_size = probe_commit[3] - 1;
    usb_device->wDataLength = req->wLength;
    usb_device->setup_ptr = usb_device->setup_buffer;
    usb_device->bDataOverFlag = 0;
    u8 format0, format;
    u8 frame0, frame;
    u32 fps;
    u16 still_width, still_height;
    u8 *still_frame_desc = NULL;

    u32 usb_packet_size = uvc_handle->usb_packet_size * UVC_PKT_SPILT;

    probe_commit[22] = DW1BYTE(usb_packet_size);
    probe_commit[23] = DW2BYTE(usb_packet_size);
    probe_commit[24] = DW3BYTE(usb_packet_size);
    probe_commit[25] = DW4BYTE(usb_packet_size); //MaxPayLoadTransferSize

    stillprobe_commit[7] = DW1BYTE(usb_packet_size);
    stillprobe_commit[8] = DW2BYTE(usb_packet_size);
    stillprobe_commit[9] = DW3BYTE(usb_packet_size);
    stillprobe_commit[10] = DW4BYTE(usb_packet_size); //MaxPayLoadTransferSize

    usb_set_setup_phase(usb_device, USB_EP0_STAGE_SETUP);

    switch (req->bRequestType & USB_TYPE_MASK) {
    case USB_TYPE_STANDARD:
        usb_log_info("standard request\n");
        if (req->bRequest == USB_REQ_SET_INTERFACE) {
            if (req->wIndex == uvc_handle->itf_base + 1) {  //mjpeg vs interface number
                usb_log_info("uvc set vs_interface %d alternate setting to %d\n", req->wIndex, req->wValue);
                if (req->wValue) {
                    usb_video_open_camera(usb_id, 0);
                } else {
                    usb_video_close_camera(usb_id);
                }
            } else {
                usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
            }
        }
        break;
    case USB_TYPE_CLASS:
        if (LOBYTE(req->wIndex) == uvc_handle->itf_base + 1) { //vs interface class specificed request
            usb_log_info("vs interface request\n");
            switch (HIBYTE(req->wValue)) {
            case UVC_VS_PROBE_CONTROL:
            case UVC_VS_COMMIT_CONTROL:
                uvc_vs_probe_commit_ctl_handle(usb_id, usb_device, req);
                break;
            case UVC_VS_STILL_PROBE_CONTROL:
                uvc_vs_still_probe_commit_ctl_handle(usb_id, usb_device, req);
                break;
            case UVC_VS_STILL_COMMIT_CONTROL:
                uvc_vs_still_probe_commit_ctl_handle(usb_id, usb_device, req);
                /* printf("uvc_slave_probe_commit_control:\n"); */
                /* printf_buf(uvc_slave_probe_commit_control, 4); */
                /* printf("uvc_slave_stillprobe_commit_control:\n"); */
                /* printf_buf(uvc_slave_stillprobe_commit_control, 4); */
                format0 = uvc_slave_probe_commit_control[2] - 1;
                frame0 = uvc_slave_probe_commit_control[3] - 1;
                format = uvc_slave_stillprobe_commit_control[0] - 1;
                frame = uvc_slave_stillprobe_commit_control[1] - 1;
                still_frame_desc = (u8 *)uvc_still_image_frame_desc; //mjpeg still
                if (format == 1) {
                    still_frame_desc = (u8 *)uvc_yuv_still_image_frame_desc; //yuv still
                }
                memcpy(&still_width, &still_frame_desc[5 + frame * 4], 2);
                memcpy(&still_height, &still_frame_desc[7 + frame * 4], 2);

                if ((still_width != uvc_handle->width[FMT_MAX_RESO_NUM * format0 + frame0]) || \
                    (still_height != uvc_handle->height[FMT_MAX_RESO_NUM * format0 + frame0]) || \
                    format0 != format) {
                    uvc_handle->still_image_change = 1;
                    /* uvc_handle->video_close(usb_id); */
                    memcpy(&fps, &uvc_slave_probe_commit_control[4], 4);
                    uvc_handle->video_open(usb_id,
                                           format,
                                           frame,
                                           10000000 / fps,
                                           still_width,
                                           still_height);
                }
                break;
            case UVC_VS_STILL_IMAGE_TRIGGER_CONTROL:
                g_still_image_flag = 1;
                break;

            }
        } else {
            usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        }
        break;
    case USB_TYPE_VENDOR:
        goto __req_default;
        break;
    default:
        usb_log_info("requeset type dose not exist\n");
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
    if ((req->bRequestType & 0x80) == 0) {
        usb_set_setup_recv(usb_id2device(usb_id), uvc_vs_itf_recv_handle);
    }
    //usb_log_info("%d\n", req->wLength);
    return 0;

__req_default:
    if (req->bRequest & 0x80) {  //UVC_GET_XXX
        memset(usb_device->setup_ptr, 0, req->wLength);
        usb_device->wDataLength = 0;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
    } else if (req->bRequest == UVC_SET_CUR) {
        usb_set_setup_recv(usb_id2device(usb_id), uvc_vs_itf_recv_handle);
        usb_device->wDataLength = req->wLength;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
        uvc_handle->uvc_status = 0;
    }
    return 0;
}

static u32 uvc_vc_itf_recv_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 rx_len = req->wLength;
    int err;
    usb_dev usb_id = usb_device2id(usb_device);

    usb_device->setup_ptr = usb_device->setup_buffer;
    rx_len = usb_read_count0(usb_id);
    usb_read_ep0(usb_id, usb_device->setup_ptr, rx_len);
    if (LOBYTE(req->wIndex) == uvc_handle->itf_base) {
        err = -1;
        if (HIBYTE(req->wIndex) == UVC_PU_TERMINALID &&
            uvc_handle->processing_unit_response) {
            struct uvc_unit_ctrl unit_ctrl_req;
            unit_ctrl_req.request = req->bRequest;
            unit_ctrl_req.unit = HIBYTE(req->wValue);
            unit_ctrl_req.data = usb_device->setup_ptr;
            unit_ctrl_req.len = req->wLength;
            err = uvc_handle->processing_unit_response(&unit_ctrl_req);
        }
        if (err && uvc_handle->uvc_status) {
            ((pUVC_SETUP_HOOK)uvc_handle->uvc_status)(usb_id, usb_device, req);
        }
    } else {
        usb_log_info("interface invalid\n");
    }

    uvc_handle->uvc_status = 0;
    return USB_EP0_STAGE_SETUP;
}

static u32 uvc_vs_itf_recv_handle(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 rx_len = req->wLength;
    int err;
    usb_dev usb_id = usb_device2id(usb_device);

    usb_device->setup_ptr = usb_device->setup_buffer;
    rx_len = usb_read_count0(usb_id);
    usb_read_ep0(usb_id, usb_device->setup_ptr, rx_len);
    if (LOBYTE(req->wIndex) == uvc_handle->itf_base + 1) {
        if (uvc_handle->uvc_status) {
            ((pUVC_SETUP_HOOK)uvc_handle->uvc_status)(usb_id, usb_device, req);
        }
    } else {
        usb_log_info("interface invalid\n");
    }

    uvc_handle->uvc_status = 0;
    return USB_EP0_STAGE_SETUP;
}

int uvc_setup_private(usb_dev usb_id, struct usb_device_t *usb_devie, struct usb_ctrlrequest *req)
{
    uvc_handle->private_cmd_response(req->bRequest, req->wValue);
    return 0;
}

/*------------------------------- uvc main -------------------------------*/
#define UVC_CAMERA_MAGIC    0x5aa555aa


enum {
    UVC_CUR,
    UVC_MIN,
    UVC_MAX,
    UVC_DEF,
};
struct video_arg_info {
    //cur min max default
    s16 wBacklightCompensation[4];//number
    s16 wBrightness[4];//Signed number
    s16 wContrast[4];//Number
    s16 wGain[4];//Number
    u8 bPowerLineFrequency[4];//0: Disabled 1: 50 Hz 2: 60 Hz
    s16 wHue[4];//Signed number
    s16 wSaturation[4];
    s16 wSharpness[4];
    s16 wGamma[4];
    s16 wWhiteBalanceTemperature[4];
    u8 bWhiteBalanceTemperatureAuto[4];
    u32 pu_white_balance_component_control[4];//wWhiteBalanceBlue wWhiteBalanceRed
    u8 bWhiteBalanceComponentAuto[4];
    u16 wMultiplierStep[4];
    u16 wMultiplierLimit[4];
    u8 bHueAuto[4];
    u8 bVideoStandard[4];//0: None 1: NTSC – 525/60 2: PAL – 625/50 4: SECAM – 625/50 4: NTSC – 625/50 5: PAL – 525/60 6-255: Reserved. Do not use
    u8 bStatus[4];//0: Video Decoder is locked. 1: Video Decoder is not locked. 2-255: Reserved. Do not use.
};
static struct video_arg_info video_ctl_value[USB_MAX_HW_NUM];// sec(.usb_ex_bss);

static void usb_video_feature_init(usb_dev usb_id)
{
    usb_log_info("sizeof(video_ctl_value[%d]) = %lu\n", usb_id, sizeof(video_ctl_value[usb_id]));
    video_ctl_value[usb_id].wBacklightCompensation[UVC_MAX] = 0x00ff;
    video_ctl_value[usb_id].wBacklightCompensation[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].wBacklightCompensation[UVC_DEF] = 0x80;
    video_ctl_value[usb_id].wBacklightCompensation[UVC_CUR] = 0x80;

    video_ctl_value[usb_id].wBrightness[UVC_MAX] = 127;
    video_ctl_value[usb_id].wBrightness[UVC_MIN] = -127;
    video_ctl_value[usb_id].wBrightness[UVC_DEF] = 0;
    video_ctl_value[usb_id].wBrightness[UVC_CUR] = 0;

    video_ctl_value[usb_id].wContrast[UVC_MAX] = 511;
    video_ctl_value[usb_id].wContrast[UVC_MIN] = 0;
    video_ctl_value[usb_id].wContrast[UVC_DEF] = 256;
    video_ctl_value[usb_id].wContrast[UVC_CUR] = 256;

    video_ctl_value[usb_id].wGain[UVC_MAX] = 7;
    video_ctl_value[usb_id].wGain[UVC_MIN] = 1;
    video_ctl_value[usb_id].wGain[UVC_DEF] = 4;
    video_ctl_value[usb_id].wGain[UVC_CUR] = 4;

    video_ctl_value[usb_id].bPowerLineFrequency[UVC_MAX] = 0x02;
    video_ctl_value[usb_id].bPowerLineFrequency[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bPowerLineFrequency[UVC_DEF] = 0x00;
    video_ctl_value[usb_id].bPowerLineFrequency[UVC_CUR] = 0x00;

    video_ctl_value[usb_id].wHue[UVC_MAX] = 180;
    video_ctl_value[usb_id].wHue[UVC_MIN] = -180;
    video_ctl_value[usb_id].wHue[UVC_DEF] = 0;
    video_ctl_value[usb_id].wHue[UVC_CUR] = 0;

    video_ctl_value[usb_id].wSaturation[UVC_MAX] = 511;
    video_ctl_value[usb_id].wSaturation[UVC_MIN] = 0;
    video_ctl_value[usb_id].wSaturation[UVC_DEF] = 256;
    video_ctl_value[usb_id].wSaturation[UVC_CUR] = 256;

    video_ctl_value[usb_id].wSharpness[UVC_MAX] = 256;
    video_ctl_value[usb_id].wSharpness[UVC_MIN] = 0;
    video_ctl_value[usb_id].wSharpness[UVC_DEF] = 128;
    video_ctl_value[usb_id].wSharpness[UVC_CUR] = 128;

    video_ctl_value[usb_id].wGamma[UVC_MAX] = 30;
    video_ctl_value[usb_id].wGamma[UVC_MIN] = 10;
    video_ctl_value[usb_id].wGamma[UVC_DEF] = 20;
    video_ctl_value[usb_id].wGamma[UVC_CUR] = 20;

    video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_MAX] = 6500;
    video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_MIN] = 0;
    video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_DEF] = 4500;
    video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_CUR] = 4500;

    video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_MAX] = 1;
    video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_DEF] = 1;
    video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_CUR] = 1;

    video_ctl_value[usb_id].pu_white_balance_component_control[UVC_MAX] = 0x00ff00ff;
    video_ctl_value[usb_id].pu_white_balance_component_control[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].pu_white_balance_component_control[UVC_DEF] = 0x00800080;
    video_ctl_value[usb_id].pu_white_balance_component_control[UVC_CUR] = 0x00800080;

    video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_MAX] = 1;
    video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_DEF] = 0;
    video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_CUR] = 0;

    video_ctl_value[usb_id].wMultiplierStep[UVC_MAX] = 0x00ff;
    video_ctl_value[usb_id].wMultiplierStep[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].wMultiplierStep[UVC_DEF] = 0x80;
    video_ctl_value[usb_id].wMultiplierStep[UVC_CUR] = 0x80;

    video_ctl_value[usb_id].wMultiplierLimit[UVC_MAX] = 0x00ff;
    video_ctl_value[usb_id].wMultiplierLimit[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].wMultiplierLimit[UVC_DEF] = 0x80;
    video_ctl_value[usb_id].wMultiplierLimit[UVC_CUR] = 0x80;

    video_ctl_value[usb_id].bHueAuto[UVC_MAX] = 1;
    video_ctl_value[usb_id].bHueAuto[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bHueAuto[UVC_DEF] = 0;
    video_ctl_value[usb_id].bHueAuto[UVC_CUR] = 0;

    video_ctl_value[usb_id].bVideoStandard[UVC_MAX] = 0;
    video_ctl_value[usb_id].bVideoStandard[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bVideoStandard[UVC_DEF] = 0;
    video_ctl_value[usb_id].bVideoStandard[UVC_CUR] = 0;

    video_ctl_value[usb_id].bStatus[UVC_MAX] = 1;
    video_ctl_value[usb_id].bStatus[UVC_MIN] = 0x00;
    video_ctl_value[usb_id].bStatus[UVC_DEF] = 1;
    video_ctl_value[usb_id].bStatus[UVC_CUR] = 1;
}

typedef struct uvc_payload_hdr {
    u8 bfh_val;
    u32 frame_cnt;
    u32 scr;
    u16 sof_cnt;
} UVC_PLD_HDR;

static UVC_PLD_HDR pld_hdr;// sec(.usb_ex_bss);
/* static u32 IsNewFrame; */

static void fill_payload_head(u8 *tx_addr, UVC_PLD_HDR *hdr)
{
    u32 frame_cnt = 0, scr = 0;
    u16 sof_count = 0;
    /// Stream Head
    tx_addr[0x00] = 0x0C;//Head Length
    tx_addr[0x01] = hdr->bfh_val; //BFH[0] [7:0]={EOH,ERR,STI,RES,SCR,PTS,EOF,FID}
    /* tx_addr[0x1] &= ~(BIT(2) | BIT(3) | BIT(5) | BIT(6)); */
    frame_cnt = hdr->frame_cnt;
    tx_addr[0x02] = frame_cnt ;//PTS[7:0]
    tx_addr[0x03] = (frame_cnt >> 8); //PTS[15:8]
    tx_addr[0x04] = (frame_cnt >> 16) ; //PTS[23:16]
    tx_addr[0x05] = (frame_cnt >> 24) ; //PTS[31:24]
    scr = hdr->scr;
    tx_addr[0x06] = DW1BYTE(scr);//SCR[7:0]
    tx_addr[0x07] = DW2BYTE(scr);//SCR[15:8]
    tx_addr[0x08] = DW3BYTE(scr);//SCR[23:16]
    tx_addr[0x09] = DW4BYTE(scr);//SCR[31:24]
    sof_count = hdr->sof_cnt;
    tx_addr[0x0A] = LOBYTE(sof_count); //SCR[42:32] 1KHz SOF token counter
    tx_addr[0x0B] = HIBYTE(sof_count); //SCR[47:42] res set to 0.
}

static void uvc_g_bulk_transfer(struct usb_device_t *usb_device, u32 ep)
{
    u32 len = 0;
    u32 IsEOFFrame = 0;
    u8 *tx_addr;
    usb_dev usb_id = usb_device2id(usb_device);
    u8 head_len = 0x02;

    /* putchar('i'); */
    if (uvc_handle == NULL) {
        return;
    }
    tx_addr = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
    if (uvc_handle->video_reqbuf) {
        if (uvc_handle->is_new_frame == 0) {
            len = uvc_handle->video_reqbuf(usb_id, tx_addr, UVC_FIFO_TXMAXP, &IsEOFFrame);
            if (IsEOFFrame) {
                uvc_handle->is_new_frame = 1;
            } else {
                uvc_handle->is_new_frame = 0;
            }
        } else {
            len = uvc_handle->video_reqbuf(usb_id, &tx_addr[head_len], UVC_FIFO_TXMAXP - head_len, &IsEOFFrame);
            if (len) {
                pld_hdr.sof_cnt = usb_read_sofframe(usb_id);
                pld_hdr.frame_cnt++;
                /* pld_hdr.bfh_val &= ~UVC_EOF; */
                /* pld_hdr.bfh_val |= UVC_SCR; */
                /* pld_hdr.bfh_val |= UVC_PTS; */
                /* fill_payload_head(tx_addr, &pld_hdr); */
                tx_addr[0x00] = head_len;//Head Length
                tx_addr[0x01] = pld_hdr.bfh_val;
                pld_hdr.bfh_val ^= UVC_FID;
                len += head_len;
                uvc_handle->is_new_frame = 0;
            }
        }
    }
    if (len) {
        usb_g_bulk_write(usb_id, UVC_STREAM_EP_IN, NULL, len);
    }
}

int uvc_cyc_bulk_transfer(usb_dev usb_id)
{
    u32 len = 0;
    u32 IsEOFFrame = 0;
    u8 *tx_addr;
    u8 head_len = 0x02;
    /* putchar('i'); */
    if (uvc_handle == NULL) {
        return len;
    }
    tx_addr = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
    if (uvc_handle->video_reqbuf) {
        if (uvc_handle->is_new_frame == 0) {
            len = uvc_handle->video_reqbuf(usb_id, tx_addr, UVC_FIFO_TXMAXP, &IsEOFFrame);
            if (IsEOFFrame) {
                uvc_handle->is_new_frame = 1;
            } else {
                uvc_handle->is_new_frame = 0;
            }
        } else {
            len = uvc_handle->video_reqbuf(usb_id, &tx_addr[head_len], UVC_FIFO_TXMAXP - head_len, &IsEOFFrame);
            if (len) {
                pld_hdr.sof_cnt = usb_read_sofframe(usb_id);
                pld_hdr.frame_cnt++;
                /* pld_hdr.bfh_val &= ~UVC_EOF; */
                /* pld_hdr.bfh_val |= UVC_SCR; */
                /* pld_hdr.bfh_val |= UVC_PTS; */
                /* fill_payload_head(tx_addr, &pld_hdr); */
                tx_addr[0x00] = head_len;//Head Length
                tx_addr[0x01] = pld_hdr.bfh_val;
                pld_hdr.bfh_val ^= UVC_FID;
                len += head_len;
                uvc_handle->is_new_frame = 0;
            }
        }
    }
    if (len) {
        len = usb_g_bulk_write(usb_id, UVC_STREAM_EP_IN, NULL, len);
    }
    return len;
}

static void uvc_g_iso_transfer(struct usb_device_t *usb_device, u32 ep)
{
    u32 len = 0;
    u32 IsEOFFrame = 0;
    u8 undo_tx = 0;
    u32 len2;
    u8 *tx_addr;
    usb_dev usb_id = usb_device2id(usb_device);
    static u8 is_still_packet = 0;
    /* putchar('i'); */

    u8 uvc_pkg_spilt = (uvc_handle->format == 0) ? 1 : 3;

    if (uvc_handle == NULL) {
        return;
    }
    tx_addr = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
    pld_hdr.sof_cnt = usb_read_sofframe(usb_id);
    if (!uvc_handle->tx_broken) {
        if (uvc_handle->video_reqbuf)  {
            /* len = uvc_handle->video_reqbuf(usb_id, &tx_addr[0x0c], uvc_handle->usb_packet_size * UVC_PKT_SPILT  - 0x0c, &IsEOFFrame); */
            len = uvc_handle->video_reqbuf(usb_id, &tx_addr[0x0c], uvc_handle->usb_packet_size * uvc_pkg_spilt  - 0x0c, &IsEOFFrame);
            if (is_still_packet) {
                //send still image packet
                pld_hdr.bfh_val |= UVC_STI;
                if (IsEOFFrame) {
                    pld_hdr.bfh_val |= UVC_EOF;
                    pld_hdr.frame_cnt++;
                    fill_payload_head(tx_addr, &pld_hdr);
                    pld_hdr.bfh_val ^= UVC_FID;
                    g_still_image_flag = 0;
                    is_still_packet = 0;
                    pld_hdr.bfh_val &= ~UVC_STI;
                    if (uvc_handle->still_image_change == 1) {
                        uvc_handle->still_image_change = 0;
                        uvc_handle->video_close(usb_id);
                        usb_video_open_camera(usb_id, 1);
                    }
                } else {
                    pld_hdr.bfh_val &= ~UVC_EOF;
                    fill_payload_head(tx_addr, &pld_hdr);
                }
            } else {

                if (IsEOFFrame) {
                    pld_hdr.bfh_val |= UVC_EOF;
                    pld_hdr.frame_cnt++;
                    fill_payload_head(tx_addr, &pld_hdr);
                    pld_hdr.bfh_val ^= UVC_FID;
                    if (g_still_image_flag && !is_still_packet) {
                        //开始发送still 包
                        is_still_packet = 1;
                    }
                } else {
                    pld_hdr.bfh_val &= ~UVC_EOF;
                    fill_payload_head(tx_addr, &pld_hdr);
                }
            }
        } else {
            pld_hdr.bfh_val &= ~UVC_EOF;
            fill_payload_head(tx_addr, &pld_hdr);
        }
       //// len = len > 0 ? len + 12 : 0;
#if (CONFIG_IS_PRODUCT_S30)
        len = len > 0 ? len + 12 : 0;
#else
        len = len > 0 ? len + 12 : 12;  ///by frank 0406
#endif
    } else {
        len = uvc_handle->last_len;
    }
    len2 = usb_g_iso_write(usb_id, UVC_STREAM_EP_IN, NULL, len);
    if (len && !len2) {
        uvc_handle->tx_broken = 1;
        uvc_handle->last_len = len;
    } else {
        uvc_handle->tx_broken = 0;
    }
}

static void uvc_g_iso_transfer2(struct usb_device_t *usb_device, u32 ep)
{
    u32 len = 0;
    u32 IsEOFFrame = 0;
    u8 undo_tx = 0;
    u32 len2;
    u8 *tx_addr = NULL;
    usb_dev usb_id = usb_device2id(usb_device);
    static u8 is_still_packet = 0;
    /* putchar('i'); */

    u8 uvc_pkg_spilt = (uvc_handle->format == 0) ? 1 : 3;
    /* u8 uvc_pkg_spilt = 3; */

    if (uvc_handle == NULL) {
        return;
    }
    pld_hdr.sof_cnt = usb_read_sofframe(usb_id);
    if (!uvc_handle->tx_broken) {
        if (uvc_handle->video_reqbuf2)  {
            len = uvc_handle->video_reqbuf2(usb_id, (void *)&tx_addr, uvc_handle->usb_packet_size * uvc_pkg_spilt, &IsEOFFrame);
            if (!tx_addr || len == 0) {
                tx_addr = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
                IsEOFFrame = 0;
                /* len = 12; */
            }
            if (is_still_packet) {
                //send still image packet
                pld_hdr.bfh_val |= UVC_STI;
                if (IsEOFFrame) {
                    pld_hdr.bfh_val |= UVC_EOF;
                    pld_hdr.frame_cnt++;
                    fill_payload_head(tx_addr, &pld_hdr);
                    pld_hdr.bfh_val ^= UVC_FID;
                    g_still_image_flag = 0;
                    is_still_packet = 0;
                    pld_hdr.bfh_val &= ~UVC_STI;
                    if (uvc_handle->still_image_change == 1) {
                        uvc_handle->still_image_change = 0;
                        uvc_handle->video_close(usb_id);
                        usb_video_open_camera(usb_id, 1);
                    }
                } else {
                    pld_hdr.bfh_val &= ~UVC_EOF;
                    fill_payload_head(tx_addr, &pld_hdr);
                }
            } else {

                if (IsEOFFrame) {
                    pld_hdr.bfh_val |= UVC_EOF;
                    pld_hdr.frame_cnt++;
                    fill_payload_head(tx_addr, &pld_hdr);
                    pld_hdr.bfh_val ^= UVC_FID;
                    if (g_still_image_flag && !is_still_packet) {
                        //开始发送still 包
                        is_still_packet = 1;
                    }
                } else {
                    pld_hdr.bfh_val &= ~UVC_EOF;
                    fill_payload_head(tx_addr, &pld_hdr);
                }
            }
        } else {
            pld_hdr.bfh_val &= ~UVC_EOF;
            fill_payload_head(tx_addr, &pld_hdr);
        }
#if (CONFIG_IS_PRODUCT_S30)
        len = len > 0 ? len /*+ 12*/ : 0;
#else
        len = len > 0 ? len /*+ 12*/ : 12; ////by frank 20230406 ---0;
#endif
    } else {
        len = uvc_handle->last_len;
    }

    if (tx_addr) {
        usb_set_dma_taddr(usb_id, UVC_STREAM_EP_IN, tx_addr);
    }
    len2 = usb_g_iso_write(usb_id, UVC_STREAM_EP_IN, NULL, len);
    if (len && !len2) {
        uvc_handle->tx_broken = 1;
        uvc_handle->last_len = len;
    } else {
        uvc_handle->tx_broken = 0;
    }
}
//void uvc_g_video_transfer(usb_dev usb_id, u32 ep)
//{
//#if UVC_ISO_MODE
//    uvc_g_iso_transfer(usb_id, ep);
//#else
//    uvc_g_bulk_transfer(usb_id, ep);
//#endif
//}
static void uvc_start_stream(usb_dev usb_id)
{
    if (uvc_handle == NULL) {
        return;
    }

    u8 *ep_buffer = usb_get_ep_buffer(usb_id, USB_CLASS_VIDEO, USB_DIR_IN);
#if UVC_ISO_MODE
    u8 uvc_pkg_spilt = (uvc_handle->format == 0) ? 1 : 3;
#if (ISO_EP_INTERVAL == 4)
    uvc_pkg_spilt = UVC_PKT_SPILT;
#endif
    /* usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_ISOC, 1, ep_buffer, (uvc_handle->usb_packet_size | ((UVC_PKT_SPILT - 1) << 11))); */
    usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_ISOC, 1, ep_buffer, (uvc_handle->usb_packet_size | ((uvc_pkg_spilt - 1) << 11)));
#ifndef CONFIG_UVC_ISO_REDIRECT_ENABLE
    usb_g_set_intr_hander(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, uvc_g_iso_transfer);
#else
    usb_g_set_intr_hander(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, uvc_g_iso_transfer2);
#endif
    pld_hdr.bfh_val |= UVC_EOF;
    fill_payload_head(ep_buffer, &pld_hdr);
    usb_g_iso_write(usb_id, UVC_STREAM_EP_IN, NULL, 0x0c);
    uvc_handle->tx_broken = 1;
    uvc_handle->last_len = 0x0c;
#else
    usb_g_ep_config(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_BULK, 1, ep_buffer, UVC_FIFO_TXMAXP);
    usb_g_set_intr_hander(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, uvc_g_bulk_transfer);

    if (!uvc_handle->bulk_send) {
        //只有重新枚举才需要发空包启动bulk传输,否则fifo数据将出错
        uvc_handle->bulk_send = 1;
        pld_hdr.bfh_val |= UVC_EOF;
        fill_payload_head(ep_buffer, &pld_hdr);
        usb_g_bulk_write(usb_id, UVC_STREAM_EP_IN, NULL, 0x0c);
        uvc_handle->is_new_frame = 1;
    }
#endif
#ifdef TCFG_UVC_STILL_IMAGE_ENABLE
    usb_g_ep_config(usb_id, VIDEO_STATUS_EP_IN | USB_DIR_IN, USB_ENDPOINT_XFER_INT, 0, uvc_intr_ep_buffer, VIDEO_STATUS_TXMAXP);
#endif
    uvc_handle->streamon = 1;
}

static void usb_video_open_camera(usb_dev usb_id, u8 only)
{
    u32 fps;
    u32 format;
    u32 frame;
    u8 mjpg_en = 0, yuv_en = 0;

    if (!uvc_handle) {
        usb_log_error("uvc open fail: hdl null");
        return;
    }
    g_still_image_flag = 0;

    pld_hdr.frame_cnt = 0;
    pld_hdr.sof_cnt = usb_read_sofframe(usb_id);
    pld_hdr.bfh_val = 0x80;
    memcpy(&fps, &uvc_slave_probe_commit_control[4], 4);
    usb_log_info("%d %d %d\n", g_still_image_flag, uvc_slave_probe_commit_control[3] - 1, 10000000 / fps);
    if (uvc_handle->width[0] && uvc_handle->height[0]) {
#if UVC_FORMAT_MJPG
        mjpg_en = 1;
#endif
    }
    if (uvc_handle->width[FMT_MAX_RESO_NUM] && uvc_handle->height[FMT_MAX_RESO_NUM]) {
#if UVC_FORMAT_YUY2 || UVC_FORMAT_NV12 || UVC_FORMAT_I420
        yuv_en = 1;
#endif
    }
    if (mjpg_en && yuv_en) {
        format = uvc_slave_probe_commit_control[2] - 1;
    } else if (mjpg_en) {
        format = 0;
    } else if (yuv_en) {
        format = 1;
    }
    uvc_handle->format = format;
    frame = uvc_slave_probe_commit_control[3] - 1;
    if (uvc_handle->video_open) {
        /* printf("%s %p\n", __func__, uvc_handle->video_open); */
        uvc_handle->video_open(usb_id,
                               //uvc_slave_probe_commit_control[2] - 1,  //Format Index
                               format,
                               //uvc_slave_probe_commit_control[3] - 1,  //Frame  Index
                               frame,
                               10000000 / fps,
                               uvc_handle->width[FMT_MAX_RESO_NUM * format + frame],
                               uvc_handle->height[FMT_MAX_RESO_NUM * format + frame]);
    }
    if (!only) {
        uvc_start_stream(usb_id);
        usb_log_error("open camera success\n");
    }
}

static void usb_video_close_camera(usb_dev usb_id)
{
    usb_log_error("close camera success\n");
    usb_clr_intr_txe(usb_id, UVC_STREAM_EP_IN);
    usb_g_set_intr_hander(usb_id, UVC_STREAM_EP_IN | USB_DIR_IN, NULL);

    if (uvc_handle && uvc_handle->video_close) {
        uvc_handle->video_close(usb_id);
    }

    uvc_handle->streamon = 0;
}

typedef struct _USB_VIDEO_STATUS_VAR {
    u8  bStateType;
    u8  bOriginator;
    u8  bEvent;
    u8  bSelector_Value;//当为视频控制接口时，表示控制的类型；当为视频流接口时，表示BUTTON值

    u8  bAttribute;
    u8  bValue;
} sUSB_VIDEO_STATUS_VAR;
static sUSB_VIDEO_STATUS_VAR g_video_status;

static void usb_video_status(u8 *pStatus, u8 interface_type)
{
    u8 res = 0;
    puts("usb_video_status\n");

}

void usb_video_shutter(usb_dev usb_id)
{
#if 1

    usb_log_info("shutter start....\n");
    if (!uvc_handle->streamon) {
        usb_log_info("uvc streamoff!!\n");
        return;
    }

    g_video_status.bStateType = VIDEO_STEAM_INTFACE_TYPE;
    g_video_status.bOriginator = 0x01;
    g_video_status.bEvent = 0x00;
    g_video_status.bSelector_Value = 0x01;

    /* usb_video_status((u8 *)&g_video_status, VIDEO_CTL_INTFACE_TYPE); */

    /* g_still_image_flag = 1; */

    /* while (g_still_image_flag); */

    usb_g_intr_write(usb_id, VIDEO_STATUS_EP_IN, (const u8 *)&g_video_status, 4);

    /* g_video_status.bSelector_Value = 0x00; */
    /* usb_video_status((u8 *)&g_video_status, VIDEO_CTL_INTFACE_TYPE); */
    /* usb_g_intr_write(usb_id, VIDEO_STATUS_EP_IN, (const u8 *)&g_video_status, 4); */
    usb_log_info("shutter end....\r\n");

#else
    usb_log_info("shutter start....\n");
    g_still_image_flag = 1;
    usb_log_info("shutter end....\r\n");
#endif
}










#ifdef ISP0_EN

#include "isp_alg.h"
#include "isp_customize.h"
/*
Table 4-3 Defined Bits Containing Capabilities of the Control
D0 1=Supports GET value requests Capability
D1 1=Supports SET value requests Capability
D2 1=Disabled due to automatic mode (under device control) State
D3 1= Autoupdate Control (see section 2.4.2.2 "Status Interrupt Endpoint") Capability
D4 1= Asynchronous Control (see sections 2.4.2.2 "Status Interrupt Endpoint" and 2.4.4, “Control Transfer and Request Processing”) Capability
D7..D5 Reserved (Set to 0)
*/
static void get_isp_brightness(s16 *val)
{
    ispt_get_brightness(val);
}
static void get_isp_contrast(s16 *val)
{
    ispt_get_contrast(val);
}
static void get_isp_saturation(u32 *val)
{
    ispt_get_saturation_u(val);
}
static void get_isp_sharpness(s16 *val)
{
    isp_shp_t shp;
    ispt_get_shp(&shp);
    *val = (s16)shp.a0;
}
static void get_isp_gamma(s16 *val)
{
    u8 gamma[256];
    ispt_get_gamma(gamma);
    s16 g = gamma[128];
    s16 g0 = 128;
    s16 g1 = 224;
    s16 g2 = 249;

    /* for(int i = 0 ; i < 16; i ++){ */
    /* printf("%3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d\n", */
    /* gamma[i*16 + 0], */
    /* gamma[i*16 + 1], */
    /* gamma[i*16 + 2], */
    /* gamma[i*16 + 3], */
    /* gamma[i*16 + 4], */
    /* gamma[i*16 + 5], */
    /* gamma[i*16 + 6], */
    /* gamma[i*16 + 7], */
    /* gamma[i*16 + 8], */
    /* gamma[i*16 + 9], */
    /* gamma[i*16 + 10], */
    /* gamma[i*16 + 11], */
    /* gamma[i*16 + 12], */
    /* gamma[i*16 + 13], */
    /* gamma[i*16 + 14], */
    /* gamma[i*16 + 15] */
    /* ); */
    /* } */

    if (g <= g0) {
        *val = 10;
    } else if (g <= g1) {
        *val = (g - g0) * 10 / (g1 - g0) + 10;
    } else if (g < g2) {
        *val = (g - g1) * 10 / (g2 - g1) + 20;
    } else {
        *val = 30;
    }
}
static void get_isp_gain(s16 *val)
{
    ispt_get_ev(val);
    *val += 4;
}
static void get_isp_powerlinefrequency(s8 *val)
{
    *val = 2;
}




static void set_isp_brightness(s16 val)
{
    stop_update_isp_scenes();
    /* printf("set_brightness %d\n", val); */
    ispt_set_brightness(val);
    ispt_params_flush();
}
static void set_isp_contrast(s16 val)
{
    stop_update_isp_scenes();
    /* printf("set_contrast %d\n", val); */
    ispt_set_contrast(val);
    ispt_params_flush();
}
static void set_isp_saturation(u32 val)
{
    stop_update_isp_scenes();
    /* printf("set_saturation %d\n", val); */
    ispt_set_saturation_u(val);
    ispt_set_saturation_v(val);
    ispt_params_flush();
}
static void set_isp_sharpness(s16 val)
{
    isp_shp_t shp;
    stop_update_isp_scenes();
    /* printf("set_sharpness %d\n", val); */
    ispt_get_shp(&shp);
    shp.a0 = val;
    shp.a1 = val;
    ispt_set_shp(&shp);
    ispt_params_flush();
}
static void set_isp_gamma(s16 val)
{
    stop_update_isp_scenes();
    /* printf("set_gamma %d\n", val); */
#if 0
    u8 *gamma = NULL;

    if (val < 10 || val > 30) {
        return 0;
    }

    if (val % 10) {
        u8 *g0 = gamma_table[val / 10 - 1];
        u8 *g1 = gamma_table[val / 10];
        int d = val % 10;
        gamma = gamma_tmp;
        for (int i = 0; i < 256; i++) {
            gamma[i] = g0[i] + (g1[i] - g0[i]) * d / 10;
        }
    } else {
        gamma = gamma_table[val / 10 - 1];
    }
    /* for(int i = 0 ; i < 16; i ++){ */
    /* printf("%3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d %3d\n", */
    /* gamma[i*16 + 0], */
    /* gamma[i*16 + 1], */
    /* gamma[i*16 + 2], */
    /* gamma[i*16 + 3], */
    /* gamma[i*16 + 4], */
    /* gamma[i*16 + 5], */
    /* gamma[i*16 + 6], */
    /* gamma[i*16 + 7], */
    /* gamma[i*16 + 8], */
    /* gamma[i*16 + 9], */
    /* gamma[i*16 + 10], */
    /* gamma[i*16 + 11], */
    /* gamma[i*16 + 12], */
    /* gamma[i*16 + 13], */
    /* gamma[i*16 + 14], */
    /* gamma[i*16 + 15] */
    /* ); */
    /* } */
    ispt_set_gamma(gamma);
    ispt_params_flush();
#endif
}
static void set_isp_gain(s16 val)
{
    stop_update_isp_scenes();
    /* printf("set_gain %d\n", val); */
    ispt_set_ev(val - 4);
    ispt_params_flush();
}
static void set_isp_powerlinefrequency(s8 val)
{
    /* printf("set_powerlinefrequency %d\n", val); */
}


#else
static void get_isp_brightness(s16 *val) {}
static void get_isp_contrast(s16 *val) {}
static void get_isp_saturation(u32 *val) {}
static void get_isp_sharpness(s16 *val) {}
static void get_isp_gamma(s16 *val) {}
static void get_isp_gain(s16 *val) {}
static void get_isp_powerlinefrequency(s8 *val) {}
static void set_isp_brightness(s16 val) {}
static void set_isp_contrast(s16 val) {}
static void set_isp_saturation(u32 val) {}
static void set_isp_sharpness(s16 val) {}
static void set_isp_gamma(s16 val) {}
static void set_isp_gain(s16 val) {}
static void set_isp_powerlinefrequency(s8 val) {}
#endif




void uvc_vc_pu_wBacklightCompensation_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;
    ////usb_log_info("111---%s() %d,%x\n", __func__, __LINE__,req->bRequest);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBacklightCompensation[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBacklightCompensation[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBacklightCompensation[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBacklightCompensation[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wBacklightCompensation_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wBacklightCompensation[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wBrightness_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_brightness(&video_ctl_value[usb_id].wBrightness[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBrightness[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBrightness[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBrightness[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wBrightness[UVC_DEF], req->wLength);
        usb_device->wDataLength = req->wLength;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wBrightness_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wBrightness[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_brightness(video_ctl_value[usb_id].wBrightness[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wContrast_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_contrast(&video_ctl_value[usb_id].wContrast[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wContrast[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wContrast[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wContrast[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wContrast[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wContrast_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wContrast[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_contrast(video_ctl_value[usb_id].wContrast[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wGain_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_gain(&video_ctl_value[usb_id].wGain[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGain[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGain[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGain[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGain[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wGain_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wGain[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_gain(video_ctl_value[usb_id].wGain[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bPowerLineFrequency_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bPowerLineFrequency[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bPowerLineFrequency[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_bPowerLineFrequency_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].bPowerLineFrequency[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wHue_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wHue[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wHue[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wHue[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wHue[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wHue_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wHue[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wSaturation_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_saturation((u32 *)&video_ctl_value[usb_id].wSaturation[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSaturation[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSaturation[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSaturation[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSaturation[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wSaturation_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wSaturation[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_saturation(video_ctl_value[usb_id].wSaturation[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wSharpness_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_sharpness(&video_ctl_value[usb_id].wSharpness[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSharpness[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSharpness[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSharpness[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wSharpness[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wSharpness_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wSharpness[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_sharpness(video_ctl_value[usb_id].wSharpness[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wGamma_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        get_isp_gamma(&video_ctl_value[usb_id].wGamma[UVC_CUR]);
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGamma[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGamma[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGamma[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x0a;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wGamma[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wGamma_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wGamma[UVC_CUR], usb_device->setup_ptr, req->wLength);
            set_isp_gamma(video_ctl_value[usb_id].wGamma[UVC_CUR]);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wWhiteBalanceTemperature_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wWhiteBalanceTemperature_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wWhiteBalanceTemperature[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bWhiteBalanceTemperatureAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_bWhiteBalanceTemperatureAuto_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].bWhiteBalanceTemperatureAuto[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_white_balance_component_control_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].pu_white_balance_component_control[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].pu_white_balance_component_control[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].pu_white_balance_component_control[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].pu_white_balance_component_control[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_white_balance_component_control_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].pu_white_balance_component_control[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bWhiteBalanceComponentAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_bWhiteBalanceComponentAuto_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].bWhiteBalanceComponentAuto[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wMultiplierStep_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierStep[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierStep[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierStep[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierStep[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wMultiplierStep_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wMultiplierStep[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_wMultiplierLimit_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierLimit[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierLimit[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierLimit[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 0x01;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].wMultiplierLimit[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_wMultiplierLimit_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].wMultiplierLimit[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bHueAuto_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bHueAuto[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bHueAuto[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_pu_bHueAuto_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&video_ctl_value[usb_id].bHueAuto[UVC_CUR], usb_device->setup_ptr, req->wLength);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bVideoStandard_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bVideoStandard[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_SET_CUR:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vc_pu_bStatus_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &video_ctl_value[usb_id].bStatus[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_MAX:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_RES:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    case UVC_SET_CUR:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}
/**
 * @note    The setting for the attribute of the
 *          addressed Auto-Exposure Mode
 *          Control:
 *          D0: Manual Mode – manual
 *          Exposure Time, manual Iris
 *          D1: Auto Mode – auto Exposure
 *          Time, auto Iris
 *          D2: Shutter Priority Mode –
 *          manual Exposure Time, auto Iris
 *          D3: Aperture Priority Mode – auto
 *          Exposure Time, manual Iris
 *          D4..D7: Reserved, set to zero
 */
static u8 auto_exposure_mode = 0x08;

void uvc_vc_it_auto_exposure_mode_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        usb_device->setup_ptr[0] = auto_exposure_mode;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        /* value = 1; */
        value = 0x09;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        usb_device->setup_ptr[0] = 0x08;  //default auto exposure time, auto iris
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vc_it_auto_exposure_mode_handle;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            auto_exposure_mode = usb_device->setup_ptr[0];
            usb_log_info("auto exposure mode=%d\n", auto_exposure_mode);
            if (auto_exposure_mode & BIT(1) || auto_exposure_mode & BIT(3)) {
#ifdef ISP0_EN
                //auto
                s32 isp_ae_alg_thaw();
                isp_ae_alg_thaw();
#endif

            } else if (auto_exposure_mode & BIT(0) || auto_exposure_mode & BIT(2)) {
#ifdef ISP0_EN
                //manual
                s32 isp_ae_alg_freeze();
                isp_ae_alg_freeze();
#endif
            }

        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

//unit in 100us, 10 means 1/1000 of a second
static u32 exposure_time_table[4] = {
    [UVC_CUR] = 80,
    [UVC_MIN] = 80,
    [UVC_MAX] = 100000,
    [UVC_DEF] = 0,
};

void uvc_vc_it_exposure_time_absolutive_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 value;
    struct sys_msg msg = {0};

    usb_log_info("%s() %d\n", __func__, __LINE__);
    switch (req->bRequest) {
    case UVC_GET_CUR:
        memcpy(usb_device->setup_ptr, &exposure_time_table[UVC_CUR], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
        memcpy(usb_device->setup_ptr, &exposure_time_table[UVC_MIN], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        memcpy(usb_device->setup_ptr, &exposure_time_table[UVC_MAX], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        value = 1;
        memcpy(usb_device->setup_ptr, &value, req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x0F;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_DEF:
        exposure_time_table[UVC_DEF] = exposure_time_table[UVC_MIN];
        memcpy(usb_device->setup_ptr, &exposure_time_table[UVC_DEF], req->wLength);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            if (auto_exposure_mode & (BIT(1) | BIT(3))) {
                usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
            } else {
                usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
                uvc_handle->uvc_status = (u32)uvc_vc_it_exposure_time_absolutive_handle;
            }
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            memcpy(&exposure_time_table[UVC_CUR], usb_device->setup_ptr, req->wLength);
            msg.type = SYS_MSG_EVENT;
            msg.event = MSG_UVC_AE_CONTROL;
            msg.value = exposure_time_table[UVC_CUR];
            sys_msg_post(&msg);
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    default:
        usb_set_setup_phase(usb_device, USB_EP0_SET_STALL);
    }
}

void uvc_vs_probe_commit_ctl_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 i, len = 0;

    switch (req->bRequest) {
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vs_probe_commit_ctl_handle;
            return;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            uvc_slave_probe_commit_control[0] = usb_device->setup_ptr[0];  //bmHint
            uvc_slave_probe_commit_control[1] = usb_device->setup_ptr[1];
            uvc_slave_probe_commit_control[2] = usb_device->setup_ptr[2];  //bFormatIndex
            uvc_slave_probe_commit_control[3] = usb_device->setup_ptr[3];  //bFrameIndex
            uvc_slave_probe_commit_control[4] = usb_device->setup_ptr[4];  //dwFrameInterval
            uvc_slave_probe_commit_control[5] = usb_device->setup_ptr[5];
            uvc_slave_probe_commit_control[6] = usb_device->setup_ptr[6];
            uvc_slave_probe_commit_control[7] = usb_device->setup_ptr[7];
#if !UVC_ISO_MODE
            if (HIBYTE(req->wValue) == VS_COMMIT_CONTROL) {
                usb_video_close_camera(usb_id);
                usb_video_open_camera(usb_id, 0);
            }
#endif
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    case UVC_GET_CUR:
        len = sizeof(uvc_slave_probe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < len; i++) {
            usb_device->setup_ptr[i] = uvc_slave_probe_commit_control[i];
        }
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
    case UVC_GET_DEF:
        len = sizeof(uvc_slave_probe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < usb_device->wDataLength; i++) {
            usb_device->setup_ptr[i] = uvc_slave_probe_commit_control[i];
        }
        usb_device->setup_ptr[2] = 1;
        usb_device->setup_ptr[3] = 1;
        usb_device->setup_ptr[4] = DW1BYTE(FRAME_FPS);
        usb_device->setup_ptr[5] = DW2BYTE(FRAME_FPS);
        usb_device->setup_ptr[6] = DW3BYTE(FRAME_FPS);
        usb_device->setup_ptr[7] = DW4BYTE(FRAME_FPS);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        len = sizeof(uvc_slave_probe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < usb_device->wDataLength; i++) {
            usb_device->setup_ptr[i] = uvc_slave_probe_commit_control[i];
        }
        usb_device->setup_ptr[2] = 1;
        usb_device->setup_ptr[3] = 5;
        usb_device->setup_ptr[4] = DW1BYTE(FRAME_FPS * 2);
        usb_device->setup_ptr[5] = DW2BYTE(FRAME_FPS * 2);
        usb_device->setup_ptr[6] = DW3BYTE(FRAME_FPS * 2);
        usb_device->setup_ptr[7] = DW4BYTE(FRAME_FPS * 2);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        len = sizeof(uvc_slave_probe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        memset(usb_device->setup_ptr, 0, usb_device->wDataLength);
        usb_device->setup_ptr[2] = 1;
        usb_device->setup_ptr[3] = 1;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_LEN:
        len = req->wLength;
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        *(u32 *)usb_device->setup_ptr = sizeof(uvc_slave_probe_commit_control);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    }

    usb_log_info("vs probe commit control data\n");
    if (usb_device->bsetup_phase == USB_EP0_STAGE_IN) {
        usb_put_buf(usb_device->setup_ptr, usb_device->wDataLength);
    } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
        usb_put_buf(uvc_slave_probe_commit_control, sizeof(uvc_slave_probe_commit_control));
    }
}

void uvc_vs_still_probe_commit_ctl_handle(usb_dev usb_id, struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 i, len = 0;

    switch (req->bRequest) {
    case UVC_SET_CUR:
        if (usb_device->bsetup_phase == USB_EP0_STAGE_SETUP) {
            usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
            uvc_handle->uvc_status = (u32)uvc_vs_still_probe_commit_ctl_handle;
            return;
        } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
            uvc_slave_stillprobe_commit_control[0] = usb_device->setup_ptr[0];  //bFormatIndex
            uvc_slave_stillprobe_commit_control[1] = usb_device->setup_ptr[1];  //bFrameIndex
            uvc_slave_stillprobe_commit_control[2] = usb_device->setup_ptr[2];  //bCompressionIndex
        } else {
            usb_log_info("setup state err\n");
        }
        break;
    case UVC_GET_CUR:
        len = sizeof(uvc_slave_stillprobe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < usb_device->wDataLength; i++) {
            usb_device->setup_ptr[i] = uvc_slave_stillprobe_commit_control[i];
        }
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MIN:
    case UVC_GET_DEF:
        len = sizeof(uvc_slave_stillprobe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < usb_device->wDataLength; i++) {
            usb_device->setup_ptr[i] = uvc_slave_stillprobe_commit_control[i];
        }
        usb_device->setup_ptr[0] = 1;  //bFormatIndex
        usb_device->setup_ptr[1] = 1;  //bFrameIndex
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_MAX:
        len = sizeof(uvc_slave_stillprobe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        for (i = 0; i < usb_device->wDataLength; i++) {
            usb_device->setup_ptr[i] = uvc_slave_stillprobe_commit_control[i];
        }
        usb_device->setup_ptr[0] = 1;
        usb_device->setup_ptr[1] = 5;  //bFrameIndex
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_RES:
        len = sizeof(uvc_slave_stillprobe_commit_control);
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        memset(usb_device->setup_ptr, 0, usb_device->wDataLength);
        usb_device->setup_ptr[0] = 1;
        usb_device->setup_ptr[1] = 1;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_LEN:
        len = req->wLength;
        usb_device->wDataLength = req->wLength > len ? len : req->wLength;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        *(u32 *)usb_device->setup_ptr = sizeof(uvc_slave_stillprobe_commit_control);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    case UVC_GET_INFO:
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_device->setup_ptr[0] = 0x03;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        break;
    }

    usb_log_info("vs still probe commit control data\n");
    if (usb_device->bsetup_phase == USB_EP0_STAGE_IN) {
        usb_put_buf(usb_device->setup_ptr, usb_device->wDataLength);
    } else if (usb_device->bsetup_phase == USB_EP0_STAGE_OUT) {
        usb_put_buf(uvc_slave_stillprobe_commit_control, sizeof(uvc_slave_stillprobe_commit_control));
    }
}

void user_uvc_set_camera_info(usb_dev usb_id, const struct usb_camera *info)
{
    int i;
    u16 width, height;
    u16 fps, fps2;
    u8 jpg_terminate = 0, yuv_terminate = 0;
    if (uvc_handle != NULL) {
        uvc_handle->video_open = info->video_open;
        uvc_handle->video_reqbuf = info->video_reqbuf;
        uvc_handle->video_reqbuf2 = info->video_reqbuf2;
        uvc_handle->video_close =  info->video_close;
        uvc_handle->processing_unit_response = info->processing_unit_response;
        uvc_handle->private_cmd_response = info->private_cmd_response;
        for (i = 0; i < FMT_MAX_RESO_NUM; i++) {
            uvc_handle->width[i] = 0;
            uvc_handle->height[i] = 0;
            uvc_handle->fps[i] = 0;
            uvc_handle->fps2[i] = 0;
            if (info->jpg_info && i < info->jpg_info->num) {
                width = info->jpg_info->reso[i].width;
                height = info->jpg_info->reso[i].height;
                fps = info->jpg_info->reso[i].fps;
                fps2 = info->jpg_info->reso[i].fps2;
                if (!jpg_terminate && (width && height)) {
                    uvc_handle->width[i] = width;
                    uvc_handle->height[i] = height;
                    uvc_handle->fps[i] = fps;
                    uvc_handle->fps2[i] = fps2;
                } else {
                    jpg_terminate = 1;
                }
            }
            uvc_handle->width[FMT_MAX_RESO_NUM + i] = 0;
            uvc_handle->height[FMT_MAX_RESO_NUM + i] = 0;
            uvc_handle->fps[FMT_MAX_RESO_NUM + i] = 0;
            uvc_handle->fps2[FMT_MAX_RESO_NUM + i] = 0;
            if (info->yuv_info && i < info->yuv_info->num) {
                width = info->yuv_info->reso[i].width;
                height = info->yuv_info->reso[i].height;
                fps = info->yuv_info->reso[i].fps;
                fps2 = info->yuv_info->reso[i].fps2;
                if (!yuv_terminate && (width && height)) {
                    uvc_handle->width[FMT_MAX_RESO_NUM + i] = width;
                    uvc_handle->height[FMT_MAX_RESO_NUM + i] = height;
                    uvc_handle->fps[FMT_MAX_RESO_NUM + i] = fps;
                    uvc_handle->fps2[FMT_MAX_RESO_NUM + i] = fps2;
                } else {
                    yuv_terminate = 1;
                }
            }
        }
    }
}

u32 user_uvc_register(usb_dev usb_id, void *ex_uvc_handle)
{
    u32 err = 0;
    if (uvc_handle == NULL) {
#if USB_MALLOC_ENABLE
        uvc_handle = malloc(sizeof(struct usb_uvc_camera));
        if (uvc_handle == NULL) {
            return (u32) - 1;
        }
        memset(uvc_handle, 0, sizeof(struct usb_uvc_camera));
#else
        if (ex_uvc_handle == NULL) {
            uvc_handle = &_uvc_handle;
            memset(uvc_handle, 0, sizeof(struct usb_uvc_camera));
        }
#endif
    }
    usb_video_feature_init(usb_id);
    if (ex_uvc_handle == NULL) {
#if UVC_ISO_MODE
        uvc_handle->usb_packet_size = UVC_FIFO_TXMAXP;
#else
        uvc_handle->usb_packet_size = MJPG_WHEIGHT_0 * MJPG_WWIDTH_0 ;
#endif

        printf(">>>> usb packet size = %d\n", uvc_handle->usb_packet_size);
    }

    return 0;

__err_exit:
#if USB_MALLOC_ENABLE
    free(uvc_handle);
#endif
    uvc_handle = NULL;
    return (u32) - 1;
}

u32 user_uvc_release(usb_dev usb_id)
{
    if (uvc_handle) {
        if (uvc_handle->video_close) {
            uvc_handle->video_close(usb_id);
        }
#if USB_MALLOC_ENABLE
//        if (uvc_handle->stream_buf) {
//            free(uvc_handle->stream_buf);
//        }
        free(uvc_handle);
#else
//        uvc_handle->stream_buf = NULL;
#endif
        uvc_handle = NULL;
    }
    return 0;
}

#endif
#endif
struct usb_config_var_t {
    u8 usb_setup_buffer[USB_SETUP_SIZE];
    struct usb_ep_addr_t usb_ep_addr;
    struct usb_setup_t usb_setup;
};

struct usb_config_var_t *get_usb_config_var_address(void)
{
    u8 chip_ver = get_chip_version();
    if (chip_ver == 0) {
        //A版
        return (struct usb_config_var_t *)(0x1f6a8);
    } else if (chip_ver == 1) {
        //B版
        return (struct usb_config_var_t *)(0x1f3e0);
    }

    return NULL;
}
struct usb_setup_t *get_usb_setup_address(void)
{
    struct usb_config_var_t *usb_config_var  = get_usb_config_var_address();
    return (struct usb_setup_t *)&usb_config_var->usb_setup;
}
struct usb_device_t *usb_id2device(usb_dev usb_id)
{
    struct usb_config_var_t *usb_config_var  = get_usb_config_var_address();
    struct usb_setup_t *usb_setup = &usb_config_var->usb_setup;;
    return (struct usb_device_t *)&usb_setup->usb_device;
}


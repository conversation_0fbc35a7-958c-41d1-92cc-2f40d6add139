
#define USB_DBG_LEV 1

#include "generic/includes.h"
#include "core.h"
#include "app_config.h"
#include "app_msg.h"
#include "usb/usb.h"
#include "usb/usb_config.h"
#include "usb/device/usb_stack.h"
#include "usb/device/uac_audio.h"
#include "usb/device/msd.h"
#include "usb/device/slave_uvc.h"
#include "usb/device/descriptor.h"
#include "uvc.h"
#include "video.h"
#include "clock.h"
#include "cpu.h"


#define UVC_USER_EXTENSION_ENABLE

//自定义命令
enum {
    CMD_UVC_EXT_NONE = 0,
    CMD_UVC_EXT_CAM_NAME = 1,   //camera name
    CMD_UVC_EXT_FW_VERSION = 2, //fw version
    CMD_UVC_EXT_SN_NUMBER = 3,  //sn number
};

////////////////////////////////////
#define BUILD_MONTH ((BUILD_DATE_IS_BAD) ? 99 : GET_BUILD_MONTH)
#define BUILD_DAY   ((BUILD_DATE_IS_BAD) ? 99 : GET_BUILD_DAY)
#define BUILD_TIME_IS_BAD (__TIME__[0] == '?')
#define BUILD_HOUR  ((BUILD_TIME_IS_BAD) ? 99 : GET_BUILD_HOUR)
#define BUILD_MIN   ((BUILD_TIME_IS_BAD) ? 99 : GET_BUILD_MINUTE)
#define BUILD_SEC   ((BUILD_TIME_IS_BAD) ? 99 : GET_BUILD_SECOND)

#define BUILD_MONTH_IS_JAN (__DATE__[0] == 'J' && __DATE__[1] == 'a' && __DATE__[2] == 'n')
#define BUILD_MONTH_IS_FEB (__DATE__[0] == 'F')
#define BUILD_MONTH_IS_MAR (__DATE__[0] == 'M' && __DATE__[1] == 'a' && __DATE__[2] == 'r')
#define BUILD_MONTH_IS_APR (__DATE__[0] == 'A' && __DATE__[1] == 'p')
#define BUILD_MONTH_IS_MAY (__DATE__[0] == 'M' && __DATE__[1] == 'a' && __DATE__[2] == 'y')
#define BUILD_MONTH_IS_JUN (__DATE__[0] == 'J' && __DATE__[1] == 'u' && __DATE__[2] == 'n')
#define BUILD_MONTH_IS_JUL (__DATE__[0] == 'J' && __DATE__[1] == 'u' && __DATE__[2] == 'l')
#define BUILD_MONTH_IS_AUG (__DATE__[0] == 'A' && __DATE__[1] == 'u')
#define BUILD_MONTH_IS_SEP (__DATE__[0] == 'S')
#define BUILD_MONTH_IS_OCT (__DATE__[0] == 'O')
#define BUILD_MONTH_IS_NOV (__DATE__[0] == 'N')
#define BUILD_MONTH_IS_DEC (__DATE__[0] == 'D')

#define GET_BUILD_YEAR \
    ( \
        (__DATE__[9] - '0') * 10 + \
        (__DATE__[10] - '0') \
    )

#define GET_BUILD_MONTH \
    ( \
        (BUILD_MONTH_IS_JAN) ? 1 : \
        (BUILD_MONTH_IS_FEB) ? 2 : \
        (BUILD_MONTH_IS_MAR) ? 3 : \
        (BUILD_MONTH_IS_APR) ? 4 : \
        (BUILD_MONTH_IS_MAY) ? 5 : \
        (BUILD_MONTH_IS_JUN) ? 6 : \
        (BUILD_MONTH_IS_JUL) ? 7 : \
        (BUILD_MONTH_IS_AUG) ? 8 : \
        (BUILD_MONTH_IS_SEP) ? 9 : \
        (BUILD_MONTH_IS_OCT) ? 10 : \
        (BUILD_MONTH_IS_NOV) ? 11 : \
        (BUILD_MONTH_IS_DEC) ? 12 : \
        99 \
    )

#define GET_BUILD_DAY \
    ( \
        ((__DATE__[4] >= '0') ? (__DATE__[4] - '0') * 10 : 0) + \
        (__DATE__[5] - '0') \
    )

#define GET_BUILD_HOUR    ((__TIME__[0] - '0') * 10 + __TIME__[1] - '0')
#define GET_BUILD_MINUTE  ((__TIME__[3] - '0') * 10 + __TIME__[4] - '0')
#define GET_BUILD_SECOND  ((__TIME__[6] - '0') * 10 + __TIME__[7] - '0')

#define BUILD_DATE_IS_BAD (__DATE__[0] == '?')
#define BUILD_YEAR  ((BUILD_DATE_IS_BAD) ? 99 : GET_BUILD_YEAR)
#define BUILD_MONTH ((BUILD_DATE_IS_BAD) ? 99 : GET_BUILD_MONTH)
/////////////////////////////////////

static u32 uvc_user_extension_set_cur(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);
static u32 uvc_user_extension_get_cur(struct usb_device_t *usb_device, struct usb_ctrlrequest *req);



/**    @brief   uvc 扩展单元通信接口函数
 *     @param   usb_device:usb设备句柄
       @param   req: 请求setup 包
       @return  1: 接管setup包不往下处理  0:继续往下处理
       @note
**/
int uvc_user_extension_unit(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
#ifndef UVC_USER_EXTENSION_ENABLE
    return 0;
#endif

    usb_device->wDataLength = req->wLength;
    usb_device->setup_ptr = usb_device->setup_buffer;
    usb_device->bDataOverFlag = 0;
    usb_set_setup_phase(usb_device, USB_EP0_STAGE_SETUP);

    switch (req->bRequest) {
    case UVC_SET_CUR:
        usb_log_info("UVC_SET_CUR");
        usb_set_setup_recv(usb_device, uvc_user_extension_set_cur);
        usb_device->wDataLength = req->wLength;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_OUT);
        return 1;
    case UVC_GET_CUR:
        usb_log_info("UVC_GET_CUR");
        uvc_user_extension_get_cur(usb_device, req);
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    case UVC_GET_MIN:
        usb_log_info("UVC_GET_MIN");
        *(u16 *)usb_device->setup_buffer = 0x0001;
        usb_device->wDataLength = 4;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    case UVC_GET_MAX:
        usb_log_info("UVC_GET_MAX");
        *(u16 *)usb_device->setup_buffer = 0xFFFF;
        usb_device->wDataLength = 4;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    case UVC_GET_LEN: /*每报包数据默认(最大)长度*/
        usb_log_info("UVC_GET_LEN");
        *(u16 *)usb_device->setup_buffer = 0x0020;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    case UVC_GET_DEF:
    case UVC_GET_RES:
        *(u16 *)usb_device->setup_buffer = 0x0001;
        usb_device->wDataLength = 4;
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    case UVC_GET_INFO:
        usb_device->setup_buffer[0] = 0x03; /*Supports GET value requests /  Supports SET value requests */
        usb_device->wDataLength = 1;
        if (usb_device->wDataLength < req->wLength) {
            usb_device->bDataOverFlag = 1;
        }
        usb_set_setup_phase(usb_device, USB_EP0_STAGE_IN);
        return 1;
    default:
        break;
    }
    return 0;
}

static u32 uvc_user_extension_set_cur(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    const usb_dev usb_id = usb_device2id(usb_device);
    u32 _len = req->wLength;

    usb_device->setup_ptr = usb_device->setup_buffer;
    _len = usb_read_count0(usb_id);
    usb_read_ep0(usb_id, usb_device->setup_ptr, _len);
    usb_log_info("uvc_user_extension_set [%d]:", HIBYTE(req->wValue));
    usb_put_buf(usb_device->setup_ptr, _len);
    switch (HIBYTE(req->wValue)) {
    case CMD_UVC_EXT_SN_NUMBER: {

    }
    break;
    default:
        break;
    }

    return USB_EP0_STAGE_SETUP;
}
static u32 uvc_user_extension_get_cur(struct usb_device_t *usb_device, struct usb_ctrlrequest *req)
{
    u32 _len = req->wLength;
    usb_log_info("uvc_user_extension_get [%d]:", HIBYTE(req->wValue));
    switch (HIBYTE(req->wValue)) {
    case CMD_UVC_EXT_CAM_NAME: {
        isp_sen_t *c;
        void *camera_fd = get_video_device();
        void *isp_sen;
        char *no_camera = "no camera";
        if (camera_fd) {
            //获取当前sensor句柄
            dev_ioctl(camera_fd, CAMERA_GET_SENSOR_HANDLE, (u32)&isp_sen);
            c = (isp_sen_t *)isp_sen;
            strcpy((char *)usb_device->setup_buffer, (char *)c->logo); //获取sensor 名字
            usb_device->wDataLength = strlen((char *)c->logo);
        } else {
            strcpy((char *)usb_device->setup_buffer, no_camera);
            usb_device->wDataLength = strlen(no_camera);
        }
    }
    break;
    case CMD_UVC_EXT_FW_VERSION: {
        char version[64];
        #if FLIP
        sprintf(version,"%s_%02d%02d%02d",SOFTWARE_VERSION_PREFIX, BUILD_YEAR, BUILD_MONTH,BUILD_DAY);
        #else
        sprintf(version,"%s_R_%02d%02d%02d",SOFTWARE_VERSION_PREFIX, BUILD_YEAR, BUILD_MONTH,BUILD_DAY);
        #endif
        version[32] = '\0'; // NOTE: maximum strlen is 32, and no snprintf implemented
       //// sprintf(version,"%s%04d%02d%02d","HW_5316_V0006_S01_20230529");
        char *fw_version = "HW5316_V0006_H63P_20230927";
        strcpy((char *)usb_device->setup_buffer, version/*fw_version*/);
        usb_device->wDataLength = strlen(version/*fw_version*/);
    }
    break;
    case CMD_UVC_EXT_SN_NUMBER:
        break;
    default:
        break;
    }

    return 0;
}

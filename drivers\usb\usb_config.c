
#define USB_DBG_LEV 1
#include "app_config.h"

#include "cpu.h"
#include "usb/usb.h"
#include "usb/usb_config.h"
#include "usb/device/usb_stack.h"
#include "usb/device/msd.h"
#include "usb/device/slave_uvc.h"

#define     MAX_EP_TX   7
#define     MAX_EP_RX   7

void usb_isr(usb_dev usb_id)
{
    u8 chip_ver = get_chip_version();

    usb_interrupt usb_interrupt_tx[USB_MAX_HW_NUM][MAX_EP_TX];
    usb_interrupt usb_interrupt_rx[USB_MAX_HW_NUM][MAX_EP_RX];

    if (chip_ver == 0) {
        //A版
        memcpy(usb_interrupt_tx, (void *)0x1f66c, sizeof(usb_interrupt) * USB_MAX_HW_NUM * MAX_EP_TX);
        memcpy(usb_interrupt_rx, (void *)0x1f688, sizeof(usb_interrupt) * USB_MAX_HW_NUM * MAX_EP_RX);
    } else if (chip_ver == 1) {
        //B版
        memcpy(usb_interrupt_tx, (void *)0x1f3a4, sizeof(usb_interrupt) * USB_MAX_HW_NUM * MAX_EP_TX);
        memcpy(usb_interrupt_rx, (void *)0x1f3c0, sizeof(usb_interrupt) * USB_MAX_HW_NUM * MAX_EP_RX);
    }

    u32 intr_usb, intr_usbe;
    u32 intr_tx, intr_txe;
    u32 intr_rx, intr_rxe;

    __asm__ volatile("ssync");
    usb_read_intr(usb_id, &intr_usb, &intr_tx, &intr_rx);
    usb_read_intre(usb_id, &intr_usbe, &intr_txe, &intr_rxe);
    struct usb_device_t *usb_device = usb_id2device(usb_id);
    /* printf("intrusb %02x, intrtx %04x, intrrx %04x\n", intr_usb, intr_tx, intr_rx); */
    /* printf("intrusbe %02x, intrtxe %04x, intrrxe %04x\n", intr_usbe, intr_txe, intr_rxe); */

    intr_usb &= intr_usbe;
    intr_tx &= intr_txe;
    intr_rx &= intr_rxe;

    if (intr_usb & INTRUSB_SUSPEND) {
        usb_log_error("usb suspend");
        // disable pa power
        {
            extern void audio_spk_close_cb();
            audio_spk_close_cb();
        }
#if USB_SUSPEND_RESUME
        usb_phy_suspend(usb_id);
#endif
    }
    if (intr_usb & INTRUSB_RESET_BABBLE) {
        usb_log_error("usb reset");
        // disable pa power
        {
            extern void audio_spk_close_cb();
            audio_spk_close_cb();
        }
        extern void lrc_trim_reset();
        lrc_trim_reset();
        usb_reset_interface(usb_device);

#if USB_SUSPEND_RESUME
        u32 reg = usb_read_power(usb_id);
        usb_write_power(usb_id, (reg | INTRUSB_SUSPEND | INTRUSB_RESUME));//enable suspend resume
#endif
    }

    if (intr_usb & INTRUSB_RESUME) {
        usb_log_error("usb resume");
#if USB_SUSPEND_RESUME
        usb_phy_resume(usb_id);
#endif
    }

    if (intr_tx & BIT(0)) {
#if 0
        // Test
        putchar('.');
        u32 csr0 = usb_read_csr0(usb_id);
        u8 setup_pkt[8];
        if (csr0 & CSR0P_RxPktRdy) {
            if (usb_device->bsetup_phase) {
                usb_read_ep0(usb_id, setup_pkt, 8);
                printf_buf(setup_pkt, 8);
            }
        }
#endif
        usb_control_transfer(usb_device);
    }

    for (int i = 1; i < MAX_EP_TX; i++) {
        if (intr_tx & BIT(i)) {
            if (usb_interrupt_tx[usb_id][i]) {
                usb_interrupt_tx[usb_id][i](usb_device, i);
            }
        }
    }

    for (int i = 1; i < MAX_EP_RX; i++) {
        if (intr_rx & BIT(i)) {
            if (usb_interrupt_rx[usb_id][i]) {
                usb_interrupt_rx[usb_id][i](usb_device, i);
            }
        }
    }
    __asm__ volatile("csync");
}
static const u8 sConfigDescriptor[] = {	//<Config Descriptor
//ConfiguraTIon
    USB_DT_CONFIG_SIZE,    //bLength
    USB_DT_CONFIG,    //DescriptorType : ConfigDescriptor
    0, 0, //TotalLength
    0,//bNumInterfaces: 在set_descriptor函数里面计算
    0x01,    //bConfigurationValue - ID of this configuration
    0x00,    //Unused
    0x80,    //Attributes:Bus Power
    200,     //MaxPower = 200*2ma
};
u32 set_descriptor(usb_dev usb_id, u32 class_config, u8 *ptr, u32 max_len)
{
    u8 chip_ver = get_chip_version();
    u32 cur_itf_num = 0;
    u8 *const p = ptr;

    u32 config_len = ptr - p;
    memcpy(ptr, sConfigDescriptor, USB_DT_CONFIG_SIZE);
    ptr += USB_DT_CONFIG_SIZE;
    if ((u32)(ptr - p) > max_len) {
        return 0;
    }

    desc_config desc;
    desc_config desc_config_list[USB_MAX_HW_NUM][MAX_INTERFACE_NUM];
    if (chip_ver == 0) {
        //A版
        memcpy(desc_config_list, (void *)0x1f994, sizeof(desc_config) * USB_MAX_HW_NUM * MAX_INTERFACE_NUM);
    } else if (chip_ver == 1) {
        //B版
        memcpy(desc_config_list, (void *)0x1f6cc, sizeof(desc_config) * USB_MAX_HW_NUM * MAX_INTERFACE_NUM);
    }
    for (int i = 0; i < MAX_INTERFACE_NUM; i++) {
        desc = desc_config_list[usb_id][i];
        if (desc == NULL) {
            break;
        }
        u32 ret = desc(usb_id, ptr, &cur_itf_num);

        if (ret) {
            ptr += ret;
        } else {
            ASSERT(ret, "usb set descriptor fail @func %x\n", desc);
            return 0;
        }

        if ((u32)(ptr - p) > max_len) {
            ASSERT(ret, "usb set descriptor fail no enough buf %x\n", desc);
            return 0;
        }
    }

    config_len = ptr - p;
    p[2] = LOBYTE(config_len);
    p[3] = HIBYTE(config_len);
    p[4] = cur_itf_num;
    return config_len;
}
SET_INTERRUPT
static void usb0_g_isr()
{
    usb_isr(0);
}
void usb_g_isr_reg(usb_dev usb_id, u8 priority, u8 cpu_id)
{
    if (usb_id == 0) {
        request_irq(IRQ_USB_SIE_IDX, priority, usb0_g_isr, cpu_id);
    } else {
#if USB_MAX_HW_NUM == 2
        request_irq(IRQ_USB1_SIE_IDX, priority, usb1_g_isr, cpu_id);
#endif
    }
}
void usb_ex_device_init(usb_dev usb_id)
{
    usb_log_info("usb ex device init");
    usb_config(usb_id);
    usb_g_sie_init(usb_id);
#ifdef HUSB_MODE
    usb_slave_init(usb_id, 0x60);
#else
    usb_slave_init(usb_id, 0x40);
#endif
    u8 *ep0_dma_buffer = usb_get_ep_buffer(usb_id, 0, USB_DIR_OUT);

    usb_set_dma_raddr(usb_id, 0, ep0_dma_buffer);
    usb_set_dma_raddr(usb_id, 1, ep0_dma_buffer);
    usb_set_dma_raddr(usb_id, 2, ep0_dma_buffer);
    usb_set_dma_raddr(usb_id, 3, ep0_dma_buffer);
    usb_set_dma_raddr(usb_id, 4, ep0_dma_buffer);

    usb_write_intr_usbe(usb_id, INTRUSB_RESET_BABBLE | INTRUSB_SUSPEND);
    usb_set_intr_txe(usb_id, 0);
    usb_set_intr_rxe(usb_id, 0);
    usb_g_isr_reg(usb_id, 3, 0);

}

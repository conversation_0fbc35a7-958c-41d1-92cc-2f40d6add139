#include "gpio.h"
#include "jiffies.h"
#include "app_config.h"
#include "usb_audio.h"
#include "audio.h"
#include "cbuf.h"
#include "usb_syn_api.h"
#include "isp_scenes.h"
#include "delay.h"
// #include <stdlib.h>
extern int abs( int n );

#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE || TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)

#define AUDIO_SR_POINT (128)  //(128) 8k使用128,16k使用256 20221229
#define AUDIO_SR_SIZE (AUDIO_SR_POINT * 2)

#define AUDIO_MIC_CBUF_SIZE    (1*1024)
#define AUDIO_MIC_DEV_BUF_SIZE (AUDIO_SR_SIZE * 2)
#define AUDIO_SPK_DEV_BUF_SIZE (AUDIO_SR_SIZE * 4)

#define UAC_SRC_ENABLE      0
////u8 dac_closed = 0; ////by frank for global symbol
#ifdef CONFIG_NLPFIX_ENABLE
#undef UAC_SRC_ENABLE
#define UAC_SRC_ENABLE      0
#endif

static usb_audio_spk_play_cb_t g_usb_audio_spk_play_cb = NULL;
static usb_audio_spk_close_cb_t g_usb_audio_spk_close_cb = NULL;
static u32 g_audio_frame_play_cnt = 0;

int usb_audio_register_spk_callback(usb_audio_spk_play_cb_t play_cb, usb_audio_spk_close_cb_t close_cb)
{
    int ret = 0;

    if (!g_usb_audio_spk_play_cb) {
        g_usb_audio_spk_play_cb = play_cb;
    } else {
        ret = -1;
        printf("g_usb_audio_spk_play_cb already registered!!!\n");
    }

    if (!g_usb_audio_spk_close_cb) {
        g_usb_audio_spk_close_cb = close_cb;
    } else {
        ret = -1;
        printf("g_usb_audio_spk_close_cb already registered!!!\n");
    }

    return ret;
}

extern u8 miccount;
#define MIC_VOL_SWITCH_SCENE    1

#if (CONFIG_IS_PRODUCT_S30)
    #define UAC_SPK_GAIN   (100)
    #define UAC_MIC_GAIN   (100)
#else
    #define UAC_SPK_GAIN   (80)
    #define UAC_MIC_GAIN   (80)
#endif

static void usb_audio_init(void)
{
    static u8 inited = 0;
    if (inited == 0) {
        inited = 1;
        audio_init(AUDIO_SR_POINT);

#ifdef CONFIG_NLPFIX_ENABLE
        int mic_write(u8 * buf, int len);
        int nlpfix_init(int(*outputfun)(u8 * buf, int len));
        nlpfix_init(mic_write);
#endif
    }
}



#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE)
/* u8 mic_cbuf[AUDIO_MIC_CBUF_SIZE]; */
u8 mic_buf[AUDIO_MIC_DEV_BUF_SIZE];

struct usb_mic_t {
    struct audio_dev dev;
    cbuffer_t cbuf;
};
struct usb_mic_t usb_mic_fh;

#ifdef CONFIG_NLPFIX_ENABLE
int mic_write(u8 *buf, int len)
{
    /* putchar('o'); */
    if(miccount <=2)
    {
      memset(buf,0,len);
    }
    return cbuf_write(&usb_mic_fh.cbuf, buf, len);
}
#endif

void adc_handler(u8 *data, u32 len)
{
#ifdef CONFIG_NLPFIX_ENABLE
    void nlpfix_nearbuf(u8 * buf, int len);
    nlpfix_nearbuf(data, len);
#else
    cbuf_write(&usb_mic_fh.cbuf, data, len);
#endif
}
int usb_audio_mic_near_buf_read(void *buf, u32 len)
{
    return 0;
}
void usb_audio_mic_near_buf_clear(void)
{
}
int usb_audio_mic_aec_buf_write(void *buf, u32 len)
{
    return 0;
}
static int usb_audio_mic_tx_handler(int event, void *data, int len)
{
    int rlen = 0;

    rlen = cbuf_read(&usb_mic_fh.cbuf, data, len);

    return rlen;

}

static void audio_mic_open(u8 channel, u32 samplerate)
{
    usb_audio_init();

    ////printf("111--audio mic open ch = %d, samplerate = %d\n", channel, samplerate);

    u8 *mic_cbuf = (u8 *)(0x100); //复用maskrom buf
    cbuf_init(&usb_mic_fh.cbuf, mic_cbuf, AUDIO_MIC_CBUF_SIZE);
    set_uac_mic_tx_handler(NULL, usb_audio_mic_tx_handler);
    cbuf_clear(&usb_mic_fh.cbuf);
    switch (samplerate) {
    case 48000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_48000;
        break;
    case 44100:
        usb_mic_fh.dev.sample_rate = ADC_SMP_44100;
        break;
    case 32000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_32000;
        break;
    case 24000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_24000;
        break;
    case 22050:
        usb_mic_fh.dev.sample_rate = ADC_SMP_22050;
        break;
    case 16000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_16000;
        break;
    case 12000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_12000;
        break;
    case 11025:
        usb_mic_fh.dev.sample_rate = ADC_SMP_11025;
        break;
    case 8000:
        usb_mic_fh.dev.sample_rate = ADC_SMP_8000;
        break;
    default:
        usb_mic_fh.dev.sample_rate = ADC_SMP_8000;
        break;
    }
//    usb_mic_fh.dev.volume = 0xFF;
    usb_mic_fh.dev.volume = (u8)(UAC_MIC_GAIN * 0xFF / 100);
    usb_mic_fh.dev.adc_type = ADC_TYPE_SINGLE;
    usb_mic_fh.dev.buf = mic_buf;
    usb_mic_fh.dev.buf_size = AUDIO_MIC_DEV_BUF_SIZE / 2;
    adc_open(&usb_mic_fh.dev);
}
static void audio_mic_close()
{
    adc_close();
}

static void audio_mic_set_gain(int gain)
{
    gain  = UAC_MIC_GAIN;
    adc_set_volume(gain * 0xFF / 100);
}
void usb_audio_mic_restart(void)
{
    cbuf_clear(&usb_mic_fh.cbuf);
}

#endif

#if (TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
u8 spk_buf[AUDIO_SPK_DEV_BUF_SIZE];

struct usb_spk_t {
    struct audio_dev dev;
    cbuffer_t cbuf;
#if (UAC_SRC_ENABLE == 1)
    volatile u8 sync_start;
    int sync_buf_size;
    cbuffer_t sample_sync_cbuf;
    dac_usb_syn_ops *dacUSBsyn_ops;
#endif
};

struct usb_spk_t usb_spk_fh;

#if (UAC_SRC_ENABLE == 1)
#define SAMPLE_SYNC_SIZE   (2048)
static u8 sample_sync_buf[SAMPLE_SYNC_SIZE];// sec(.video_ram1);
static u8 dacsyn_ptr[2080];// sec(.video_ram0);
static void dac_write_obuf(u8 *outbuf, u32 len)
{
    u32 wlen;
    if (usb_spk_fh.sync_start) {
        wlen = cbuf_write(&usb_spk_fh.sample_sync_cbuf, outbuf, len);
        if (wlen != len) {
            putchar('X');
        }
    }
}
static u32 dac_get_obuf(void)
{
    u32 data_len = 0;
    if (usb_spk_fh.sync_start) {
        data_len = cbuf_get_data_size(&usb_spk_fh.sample_sync_cbuf);
    }
    return data_len;
}
static void speaker_rx_response(void *priv, void *buf, int len)
{
    if (usb_spk_fh.sync_start) {
        usb_spk_fh.dacUSBsyn_ops->run(dacsyn_ptr, buf, len);
    }
}
static int usb_audio_spk_src_open(void)
{
    speaker_funapi sf;
    sf.output = dac_write_obuf;
    sf.getlen = dac_get_obuf;

    usb_spk_fh.sync_buf_size = SAMPLE_SYNC_SIZE;
    cbuf_init(&usb_spk_fh.sample_sync_cbuf, sample_sync_buf, usb_spk_fh.sync_buf_size);

    usb_spk_fh.dacUSBsyn_ops = get_dac_usbsyn_ops();
    /* u32 bsize = usb_spk_fh.dacUSBsyn_ops->need_buf(); */
    /* printf("\n[ debug ]--func=%s line=%d %d\n", __func__,__LINE__,bsize); */
    DISABLE_INT();
    usb_spk_fh.dacUSBsyn_ops->open(dacsyn_ptr, (u32)usb_spk_fh.sync_buf_size, &sf);
    ENABLE_INT();
    set_uac_speaker_rx_handler(NULL, speaker_rx_response);
    usb_spk_fh.sync_start = 1;
    return 0;
}
static void usb_audio_spk_src_close(void)
{
    usb_spk_fh.sync_start = 0;
    set_uac_speaker_rx_handler(NULL, NULL);
}
#endif

int usb_audio_dac_aec_farbuf_read(u8 *buf, u32 len)
{
    return 0;
}
void usb_audio_dac_aec_farbuf_clear(void)
{
}
/////by frank 20221209
static u8 dac_data_check(u8 *data, u32 len)
{
  u16 i;
  for(i=0;i<64;i++){
    if(*data !=0){
        return 1;
    }
    data+=4;
  }
  return 0;////no audio data

}

static bool is_valid_human_voice_pcm(u8 *data, u32 len)
{
#if 1
    return true;
#else
    const int valid_human_voice_max_amp = 10000; // magic num from chatgpt
    const u16 pcm_frame_len = 16;
    u16 pcm_block_num = len / pcm_frame_len;
    // printf("%d %d %d\n", (int)pcm_frame_len, (int)len, (int)pcm_block_num);
    for (int i = 0; i < pcm_block_num; i++) {
        u8 *p = data + i * pcm_frame_len;
        s16 val = p[1] << 8 | p[0];
        if (abs(val) > valid_human_voice_max_amp) {
            printf("wrong pcm:%d\n", (int)val);
            return false;
        }
    }

    return true;
#endif
}

extern void hw_spk_close(void);
extern void hw_spk_enable(void);
extern u8 dac_closed; ////by frank
/*
static void muteon(void *p){
  hw_spk_enable();
}
*/
void dac_handler(u8 *data, u32 len)
{
    int rlen = 0;
    int wlen = 0;
#if (UAC_SRC_ENABLE == 1)
    cbuffer_t *cbuffer = &usb_spk_fh.sample_sync_cbuf;
    if (usb_spk_fh.sync_start) {
        memset(data, 0, len);
        rlen = cbuf_get_data_size(cbuffer);
        rlen = (rlen > len) ? len : rlen;
        if (rlen) {
            int l = cbuf_read(cbuffer, data, rlen);
            if (l != rlen) {
                puts("sync cbuf read error\n");
            }
        }
    } else {
        rlen = uac_speaker_read(NULL, (void *)data, len);
    }
#else
    rlen = uac_speaker_read(NULL, (void *)data, len);

    if(rlen == 0 || !is_valid_human_voice_pcm(data, rlen))
        memset(data, 0, len);
    ////end 20230113
#ifdef CONFIG_NLPFIX_ENABLE
    if(dac_data_check((void *)data,len) == 0){////by frank
        dac_closed =1;
    }else
    {
     dac_closed = 0;
    }
#endif

#endif
    if (g_usb_audio_spk_play_cb && rlen > 0) {
        g_usb_audio_spk_play_cb(++g_audio_frame_play_cnt);
    }
}
static void audio_spk_open(u8 channel, u32 samplerate)
{
    usb_audio_init();
    ////printf("1111-audio spk open ch = %d, samplerate = %d\n", channel, samplerate);
    cbuf_clear(&usb_spk_fh.cbuf);

    switch (samplerate) {
    case 96000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_96000;
        break;
    case 88200:
        usb_spk_fh.dev.sample_rate = DAC_SMP_88200;
        break;
    case 64000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_64000;
        break;
    case 48000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_48000;
        break;
    case 44100:
        usb_spk_fh.dev.sample_rate = DAC_SMP_44100;
        break;
    case 32000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_32000;
        break;
    case 24000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_24000;
        break;
    case 22050:
        usb_spk_fh.dev.sample_rate = DAC_SMP_22050;
        break;
    case 16000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_16000;
        break;
    case 12000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_12000;
        break;
    case 11025:
        usb_spk_fh.dev.sample_rate = DAC_SMP_11025;
        break;
    case 8000:
        usb_spk_fh.dev.sample_rate = DAC_SMP_8000;
        break;
    default:
        usb_spk_fh.dev.sample_rate = DAC_SMP_8000;
        break;
    }
    usb_spk_fh.dev.volume = 0xFF;
    usb_spk_fh.dev.buf = spk_buf;
    usb_spk_fh.dev.buf_size = AUDIO_SPK_DEV_BUF_SIZE / 2;
    dac_open(&usb_spk_fh.dev);
#if (UAC_SRC_ENABLE == 1)
    usb_audio_spk_src_open();
#endif
}

// 清除播放计数，等下次播放开始重新计数，以便于在PA开之前正确的丢弃一些帧避免爆破音
void usb_audio_reset_audio_frame_cnt(void)
{
    g_audio_frame_play_cnt = 0;
}

void usb_audio_clear_audio_data(void)
{
    memset(spk_buf, 0, sizeof(spk_buf));
    memset(mic_buf, 0, sizeof(mic_buf));
}

static void audio_spk_close()
{
    if (g_usb_audio_spk_close_cb) {
        g_usb_audio_spk_close_cb();
    }

    dac_close();
#if (UAC_SRC_ENABLE == 1)
    usb_audio_spk_src_close();
#endif
}
static void audio_spk_set_gain(int gain)
{
    gain  = UAC_SPK_GAIN;
    dac_set_volume(gain * 0xFF / 100);
}
#endif


void usb_audio_process(struct sys_msg *msg)
{
    switch (msg->event) {
#if (TCFG_USB_SLAVE_AUDIO_MIC_ENABLE)
    case USB_AUDIO_MIC_OPEN:
        ////   puts("\nUSB_AUDIO_MIC_OPEN\n");
        audio_mic_open((u8)(msg->value >> 24), (u32)(msg->value & 0xFFFFFF));
        break;
    case USB_AUDIO_MIC_CLOSE:
        ////puts("\nUSB_AUDIO_MIC_CLOSE\n");
        audio_mic_close();
        break;
    case USB_AUDIO_SET_MIC_VOL:
       //// puts("\nUSB_AUDIO_SET_MIC_VOL\n");
        audio_mic_set_gain(msg->value);
////by frank 20221216 add by 5211 cam mode
#if MIC_VOL_SWITCH_SCENE
        if (msg->value == MIC_VOL_SWITCH_SCENE) {
            ////puts("\n user_flush_isp_cfg(1)--------\n");
            user_flush_isp_cfg(1);
        } else {
           //// puts("\n user_flush_isp_cfg(0)--------\n");
           user_flush_isp_cfg(0);
        }
#endif
        break;
#endif

#if (TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE)
    case USB_AUDIO_PLAY_OPEN:
       //// puts("\nUSB_AUDIO_PLAY_OPEN\n");
        audio_spk_open((u8)(msg->value >> 24), (u32)(msg->value & 0xFFFFFF));
       //// sys_timeout_add(NULL, muteon, 500);
        delay_ms(100);
       hw_spk_enable();
        break;
    case USB_AUDIO_PLAY_CLOSE:
      ////  puts("\nUSB_AUDIO_PLAY_CLOSE\n");
        audio_spk_close();
        extern void hw_spk_close(void);
        hw_spk_close();////
        break;
    case USB_AUDIO_SET_PLAY_VOL:
       //// puts("\nUSB_AUDIO_SET_PLAY_VOL\n");
        audio_spk_set_gain(msg->value);
        break;
#endif
    default:
        break;
    }
}






















#endif

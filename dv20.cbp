<?xml version="1.0" encoding="UTF-8" standalone="yes" ?>
<CodeBlocks_project_file>
	<FileVersion major="1" minor="6" />
	<Project>
		<Option title="dv20" />
		<Option compiler="q32s_lto_compiler" />
		<Option virtualFolders="drivers/camera/dv20_h63p_mipi_1lane_1280.720/;cpu/dv20/" />
		<Build>
			<Target title="Release">
				<Option output="cpu/dv20/tools/sdk.elf" prefix_auto="0" extension_auto="0" />
				<Option object_output="obj/Release/" />
				<Option type="1" />
				<Option compiler="q32s_lto_compiler" />
				<Compiler>
					<Add option="-flto" />
					<Add option="-g" />
					<Add option="-integrated-as" />
					<Add option="-fallow-pointer-null" />
					<Add option="-fno-common" />
					<Add option="-Oz" />
					<Add option="-mllvm -q32s-large-program" />
					<Add option="-Wno-format" />
					<Add option="-W" />
					<Add option="-Wno-unused-value" />
					<Add option="-Wno-unused-function" />
					<Add option="-Wno-unused-comparison" />
					<Add option="-Wno-unused-parameter" />
					<Add option="-Wno-missing-field-initializers" />
					<Add option="-DNOFLOAT" />
					<Add option="-DCONFIG_USE_USER_UVC_DRIVER_EN" />
					<Add option="-DCONFIG_USE_JPEG_DRIVER_EN" />
					<Add option="-DISP0_EN" />
					<Add option="-D__PRINTF_DEBUG" />
					<Add option="-D__CPU_dv20" />
					<Add option="-D__ARCH_q32s" />
					<Add option="-DUSB_HW_20" />
					<Add directory="include" />
					<Add directory="include/generic" />
					<Add directory="include/include_lib" />
					<Add directory="include/fs" />
					<Add directory="include/drivers" />
					<Add directory="include/drivers/flash" />
					<Add directory="include/drivers/key" />
					<Add directory="include/drivers/eeprom/E2BApi" />
					<Add directory="include/include_lib/ejpeg_header_new" />
					<Add directory="cpu/dv20" />
					<Add directory="include/cpu/dv20" />
					<Add directory="include/cpu/dv20/asm" />
					<Add directory="include/cpu/dv20/husb" />
					<Add directory="include/drivers/usb" />
					<Add directory="include/drivers/usb/device" />
				</Compiler>
				<Linker>
					<Add option="-flto" />
					<Add option="--plugin-opt=-dont-used-symbol-list=malloc,free,sprintf,printf,puts,putchar,perror,vprintf,printi,fopen,fread" />
					<Add option="-Map=map.txt" />
					<Add option="-Tcpu/dv20/ram.ld" />
					<Add option="--plugin-opt=-inline-threshold=5" />
					<Add option="--plugin-opt=-enable-ipra=true" />
					<Add option="--plugin-opt=-global-merge-on-const" />
					<Add option="--plugin-opt=-inline-normal-into-special-section=true" />
					<Add option="--plugin-opt=-q32s-large-program" />
					<Add option="--start-group" />
					<Add option="liba/cpu.a" />
					<Add option="liba/lib_usb_syn.a" />
					<Add option="liba/jpeg_encoder.a" />
					<Add option="liba/cbuf.a" />
					<Add option="liba/lbuf.a" />
					<Add option="--whole-archive" />
					<Add option="--no-whole-archive" />
					<Add option="liba/mm.a" />
					<Add option="liba/database.a" />
					<Add option="liba/isp.a" />
					<Add option="liba/nlpfix/libFFT_pi32v2_OnChip.a" />
					<Add option="liba/nlpfix/libEchoSuppress_fix_pi32v2_OnChip.a" />
					<Add option="liba/nlpfix/libSplittingFilter_pi32v2_small_OnChip.a" />
					<Add option="--end-group" />
					<Add library="C:/JL/pi32/q32s-lib/libm.a" />
				</Linker>
				<ExtraCommands>
					<Add before="$compiler $options $includes -D__LD__ -E -P cpu\dv20\ram_ld.c -o cpu\dv20\ram.ld" />
					<Add after="cpu\dv20\tools\post_build.bat sdk" />
					<Add after="cpu\dv20\tools\下载代码.bat" />
					<Mode after="always" />
				</ExtraCommands>
			</Target>
		</Build>
		<Unit filename="app/car_cam_case/video.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/car_cam_case/video_bulk.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/device_change.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/isp_scenes.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/main.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/osd.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/pc_cam_case/video.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/pc_cam_case/video2.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/pc_cam_case/video_bulk.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/test/audio_test.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/test/nlpfix_test.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="app/test/sleep_test.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/board/board_AC5316A_DEV_20211231.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/board/board_AC5316B_DEV_20211231.c">
			<Option compilerVar="CC" />
			<Option virtualFolder="cpu/dv20/" />
		</Unit>
		<Unit filename="cpu/dv20/board/board_AC532X_DEV_20211231.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/exception.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/gpio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/image_capture.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/ldo.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/setup.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/startup.S" />
		<Unit filename="cpu/dv20/timer_pwm.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/uart.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="cpu/dv20/user.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi.h" />
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_ae.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_ae.h" />
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_awb.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_awb.h" />
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_iq.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/camera/dv20_h63p_mipi_1lane_1280.720/h63_mipi_iq.h" />
		<Unit filename="drivers/camera/h63_mipi/h63_mipi.h" />
		<Unit filename="drivers/camera/h63_mipi/h63_mipi_ae.h" />
		<Unit filename="drivers/camera/h63_mipi/h63_mipi_awb.h" />
		<Unit filename="drivers/camera/h63_mipi/h63_mipi_iq.h" />
		<Unit filename="drivers/camera/h63p/h63p_mipi.h" />
		<Unit filename="drivers/camera/h63p/h63p_mipi_ae.h" />
		<Unit filename="drivers/camera/h63p/h63p_mipi_awb.h" />
		<Unit filename="drivers/camera/h63p/h63p_mipi_iq.h" />
		<Unit filename="drivers/common/printf-stdarg.c">
			<Option compilerVar="CC" />
			<Option virtualFolder="drivers/camera/dv20_h63p_mipi_1lane_1280.720/" />
		</Unit>
		<Unit filename="drivers/common/rand.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/delay.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/device_api.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/key/adkey.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/key/iokey.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/key/key_driver.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/msd.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/task_pc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/uac1.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/uac_stream.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/uvc.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/uvc_user_extension.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/device/uvc_video_interface.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb/usb_config.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="drivers/usb_audio.c">
			<Option compilerVar="CC" />
		</Unit>
		<Unit filename="include/app_config.h" />
		<Unit filename="include/app_msg.h" />
		<Unit filename="include/cpu/dv20/asm/core.h" />
		<Unit filename="include/cpu/dv20/asm/csfr.h" />
		<Unit filename="include/cpu/dv20/asm/dv20.h" />
		<Unit filename="include/cpu/dv20/asm/hwi.h" />
		<Unit filename="include/cpu/dv20/asm/icache.h" />
		<Unit filename="include/cpu/dv20/asm/io_imap.h" />
		<Unit filename="include/cpu/dv20/asm/io_omap.h" />
		<Unit filename="include/cpu/dv20/cpu.h" />
		<Unit filename="include/cpu/dv20/efuse.h" />
		<Unit filename="include/cpu/dv20/gpio.h" />
		<Unit filename="include/cpu/dv20/gpio_hw.h" />
		<Unit filename="include/cpu/dv20/husb/husb_hal.h" />
		<Unit filename="include/cpu/dv20/hw_timer.h" />
		<Unit filename="include/cpu/dv20/iic.h" />
		<Unit filename="include/cpu/dv20/irq.h" />
		<Unit filename="include/cpu/dv20/ldo.h" />
		<Unit filename="include/cpu/dv20/sfc.h" />
		<Unit filename="include/cpu/dv20/uart.h" />
		<Unit filename="include/cpu/dv20/wdt.h" />
		<Unit filename="include/drivers/camera.h" />
		<Unit filename="include/drivers/delay.h" />
		<Unit filename="include/drivers/device.h" />
		<Unit filename="include/drivers/eva.h" />
		<Unit filename="include/drivers/flash/spiflash.h" />
		<Unit filename="include/drivers/isp.h" />
		<Unit filename="include/drivers/isp_alg.h" />
		<Unit filename="include/drivers/isp_customize.h" />
		<Unit filename="include/drivers/isp_dev.h" />
		<Unit filename="include/drivers/isp_scenes.h" />
		<Unit filename="include/drivers/jtime.h" />
		<Unit filename="include/drivers/key/adkey.h" />
		<Unit filename="include/drivers/key/iokey.h" />
		<Unit filename="include/drivers/key/key_driver.h" />
		<Unit filename="include/drivers/osd.h" />
		<Unit filename="include/drivers/usb/ch9.h" />
		<Unit filename="include/drivers/usb/device/descriptor.h" />
		<Unit filename="include/drivers/usb/device/msd.h" />
		<Unit filename="include/drivers/usb/device/slave_uvc.h" />
		<Unit filename="include/drivers/usb/device/uac_audio.h" />
		<Unit filename="include/drivers/usb/device/uac_stream.h" />
		<Unit filename="include/drivers/usb/device/usb_stack.h" />
		<Unit filename="include/drivers/usb/scsi.h" />
		<Unit filename="include/drivers/usb/usb.h" />
		<Unit filename="include/drivers/usb/usb_common_def.h" />
		<Unit filename="include/drivers/usb/usb_config.h" />
		<Unit filename="include/drivers/usb/usb_std_class_def.h" />
		<Unit filename="include/drivers/usb/uvc.h" />
		<Unit filename="include/drivers/usb_audio.h" />
		<Unit filename="include/drivers/video.h" />
		<Unit filename="include/generic/atomic.h" />
		<Unit filename="include/generic/crc.h" />
		<Unit filename="include/generic/errno-base.h" />
		<Unit filename="include/generic/fcvt.h" />
		<Unit filename="include/generic/includes.h" />
		<Unit filename="include/generic/ioctl.h" />
		<Unit filename="include/generic/jiffies.h" />
		<Unit filename="include/generic/list.h" />
		<Unit filename="include/generic/printf.h" />
		<Unit filename="include/generic/typedef.h" />
		<Unit filename="include/include_lib/EchoSuppressLib.h" />
		<Unit filename="include/include_lib/SplittingFilter.h" />
		<Unit filename="include/include_lib/adc.h" />
		<Unit filename="include/include_lib/audio.h" />
		<Unit filename="include/include_lib/cbuf.h" />
		<Unit filename="include/include_lib/clock.h" />
		<Unit filename="include/include_lib/dma_copy.h" />
		<Unit filename="include/include_lib/ejpeg_header_new/jpeg_abr.h" />
		<Unit filename="include/include_lib/ejpeg_header_new/jpeg_encoder.h" />
		<Unit filename="include/include_lib/fs.h" />
		<Unit filename="include/include_lib/imc.h" />
		<Unit filename="include/include_lib/lbuf.h" />
		<Unit filename="include/include_lib/msg.h" />
		<Unit filename="include/include_lib/my_malloc.h" />
		<Unit filename="include/include_lib/resfile.h" />
		<Unit filename="include/include_lib/sd_host_api.h" />
		<Unit filename="include/include_lib/sleep.h" />
		<Unit filename="include/include_lib/spi.h" />
		<Unit filename="include/include_lib/usb_syn_api.h" />
		<Unit filename="include/include_lib/yuv_recorder.h" />
		<Unit filename="include/park_det.h" />
		<Extensions />
	</Project>
</CodeBlocks_project_file>

#ifndef  __APP_CONFIG_H__
#define  __APP_CONFIG_H__

#ifndef __LD__
#include "iic.h"
#include "app_msg.h"
#endif

//*********************************************************************************//
//                      product configure, can only select one
// defined by <PERSON>file according to selected product
// #define CONFIG_PRODUCT_SU7PRO_DEFAULT
// #defined CONFIG_PRODUCT_SU7PRO_2ND
// #define CONFIG_PRODUCT_YU7_DEFAULT

// product of S30
// #define CONFIG_PRODUCT_S30_OUTPUT_864x480
// #define CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0
// #define CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA
// #define CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG
//*********************************************************************************//
//                      app case configuare
//                     (由config.mk自动生成)
// #define CONFIG_ENABLE_OSD
#if defined(CONFIG_PRODUCT_YU7_BULK)        \
    || defined(CONFIG_PRODUCT_SU7PRO_BULK)  \
    || defined(CONFIG_PRODUCT_SU7Z_BULK)    \
    || defined(CONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK)    \
    || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA) \
    || defined(CONFIG_PRODUCT_ZB01)
#define CONFIG_ENABLE_BULK_MODE
#endif
//*********************************************************************************//
#ifdef CONFIG_ENABLE_BULK_MODE
#define CAR_CAM_CASE
#else
#define PC_CAM_CASE
#endif
//*********************************************************************************//
#ifndef CONFIG_PRODUCT_SU7Z_BULK
#define FILL_LIGHT_ENABLE //补光灯 0 function off, 1 function on
#endif

//1 为正向，0为反向
#ifdef CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG
#define FLIP         0
#else
#define FLIP         1
#endif

#if defined(CONFIG_PRODUCT_S30_OUTPUT_864x480) \
   || defined(CONFIG_PRODUCT_S30_7256) \
   || defined(CONFIG_PRODUCT_S30_USBV11_DEFAULT) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG) \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK)
   #define CONFIG_IS_PRODUCT_S30 1
#else
   #define CONFIG_IS_PRODUCT_S30 0
#endif

#if defined(CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0)           \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)  \
   || defined(CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)            \
   || defined(CONFIG_PRODUCT_YU7_VM0)                           \
   || defined(CONFIG_PRODUCT_S30_USBV11_DEFAULT)                \
   || defined(CONFIG_PRODUCT_SU7PRO_USBV11)                     \
   || defined(CONFIG_PRODUCT_SU7PRO_BULK)                     \
   || defined(CONFIG_PRODUCT_YU7L_DEFAULT)                 \
   || defined(CONFIG_PRODUCT_YU7L_XISHANG)
   #define CONFIG_COMPATIBLE_YIKANG_VM0 1
#else
   #define CONFIG_COMPATIBLE_YIKANG_VM0 0
#endif

#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT)                  \
    || defined(CONFIG_PRODUCT_SU7PRO_2ND)                   \
    || defined(CONFIG_PRODUCT_SU7PRO_USBV11)                       \
    || defined(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)             \
    || defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)        \
    || defined(CONFIG_PRODUCT_SU7PRO_BULK)                  \
    || defined(CONFIG_PRODUCT_SU7Z_BULK)                    \
    || defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)
    #define VERSION_PREFIX "SU7"
    #define VERSION_STR "V1.0.16"
#elif (CONFIG_IS_PRODUCT_S30)
    #define VERSION_PREFIX "S30"
    #define VERSION_STR "V1.0.14"
#elif defined(CONFIG_PRODUCT_YU7_DEFAULT) || defined(CONFIG_PRODUCT_YU7_VM0) || defined(CONFIG_PRODUCT_YU7_BULK)
    #define VERSION_PREFIX "YU7"
    #define VERSION_STR "V1.0.8"
#elif defined(CONFIG_PRODUCT_YU7L_DEFAULT) || defined(CONFIG_PRODUCT_YU7L_XISHANG)
    #define VERSION_PREFIX "YU7L"
    #define VERSION_STR "V1.0.3"
#elif defined(CONFIG_PRODUCT_ZB01) || defined(CONFIG_PRODUCT_ZB01_QIXI)
    #define VERSION_PREFIX "ZB01"
    #define VERSION_STR "V1.0.3"
#else
    #error "should define at least one product"
#endif

#if defined(CONFIG_PRODUCT_SU7PRO_DEFAULT) || defined(CONFIG_PRODUCT_SU7PRO_2ND)
    #define VERSION_SUFFIX "H63P_ISOC"
#elif defined(CONFIG_PRODUCT_SU7PRO_USBV11)
    #define VERSION_SUFFIX "H63P_USBV11"
#elif defined(CONFIG_PRODUCT_SU7PRO_AUDIO_16K)
    #define VERSION_SUFFIX "H63P_16K"
#elif defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)
    #define VERSION_SUFFIX "H63P_7256"
#elif defined(CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG)
    #define VERSION_SUFFIX "7256_ZC"
#elif defined(CONFIG_PRODUCT_S30_7256)
    #define VERSION_SUFFIX "7256"
#elif defined(CONFIG_PRODUCT_SU7Z_BULK) || defined(CONFIG_PRODUCT_SU7PRO_BULK)
    #define VERSION_SUFFIX "H63P_BULK"
#elif defined (CONFIG_PRODUCT_YU7_DEFAULT)
    #define VERSION_SUFFIX "1346_ISOC"
#elif defined (CONFIG_PRODUCT_YU7_BULK)
    #define VERSION_SUFFIX "1346_BULK"
#elif defined (CONFIG_PRODUCT_YU7_VM0)
    #define VERSION_SUFFIX "1346_VM0"
#elif defined (CONFIG_PRODUCT_S30_OUTPUT_864x480)
    #define VERSION_SUFFIX "ISOC_864"
#elif defined (CONFIG_PRODUCT_S30_USBV11_DEFAULT)
    #define VERSION_SUFFIX "ISOC_USBV11"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0)
    #define VERSION_SUFFIX "ISOC_VM0"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA)
    #define VERSION_SUFFIX "BULK_XFGJ"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG)
    #define VERSION_SUFFIX "ISOC_XSUN"
#elif defined (CONFIG_PRODUCT_S30_COMPATIBLE_NAITE_BULK)
    #define VERSION_SUFFIX "BULK_NT"
#elif defined (CONFIG_PRODUCT_YU7L_DEFAULT)
    #define VERSION_SUFFIX "ISOC"
#elif defined (CONFIG_PRODUCT_YU7L_XISHANG)
    #define VERSION_SUFFIX "ISOC_XS"
#elif defined (CONFIG_PRODUCT_ZB01)
    #define VERSION_SUFFIX "BULK_KL"
#elif defined (CONFIG_PRODUCT_ZB01_QIXI)
    #define VERSION_SUFFIX "ISOC_QIXI"
#else
    #error "should define at least one product"
#endif
#define SOFTWARE_VERSION_PREFIX VERSION_PREFIX"_"VERSION_STR"_"VERSION_SUFFIX
//*********************************************************************************//
//                           board configuare
//*********************************************************************************//
#define CONFIG_BOARD_AC5316A_DEV_20211231

//*********************************************************************************//
//                            function config
//*********************************************************************************//
#ifdef CONFIG_BOARD_AC5316A_DEV_20211231
    #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS | AUDIO_CLASS)
    // #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS | MIC_CLASS)
    // #define USB_DEVICE_CLASS_CONFIG  (VIDEO_CLASS)
    /////////// USB MODE
    #ifdef CONFIG_PRODUCT_S30_COMPATIBLE_YIKANG_VM0
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
    #elif defined CONFIG_PRODUCT_S30_OUTPUT_864x480
        #define HUSB_MODE
    #elif defined CONFIG_PRODUCT_S30_USBV11_DEFAULT
        //V1.1 not support high speed
    #elif defined CONFIG_PRODUCT_S30_COMPATIBLE_XIAOFENG_GUANGJIA
        //compitable jianfeng 7258 xiaofengguanjia  3.5 and 4.5
        #define HUSB_MODE
        #define CONFIG_NLPFIX_ENABLE
    #elif defined CONFIG_PRODUCT_S30_COMPATIBLE_XISHANG
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define UAC_ISO_INTERVAL 1
    #elif defined CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256_ZHICHENG \
        || defined CONFIG_PRODUCT_SU7PRO_USBV11 \
        || defined CONFIG_PRODUCT_S30_7256
        #define UAC_ISO_INTERVAL 1
        #define MIC_SamplingFrequency 1
        // #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
    #elif defined CONFIG_PRODUCT_SU7PRO_AUDIO_16K
        #define HUSB_MODE
        #define AUDIO_MIC_16K
        #define AUDIO_SPK_16K
    #elif defined CONFIG_PRODUCT_YU7L_DEFAULT
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define UAC_ISO_INTERVAL 4
        #define MIC_SamplingFrequency 1
    #elif defined CONFIG_PRODUCT_ZB01
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define UAC_ISO_INTERVAL 4
        #define MIC_SamplingFrequency 1
    #elif defined CONFIG_PRODUCT_ZB01_QIXI
        #define HUSB_MODE
        // #define CONFIG_NLPFIX_ENABLE
        #define UAC_ISO_INTERVAL 4
        #define MIC_SamplingFrequency 1
    #else // YU7 and SU7
        #define HUSB_MODE
        #define CONFIG_NLPFIX_ENABLE
    #endif

    #if (CONFIG_COMPATIBLE_YIKANG_VM0) && defined(CONFIG_NLPFIX_ENABLE)
        #undef CONFIG_NLPFIX_ENABLE
    #endif
    // #define CONFIG_YUYV_ENABLE                 //yuyv uvc enable
    // #define CONFIG_ADKEY_ENABLE                //ADKEY 使能
    // #define CONFIG_IOKEY_ENABLE                //IOKEY 使能
    // #define CONFIG_UVC_FOR_1080P_ENABLE        //1080p插值功能
#endif

#ifdef USE_FLASH_CFG_EN
#ifndef CONFIG_BOARD_AC532X_DEV_20211231
#error "please define CONFIG_BOARD_AC532X_DEV_20211231  for vga car cam case !!!"
#endif
#endif

#if defined CONFIG_NLPFIX_ENABLE && defined CONFIG_UVC_FOR_1080P_ENABLE
#error "can not Supports 1080p and nlpfix at the same time!!"
#endif



#if defined CONFIG_NLPFIX_ENABLE || defined CONFIG_UVC_FOR_1080P_ENABLE
#define CONFIG_UVC_ISO_REDIRECT_ENABLE
#endif

#define SD0_ROOT_PATH "storage/sd0/C/"

#include "usb_std_class_def.h"
#define TCFG_PC_ENABLE          1
#include "usb_common_def.h"

#endif


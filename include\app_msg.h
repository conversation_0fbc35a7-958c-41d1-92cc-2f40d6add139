#ifndef  __APP_MSG_H__
#define  __APP_MSG_H__

#include "msg.h"
enum {
    MSG_0 = 0,
    ///APP
    MSG_500MS,
    MSG_1S,
    MSG_TIMER_RUN, //定时任务
    MSG_ISP0_TASK, //isp0 task
    MSG_ISP0_EXCEPTION, //isp0 exception
    MSG_USB_TASK,
    MSG_USB_AUTO_TRIM,
    MSG_UVC_OPEN,
    MSG_UVC_CLOSE,
    MSG_UVC_BULK_SEND,
    MSG_UVC_AE_CONTROL,

    MSG_KEY_CLICK,	//按键
    MSG_KEY_LONG,	//按键长按
    MSG_KEY_HOLD,	//按键长按保持
    MSG_KEY_UP,		//按键长按后抬起

    MSG_TEST,
    MSG_ENTER_IDLE,

};

#endif

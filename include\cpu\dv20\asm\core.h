#ifndef __Q32SMALL_CORE__
#define __Q32SMALL_CORE__

//*********************************************************************************//
// Module name : core.h                                                            //
// Description : core head file define                                             //
// By Designer : zequan_liu                                                        //
// Dat changed :                                                                   //
//*********************************************************************************//

//---------------------------------------------//
// core define
//---------------------------------------------//

#define q32small_used
#define q32small_num          1
#define ISR_ENTRY             0x01fe00
#define SSP_STACK_OFFSET      0x001000
#define SSP_STACK            (0x01fe00-(q32small_num-1)*SSP_STACK_OFFSET)
#define USP_STACK            (0x01f000-(q32small_num-1)*SSP_STACK_OFFSET)

//*********************************************************************************//
//                                                                                 //
//                               end of this module                                //
//                                                                                 //
//*********************************************************************************//
#endif


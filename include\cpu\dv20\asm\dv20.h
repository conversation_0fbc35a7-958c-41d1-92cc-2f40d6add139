

#ifndef __DV20__
#define __DV20__

//===============================================================================//
//
//      sfr define
//
//===============================================================================//

#define hs_base            0x2e0000
#define ls_base            0x2d0000
//#define eva_base           0x2c0000

#define __RW               volatile       // read write
#define __RO               volatile const // only read
#define __WO               volatile       // only write

#define __u8               unsigned int   // u8  to u32 special for struct
#define __u16              unsigned int   // u16 to u32 special for struct
#define __u32              unsigned int

#define __s8(x)            char(x); char(reserved_1_##x); char(reserved_2_##x); char(reserved_3_##x)
#define __s16(x)           short(x); short(reserved_1_##x)
#define __s32(x)           int(x)

#define map_adr(grp, adr)  ((64 * grp + adr) * 4)     // grp(0x0-0xff), adr(0x0-0x3f)

//===============================================================================//
//
//      high speed sfr address define
//
//===============================================================================//

//............. 0x0000 - 0x00ff............ for hemu
typedef struct {
    __RW __u32 WREN;
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __RW __u32 CON3;
    __RW __u32 MSG0;
    __RW __u32 MSG1;
    __RW __u32 MSG2;
    __RW __u32 MSG3;
    __RO __u32 ID;
} JL_HEMU_TypeDef;

#define JL_HEMU_BASE                 (hs_base + map_adr(0x00, 0x00))
#define JL_HEMU                      ((JL_HEMU_TypeDef    *)JL_HEMU_BASE)


//............. 0x0100 - 0x01ff............ for jpg
typedef struct {
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __RW __u32 YUVLINE;
    __RW __u32 YDCVAL;
    __RW __u32 UDCVAL;
    __RW __u32 VDCVAL;
    __RW __u32 YPTR;
    __RW __u32 UPTR;
    __RW __u32 VPTR;
    __RW __u32 BADDR;
    __RW __u32 BCNT;
    __RW __u32 MCUCNT;
    __RW __u32 PRECNT;
    __RW __u32 HQTBLADR;
    __RW __u32 HQTBLDAT;
    __RW __u32 PTR_NUM;
    __RW __u32 HAS_PTR;
    __RW __u32 MCU_BUF;
} JL_JPG_TypeDef;

#define JL_JPG_BASE                  (hs_base + map_adr(0x01, 0x00))
#define JL_JPG                       ((JL_JPG_TypeDef *)JL_JPG_BASE)


//............. 0x0200 - 0x02ff............

//............. 0x0300 - 0x03ff............ for hmbist
typedef struct {
    __RW __u32 CON;
    __RW __u32 SEL;
    __RW __u32 DP;
    __RW __u32 DAT_VLD0;
    __RW __u32 DAT_VLD1;
    __RW __u32 DAT_VLD2;
    __RW __u32 DAT_VLD3;
} JL_HMBIST_TypeDef;

#define JL_HMBIST_BASE               (hs_base + map_adr(0x03, 0x00))
#define JL_HMBIST                    ((JL_HMBIST_TypeDef *)JL_HMBIST_BASE)


//............. 0x0400 - 0x04ff............ for hs_jpg_mbist
typedef struct {
    __RW __u32 CON;
    __RW __u32 SEL;
    __RW __u32 DP;
    __RW __u32 DAT_VLD0;
    __RW __u32 DAT_VLD1;
    __RW __u32 DAT_VLD2;
    __RW __u32 DAT_VLD3;
} JL_JPG_MBIST_TypeDef;

#define JL_JPG_MBIST_BASE            (hs_base + map_adr(0x04, 0x00))
#define JL_JPG_MBIST                 ((JL_JPG_MBIST_TypeDef *)JL_JPG_MBIST_BASE)


//............. 0x0500 - 0x05ff............   for sfc
typedef struct {
    __RW __u32 CON;
    __RW __u32 BAUD;
    __RW __u32 REDU_BAUD;
    __RW __u32 CODE;
    __RW __u32 BASE_ADR;
    __RW __u32 QUCNT;
    __RW __u8  ENC_CON;
    __RW __u16 ENC_KEY;
    __WO __u32 UNENC_ADRH;
    __WO __u32 UNENC_ADRL;
} JL_SFC_TypeDef;

#define JL_SFC_BASE            (hs_base + map_adr(0x05, 0x00))
#define JL_SFC                 ((JL_SFC_TypeDef *)JL_SFC_BASE)


//............. 0x0600 - 0x06ff............   for dcp
typedef struct {
    __RW __u32 CON;
    __RW __u32 ADR;
} JL_DCP_TypeDef;

#define JL_DCP_BASE            (hs_base + map_adr(0x06, 0x00))
#define JL_DCP                 ((JL_DCP_TypeDef *)JL_DCP_BASE)

//............. 0x0700 - 0x07ff............	//for eva_com
typedef struct {
    __RW __u32 CON;
} JL_EVA_COM_TypeDef;

#define JL_EVA_COM_BASE            (hs_base + map_adr(0x07, 0x00))
#define JL_EVA_COM                 ((JL_EVA_COM_TypeDef *)JL_EVA_COM_BASE)

//............. 0x0800 - 0x08ff............
typedef struct {
    __RW __u32 CON;
    __RW __u32 SEL0;
    __RW __u32 SEL1;
    __RW __u32 DP;
    __RW __u32 DAT_VLD0;
    __RW __u32 DAT_VLD1;
    __RW __u32 DAT_VLD2;
    __RW __u32 DAT_VLD3;
    __RW __u32 ROM_CRC;
} JL_EVA_MBIST_TypeDef;

#define JL_EVA_MBIST_BASE            (hs_base + map_adr(0x08, 0x00))
#define JL_EVA_MBIST                 ((JL_EVA_MBIST_TypeDef *)JL_EVA_MBIST_BASE)

//............. 0x0900 - 0x09ff............
typedef struct {
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __RW __u32 CON3;
    __RW __u32 CON4;
    __RW __u32 CON5;
    __RW __u32 CON6;
    __RW __u32 CON7;
    __RW __u32 CON8;
    __RW __u32 CON9;
} JL_YUV_REORDER_TypeDef;
#define JL_YUV_REORDER_BASE            (hs_base + map_adr(0x09, 0x00))
#define JL_YUV_REORDER                 ((JL_YUV_REORDER_TypeDef *)JL_YUV_REORDER_BASE)
//............. 0x0a00 - 0x0aff............
//............. 0x0b00 - 0x0bff............
//............. 0x0c00 - 0x0cff............
//............. 0x0d00 - 0x0dff............
//............. 0x0e00 - 0x0eff............
//............. 0x0f00 - 0x0fff............
//............. 0x1000 - 0x10ff............
//............. 0x1100 - 0x11ff............
//............. 0x1200 - 0x12ff............
//............. 0x1300 - 0x13ff............
//............. 0x1400 - 0x14ff............
//............. 0x1500 - 0x15ff............
//............. 0x1600 - 0x16ff............
//............. 0x1700 - 0x17ff............
//............. 0x1800 - 0x18ff............
//............. 0x1900 - 0x19ff............
//............. 0x1a00 - 0x1aff............
//............. 0x1b00 - 0x1bff............
//............. 0x1c00 - 0x1cff............
//............. 0x1d00 - 0x1dff............
//............. 0x1e00 - 0x1eff............
//............. 0x1f00 - 0x1fff............
//............. 0x2000 - 0x20ff............
//............. 0x2100 - 0x21ff............
//............. 0x2200 - 0x22ff............
//............. 0x2300 - 0x23ff............
//............. 0x2400 - 0x24ff............
//............. 0x2500 - 0x25ff............
//............. 0x2600 - 0x26ff............
//............. 0x2700 - 0x27ff............
//............. 0x2800 - 0x28ff............
//............. 0x2900 - 0x29ff............
//............. 0x2a00 - 0x2aff............
//............. 0x2b00 - 0x2bff............
//............. 0x2c00 - 0x2cff............
//............. 0x2d00 - 0x2dff............
//............. 0x2e00 - 0x2eff............
//............. 0x2f00 - 0x2fff............


//===============================================================================//
//
//      low speed sfr address define
//
//===============================================================================//

//............. 0x0000 - 0x00ff............ for syscfg
typedef struct {
    __RW __u32 PWR_CON;
    __RW __u32 SYS_DIV;
    __RW __u32 CLK_CON0;
    __RW __u32 CLK_CON1;
    __RW __u32 CLK_CON2;
    __RW __u32 RST_SRC;

} JL_CLOCK_TypeDef;

#define JL_CLOCK_BASE                   (ls_base + map_adr(0x00, 0x00))
#define JL_CLOCK                        ((JL_CLOCK_TypeDef      *)JL_CLOCK_BASE)

//............. 0x0100 - 0x01ff............ for mode_det
typedef struct {
    __RW __u32 MODE_CON;
} JL_MODE_TypeDef;

#define JL_MODE_BASE                    (ls_base + map_adr(0x01, 0x00))
#define JL_MODE                         ((JL_MODE_TypeDef     *)JL_MODE_BASE)


//............. 0x0200 - 0x02ff............  for isp
typedef struct {
    __WO __u32 CHIP_ID;
} JL_ISP_TypeDef;

#define JL_ISP_BASE                  (ls_base + map_adr(0x02, 0x00))
#define JL_ISP                       ((JL_ISP_TypeDef     *)JL_ISP_BASE)


//............. 0x0300 - 0x03ff............ for emu
typedef struct {
    __RW __u32 WREN;
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __RW __u32 CON3;
    __RW __u32 MSG0;
    __RW __u32 MSG1;
    __RW __u32 MSG2;
    __RW __u32 MSG3;
    __RO __u32 ID;
} JL_LEMU_TypeDef;

#define JL_LEMU_BASE                    (ls_base + map_adr(0x03, 0x00))
#define JL_LEMU                         ((JL_LEMU_TypeDef    *)JL_LEMU_BASE)


//............. 0x0400 - 0x05ff............	for timer0 timer1
typedef struct {
    __RW __u32 CON;
    __RW __u32 CNT;
    __RW __u32 PRD;
    __RW __u32 PWM;
} JL_TIMER_TypeDef;

#define JL_TIMER0_BASE                  (ls_base + map_adr(0x04, 0x00))
#define JL_TIMER0                       ((JL_TIMER_TypeDef     *)JL_TIMER0_BASE)

#define JL_TIMER1_BASE                  (ls_base + map_adr(0x05, 0x00))
#define JL_TIMER1                       ((JL_TIMER_TypeDef     *)JL_TIMER1_BASE)


//............. 0x0600 - 0x06ff............ for efuse
typedef struct {
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __RW __u32 CON3;
} JL_EFUSE_TypeDef;

#define JL_EFUSE_BASE                   (ls_base + map_adr(0x06, 0x00))
#define JL_EFUSE                        ((JL_EFUSE_TypeDef     *)JL_EFUSE_BASE)


//............. 0x0700 - 0x07ff............ for wdt
typedef struct {
    __RW __u32 CON;
} JL_WDT_TypeDef;

#define JL_WDT_BASE                     (ls_base + map_adr(0x07, 0x00))
#define JL_WDT                          ((JL_WDT_TypeDef       *)JL_WDT_BASE)

//............. 0x0d00 - 0x0dff............ for uart0
typedef struct {
    __RW __u16 CON0;
    __RW __u16 CON1;
    __WO __u16 BAUD;
    __RW __u8  BUF;
    __RW __u32 OTCNT;
    __RW __u32 TXADR;
    __WO __u16 TXCNT;
    __RW __u32 RXSADR;
    __RW __u32 RXEADR;
    __RW __u32 RXCNT;
    __RO __u16 HRXCNT;
    __RW __u16 CON2;
} JL_UART_TypeDef;

#define JL_UART_BASE0                  (ls_base + map_adr(0x0d, 0x00))
#define JL_UART0                       ((JL_UART_TypeDef       *)JL_UART_BASE0)

//............. 0x0e00 - 0x0eff............ for uart1
#define JL_UART_BASE1                  (ls_base + map_adr(0x0e, 0x00))
#define JL_UART1                       ((JL_UART_TypeDef       *)JL_UART_BASE1)

//............. 0x0f00 - 0x10ff............	for iic0 iic1
typedef struct {
    __RW __u16 CON;
    __RW __u8  BUF;
    __WO __u16 BAUD;
    __RW __u32 DMA_ADR;
    __RW __u32 DMA_CNT;
    __RW __u32 DMA_NRATE;
} JL_IIC_TypeDef;

#define JL_IIC0_BASE                    (ls_base + map_adr(0x0f, 0x00))
#define JL_IIC0                         ((JL_IIC_TypeDef       *)JL_IIC0_BASE)

#define JL_IIC1_BASE                    (ls_base + map_adr(0x10, 0x00))
#define JL_IIC1                         ((JL_IIC_TypeDef       *)JL_IIC1_BASE)


//............. 0x1100 - 0x11ff............ for lvd_ctl
typedef struct {
    __RW __u32 CON;
} JL_LVD_TypeDef;

#define JL_LVD_BASE                     (ls_base + map_adr(0x11, 0x00))
#define JL_LVD                          ((JL_LVD_TypeDef       *)JL_LVD_BASE)


//............. 0x1200 - 0x12ff............ for gpcnt
typedef struct {
    __RW __u32 CON;
    __RO __u32 NUM;
} JL_GPCNT_TypeDef;

#define JL_GPCNT_BASE                   (ls_base + map_adr(0x12, 0x00))
#define JL_GPCNT                        ((JL_GPCNT_TypeDef     *)JL_GPCNT_BASE)


//............. 0x1300 - 0x13ff............ for peri_enc
typedef struct {
    __RW __u8  ENCCON ;
    __WO __u16 ENCKEY ;
    __WO __u16 ENCADR ;
} JL_PERI_ENC_TypeDef;

#define JL_PERI_ENC_BASE                   (ls_base + map_adr(0x13, 0x00))
#define JL_PERI_ENC                        ((JL_PERI_ENC_TypeDef     *)JL_PERI_ENC_BASE)


//............. 0x1400 - 0x14ff............ for lrc_trace
typedef struct {
    __WO __u32 CON;
    __RW __u32 NUM;
} JL_LRCT_TypeDef;

#define JL_LRCT_BASE                    (ls_base + map_adr(0x14, 0x00))
#define JL_LRCT                         ((JL_LRCT_TypeDef     *)JL_LRCT_BASE)


//............. 0x1500 - 0x16ff............ for timer2 timer3

#define JL_TIMER2_BASE                  (ls_base + map_adr(0x15, 0x00))
#define JL_TIMER2                       ((JL_TIMER_TypeDef     *)JL_TIMER2_BASE)

#define JL_TIMER3_BASE                  (ls_base + map_adr(0x16, 0x00))
#define JL_TIMER3                       ((JL_TIMER_TypeDef     *)JL_TIMER3_BASE)


//............. 0x1700 - 0x17ff............  for crc
typedef struct {
    __WO __u32 FIFO;
    __RW __u32 REG;
} JL_CRC_TypeDef;

#define JL_CRC_BASE                    (ls_base + map_adr(0x17, 0x00))
#define JL_CRC                         ((JL_CRC_TypeDef     *)JL_CRC_BASE)


//............. 0x1800 - 0x18ff............ for adc
typedef struct {
    __RW __u32 CON;
    __RO __u32 RES;
} JL_ADC_TypeDef;

#define JL_ADC_BASE                     (ls_base + map_adr(0x18, 0x00))
#define JL_ADC                          ((JL_ADC_TypeDef      *)JL_ADC_BASE)


//............. 0x1900 - 0x19ff............ for irflt
typedef struct {
    __RW __u32 RFLT_CON;
} JL_IR_TypeDef;

#define JL_IR_BASE                     (ls_base + map_adr(0x19, 0x00))
#define JL_IR                          ((JL_IR_TypeDef      *)JL_IR_BASE)



//............. 0x1a00 - 0x1aff............ for osa
typedef struct {
    __RW __u32 CON;
} JL_OSA_TypeDef;

#define JL_OSA_BASE                     (ls_base + map_adr(0x1a, 0x00))
#define JL_OSA                          ((JL_OSA_TypeDef      *)JL_OSA_BASE)



//............. 0x1b00 - 0x1bff............ for sdc
typedef struct {
    __RW __u32 CON0;
    __RW __u32 CON1;
    __RW __u32 CON2;
    __WO __u32 CPTR;
    __WO __u32 DPTR;
    __RW __u32 CTU_CON;
    __WO __u32 CTU_CNT;
} JL_SDC_TypeDef;

#define JL_SDC_BASE                     (ls_base + map_adr(0x1b, 0x00))
#define JL_SDC                          ((JL_SDC_TypeDef      *)JL_SDC_BASE)



//............. 0x1c00 - 0x1cff............ for spi
typedef struct {
    __RW __u32 CON;
    __WO __u32 BAUD;
    __RW __u32 BUF;
    __WO __u32 ADR;
    __WO __u32 CNT;
    __RW __u32 CON1;
} JL_SPI_TypeDef;

#define JL_SPI_BASE                     (ls_base + map_adr(0x1c, 0x00))
#define JL_SPI                          ((JL_SPI_TypeDef      *)JL_SPI_BASE)



//............. 0x1d00 - 0x22ff............ for port
typedef struct {
    __RW __u32 OUT;
    __RO __u32 IN;
    __RW __u32 DIR;
    __RW __u32 DIE;
    __RW __u32 PU;
    __RW __u32 PD;
    __RW __u32 HD0;
    __RW __u32 HD;
    __RW __u32 DIEH;
} JL_PORT_FLASH_TypeDef;

#define JL_PORTA_BASE                   (ls_base + map_adr(0x1d, 0x00))
#define JL_PORTA                        ((JL_PORT_FLASH_TypeDef *)JL_PORTA_BASE)

#define JL_PORTB_BASE                   (ls_base + map_adr(0x1e, 0x00))
#define JL_PORTB                        ((JL_PORT_FLASH_TypeDef *)JL_PORTB_BASE)

#define JL_PORTD_BASE                   (ls_base + map_adr(0x20, 0x00))
#define JL_PORTD                        ((JL_PORT_FLASH_TypeDef *)JL_PORTD_BASE)


//............. 0x2300 - 0x2aff............ // for omap/imap
typedef struct {
    __RW __u8 PA0_OUT;
    __RW __u8 PA1_OUT;
    __RW __u8 PA2_OUT;
    __RW __u8 PA3_OUT;
    __RW __u8 PA4_OUT;
    __RW __u8 PA5_OUT;
    __RW __u8 PA6_OUT;
    __RW __u8 PA7_OUT;
    __RW __u8 PA8_OUT;
    __RW __u8 PA9_OUT;
    __RW __u8 PA10_OUT;
    __RW __u8 PA11_OUT;
    __RW __u8 PA12_OUT;
    __RW __u8 PA13_OUT;
    __RW __u8 PA14_OUT;
    __RW __u8 PB0_OUT;
    __RW __u8 PB1_OUT;
    __RW __u8 PB2_OUT;
    __RW __u8 PB3_OUT;
    __RW __u8 PB4_OUT;
    __RW __u8 PB5_OUT;
    __RW __u8 PB6_OUT;
    __RW __u8 PB7_OUT;
    __RW __u8 PB8_OUT;
    __RW __u8 PB9_OUT;
    __RW __u8 PB10_OUT;
    __RW __u8 PD0_OUT;
    __RW __u8 PD1_OUT;
    __RW __u8 PD2_OUT;
    __RW __u8 PD3_OUT;
    __RW __u8 PD4_OUT;
} JL_OMAP_TypeDef;

#define JL_OMAP_BASE      (ls_base + map_adr(0x23, 0x00))
#define JL_OMAP           ((JL_OMAP_TypeDef   *)JL_OMAP_BASE)

typedef struct {
    __RW __u8 FI_GP_ICH0;
    __RW __u8 FI_GP_ICH1;
    __RW __u8 FI_GP_ICH2;
    __RW __u8 FI_GP_ICH3;
    __RW __u8 FI_GP_ICH4;
    __RW __u8 FI_GP_ICH5;
    __RW __u8 FI_GP_ICH6;
    __RW __u8 FI_GP_ICH7;
    __RW __u8 FI_SPI0_CLK;
    __RW __u8 FI_SPI0_DA0;
    __RW __u8 FI_SPI0_DA1;
    __RW __u8 FI_SPI0_DA2;
    __RW __u8 FI_SPI0_DA3;
    __RW __u8 FI_SD0_CMD;
    __RW __u8 FI_SD0_DA0;
    __RW __u8 FI_SD0_DA1;
    __RW __u8 FI_SD0_DA2;
    __RW __u8 FI_SD0_DA3;
    __RW __u8 FI_IIC0_SCL;
    __RW __u8 FI_IIC0_SDA;
    __RW __u8 FI_IIC1_SCL;
    __RW __u8 FI_IIC1_SDA;
    __RW __u8 FI_UART0_RX;
    __RW __u8 FI_UART1_RX;
    __RW __u8 FI_TOTAL;
} JL_IMAP_TypeDef;

#define JL_IMAP_BASE      (ls_base + map_adr(0x27, 0x00))
#define JL_IMAP           ((JL_IMAP_TypeDef   *)JL_IMAP_BASE)

//............. 0x2b00 - 0x2bff............

typedef struct {
    __RW __u32 EN;
    __RW __u32 EDGE;
    __RW __u32 CPND;
    __RW __u32 PND;
    __RW __u32 FLEN;
} JL_WAKEUP_TypeDef;

#define JL_WAKEUP_BASE               (ls_base + map_adr(0x2b, 0x00))
#define JL_WAKEUP                    ((JL_WAKEUP_TypeDef            *)JL_WAKEUP_BASE)

typedef struct {
    __RW __u32 CON0;
    __RW __u32 CON1;    //och
    __RW __u32 CON2;    //ich_con0
    __RW __u32 CON3;    //ich_con1
} JL_IOMC_TypeDef;

#define JL_IOMC_BASE                    (ls_base + map_adr(0x2b, 0x5))
#define JL_IOMC                         ((JL_IOMC_TypeDef      *)JL_IOMC_BASE)

//............. 0x2c00 - 0x2cff............	//for ls_pwr_ctl
typedef struct {
    __RW __u32 LDO_CON;
    __RW __u32 LRC_CON;
    __RW __u32 XOSC_CON;
} JL_PWR_TypeDef;

#define JL_PWR_BASE                    (ls_base + map_adr(0x2c, 0x00))
#define JL_PWR                         ((JL_PWR_TypeDef      *)JL_PWR_BASE)

//............. 0x2d00 - 0x2dff............	//for audio
typedef struct {
    __RW __u32(DAC_CON);
    __RW __u32(DAC_ADR);
    __RW __u16(DAC_LEN);
    __RW __u16(DAC_PNS);
    __RW __u16(DAC_HRP);
    __RW __u16(DAC_SWP);
    __RW __u16(DAC_SWN);
    __RW __u32(DAC_VL0);
    __RW __u32(DAC_VL1);
    __RW __u32(DAC_TM0);
    __RW __u32(DAC_TM1);
    __RW __u16(DAC_DTV);
    __RW __u16(DAC_DTB);
    __RW __u32(ADC_CON);
    __RW __u32(ADC_ADR);
    __RW __u16(ADC_LEN);
    __RW __u16(ADC_PNS);
    __RW __u16(ADC_HWP);
    __RW __u16(ADC_SRP);
    __RW __u16(ADC_SRN);
    __RW __u16(DAA_TSC);
    __RW __u32(ADDA_CON0);
    __RW __u32(ADDA_CON1);
} JL_AUDIO_TypeDef;

#define JL_AUDIO_BASE                   (ls_base + map_adr(0x2d, 0x00))
#define JL_AUDIO                        ((JL_AUDIO_TypeDef   *)JL_AUDIO_BASE)

//............. 0x2e00 - 0x2eff............	//for audio
typedef struct {
    __RW __u32 CON;
    __RW __u32 SEL;
    __RW __u32 DP;
    __RW __u32 DAT_VLD0;
    __RW __u32 DAT_VLD1;
    __RW __u32 DAT_VLD2;
    __RW __u32 DAT_VLD3;
} JL_AUD_MBIST_TypeDef;

#define JL_AUD_MBIST_BASE            (ls_base + map_adr(0x2e, 0x00))
#define JL_AUD_MBIST                 ((JL_AUD_MBIST_TypeDef *)JL_AUD_MBIST_BASE)



//............. 0x2800 - 0x28ff............

#include "io_omap.h"
#include "io_imap.h"


//===============================================================================//
//
//      eva sfr address define
//
//===============================================================================//

#define eva_base(i) (0x2c0000 + 0x800*i)

//==============================================================//
//  xbus	from dv17
//==============================================================//
#define xbus_sfr_ptr(num)   (*(volatile u32 *)(eva_base(0) + num*4))

#define xbus_ch00_lvl           xbus_sfr_ptr(0x00)
#define xbus_ch01_lvl           xbus_sfr_ptr(0x01)
#define xbus_ch02_lvl           xbus_sfr_ptr(0x02)
#define xbus_ch03_lvl           xbus_sfr_ptr(0x03)
#define xbus_ch04_lvl           xbus_sfr_ptr(0x04)
#define xbus_ch05_lvl           xbus_sfr_ptr(0x05)
#define xbus_ch06_lvl           xbus_sfr_ptr(0x06)
#define xbus_ch07_lvl           xbus_sfr_ptr(0x07)
#define xbus_ch08_lvl           xbus_sfr_ptr(0x08)
#define xbus_ch09_lvl           xbus_sfr_ptr(0x09)
#define xbus_ch10_lvl           xbus_sfr_ptr(0x0a)
#define xbus_ch11_lvl           xbus_sfr_ptr(0x0b)
#define xbus_ch12_lvl           xbus_sfr_ptr(0x0c)
#define xbus_ch13_lvl           xbus_sfr_ptr(0x0d)
#define xbus_ch14_lvl           xbus_sfr_ptr(0x0e)
#define xbus_ch15_lvl           xbus_sfr_ptr(0x0f)

#define xbus_ch16_lvl           xbus_sfr_ptr(0x10)
#define xbus_ch17_lvl           xbus_sfr_ptr(0x11)
#define xbus_ch18_lvl           xbus_sfr_ptr(0x12)
#define xbus_ch19_lvl           xbus_sfr_ptr(0x13)
#define xbus_ch20_lvl           xbus_sfr_ptr(0x14)
#define xbus_ch21_lvl           xbus_sfr_ptr(0x15)
#define xbus_ch22_lvl           xbus_sfr_ptr(0x16)
#define xbus_ch23_lvl           xbus_sfr_ptr(0x17)
#define xbus_ch24_lvl           xbus_sfr_ptr(0x18)
#define xbus_ch25_lvl           xbus_sfr_ptr(0x19)
#define xbus_ch26_lvl           xbus_sfr_ptr(0x1a)
#define xbus_ch27_lvl           xbus_sfr_ptr(0x1b)
#define xbus_ch28_lvl           xbus_sfr_ptr(0x1c)
#define xbus_ch29_lvl           xbus_sfr_ptr(0x1d)
#define xbus_ch30_lvl           xbus_sfr_ptr(0x1e)
#define xbus_ch31_lvl           xbus_sfr_ptr(0x1f)

#define xbus_lv1_prd            xbus_sfr_ptr(0x20)
#define xbus_lv2_prd            xbus_sfr_ptr(0x21)
#define xbus_dist0_con          xbus_sfr_ptr(0x22)
#define xbus_dist1_con          xbus_sfr_ptr(0x23)

//==============================================================//
//  isc	from dv17
//==============================================================//

#define isc_sfr_ptr(num)   (*(volatile u32 *)(eva_base(1) + num*4))

#define isc_pnd_con             isc_sfr_ptr(0x00)

#define isc_sen0_con            isc_sfr_ptr(0x08)
#define isc_sen0_vblk           isc_sfr_ptr(0x09)
#define isc_sen0_vact           isc_sfr_ptr(0x0a)
#define isc_sen0_hblk           isc_sfr_ptr(0x0b)
#define isc_sen0_hact           isc_sfr_ptr(0x0c)

#define isc_sen1_con            isc_sfr_ptr(0x10)
#define isc_sen1_vblk           isc_sfr_ptr(0x11)
#define isc_sen1_vact           isc_sfr_ptr(0x12)
#define isc_sen1_hblk           isc_sfr_ptr(0x13)
#define isc_sen1_hact           isc_sfr_ptr(0x14)

#define isc_lcds_con            isc_sfr_ptr(0x18)
#define isc_lcds_vblk           isc_sfr_ptr(0x19)
#define isc_lcds_vact           isc_sfr_ptr(0x1a)
#define isc_lcds_hblk           isc_sfr_ptr(0x1b)
#define isc_lcds_hact           isc_sfr_ptr(0x1c)

//==============================================================//
//  isp0	from dv15
//==============================================================//
#define isp_sfr_ptr(num)   (*(volatile u32 *)(eva_base(2) + num*4))

#define isp0_pnd_con            isp_sfr_ptr(0x00)
#define isp0_scn_con            isp_sfr_ptr(0x01)

#define isp0_src_con            isp_sfr_ptr(0x04)
#define isp0_src_haw            isp_sfr_ptr(0x05)
#define isp0_src_vaw            isp_sfr_ptr(0x06)

#define isp0_blc_off_r          isp_sfr_ptr(0x08)
#define isp0_blc_off_gr         isp_sfr_ptr(0x09)
#define isp0_blc_off_gb         isp_sfr_ptr(0x0a)
#define isp0_blc_off_b          isp_sfr_ptr(0x0b)

#define isp0_dpc_th0            isp_sfr_ptr(0x0c)
#define isp0_dpc_th1            isp_sfr_ptr(0x0d)
#define isp0_dpc_th2            isp_sfr_ptr(0x0e)

#define isp0_lsc_cen_x          isp_sfr_ptr(0x10)
#define isp0_lsc_cen_y          isp_sfr_ptr(0x11)
#define isp0_lsc_dth_th         isp_sfr_ptr(0x12)
#define isp0_lsc_dth_prm0       isp_sfr_ptr(0x13)
#define isp0_lsc_dth_prm1       isp_sfr_ptr(0x14)
#define isp0_lsc_lut_r          isp_sfr_ptr(0x15)
#define isp0_lsc_lut_g          isp_sfr_ptr(0x16)
#define isp0_lsc_lut_b          isp_sfr_ptr(0x17)

#define isp0_awb_gain_r         isp_sfr_ptr(0x18)
#define isp0_awb_gain_g         isp_sfr_ptr(0x19)
#define isp0_awb_gain_b         isp_sfr_ptr(0x1a)

#define isp0_drc_lut            isp_sfr_ptr(0x1b)

#define isp0_tnr_con            isp_sfr_ptr(0x1c)
#define isp0_tnr_base           isp_sfr_ptr(0x1d)
#define isp0_tnr_size           isp_sfr_ptr(0x1e)
#define isp0_tnr_2d_str         isp_sfr_ptr(0x1f)
#define isp0_tnr_3d_th0         isp_sfr_ptr(0x20)
#define isp0_tnr_3d_th1         isp_sfr_ptr(0x21)
#define isp0_tnr_mt_th          isp_sfr_ptr(0x22)
#define isp0_tnr_wmax           isp_sfr_ptr(0x23)
#define isp0_tnr_wmin           isp_sfr_ptr(0x24)
#define isp0_tnr_wslope         isp_sfr_ptr(0x25)
#define isp0_tnr_break          isp_sfr_ptr(0x26)
#define isp0_tnr_scale0         isp_sfr_ptr(0x27)
#define isp0_tnr_scale1         isp_sfr_ptr(0x28)
#define isp0_tnr_scale2         isp_sfr_ptr(0x29)
#define isp0_tnr_scale3         isp_sfr_ptr(0x2a)
#define isp0_tnr_scale4         isp_sfr_ptr(0x2b)
#define isp0_tnr_scale5         isp_sfr_ptr(0x2c)
#define isp0_tnr_scale6         isp_sfr_ptr(0x2d)
#define isp0_tnr_scale7         isp_sfr_ptr(0x2e)

#define isp0_ccm_r_coe0         isp_sfr_ptr(0x30)
#define isp0_ccm_r_coe1         isp_sfr_ptr(0x31)
#define isp0_ccm_r_coe2         isp_sfr_ptr(0x32)
#define isp0_ccm_r_off          isp_sfr_ptr(0x33)
#define isp0_ccm_g_coe0         isp_sfr_ptr(0x34)
#define isp0_ccm_g_coe1         isp_sfr_ptr(0x35)
#define isp0_ccm_g_coe2         isp_sfr_ptr(0x36)
#define isp0_ccm_g_off          isp_sfr_ptr(0x37)
#define isp0_ccm_b_coe0         isp_sfr_ptr(0x38)
#define isp0_ccm_b_coe1         isp_sfr_ptr(0x39)
#define isp0_ccm_b_coe2         isp_sfr_ptr(0x3a)
#define isp0_ccm_b_off          isp_sfr_ptr(0x3b)

#define isp0_gma_r_lut          isp_sfr_ptr(0x3c)
#define isp0_gma_g_lut          isp_sfr_ptr(0x3d)
#define isp0_gma_b_lut          isp_sfr_ptr(0x3e)
#define isp0_csc_y_lut          isp_sfr_ptr(0x3f)

#define isp0_dnr_sim_th         isp_sfr_ptr(0x40)
#define isp0_dnr_rng_sgm        isp_sfr_ptr(0x41)
#define isp0_dnr_gaus_c00       isp_sfr_ptr(0x42)
#define isp0_dnr_gaus_c01       isp_sfr_ptr(0x43)
#define isp0_dnr_gaus_c02       isp_sfr_ptr(0x44)
#define isp0_dnr_gaus_c03       isp_sfr_ptr(0x45)
#define isp0_dnr_gaus_c11       isp_sfr_ptr(0x46)
#define isp0_dnr_gaus_c12       isp_sfr_ptr(0x47)
#define isp0_dnr_gaus_c13       isp_sfr_ptr(0x48)
#define isp0_dnr_gaus_c22       isp_sfr_ptr(0x49)
#define isp0_dnr_gaus_c23       isp_sfr_ptr(0x4a)
#define isp0_dnr_gaus_c33       isp_sfr_ptr(0x4b)
#define isp0_dnr_cmid_en        isp_sfr_ptr(0x4c)

#define isp0_shp_lone_th        isp_sfr_ptr(0x4d)
#define isp0_shp_ech_min        isp_sfr_ptr(0x4e)
#define isp0_shp_ech_max        isp_sfr_ptr(0x4f)

#define isp0_shp_hf_th0         isp_sfr_ptr(0x50)
#define isp0_shp_hf_th1         isp_sfr_ptr(0x51)
#define isp0_shp_hf_th2         isp_sfr_ptr(0x52)
#define isp0_shp_hf_amt         isp_sfr_ptr(0x53)
#define isp0_shp_hf_gain        isp_sfr_ptr(0x54)
#define isp0_shp_hf_c00         isp_sfr_ptr(0x55)
#define isp0_shp_hf_c01         isp_sfr_ptr(0x56)
#define isp0_shp_hf_c02         isp_sfr_ptr(0x57)
#define isp0_shp_hf_c10         isp_sfr_ptr(0x58)
#define isp0_shp_hf_c11         isp_sfr_ptr(0x59)
#define isp0_shp_hf_c12         isp_sfr_ptr(0x5a)
#define isp0_shp_hf_c20         isp_sfr_ptr(0x5b)
#define isp0_shp_hf_c21         isp_sfr_ptr(0x5c)
#define isp0_shp_hf_c22         isp_sfr_ptr(0x5d)
#define isp0_shp_hf_sft         isp_sfr_ptr(0x5e)

#define isp0_shp_mf_th0         isp_sfr_ptr(0x60)
#define isp0_shp_mf_th1         isp_sfr_ptr(0x61)
#define isp0_shp_mf_amt         isp_sfr_ptr(0x62)
#define isp0_shp_mf_gain        isp_sfr_ptr(0x63)
#define isp0_shp_mf_c00         isp_sfr_ptr(0x64)
#define isp0_shp_mf_c01         isp_sfr_ptr(0x65)
#define isp0_shp_mf_c02         isp_sfr_ptr(0x66)
#define isp0_shp_mf_c10         isp_sfr_ptr(0x67)
#define isp0_shp_mf_c11         isp_sfr_ptr(0x68)
#define isp0_shp_mf_c12         isp_sfr_ptr(0x69)
#define isp0_shp_mf_c20         isp_sfr_ptr(0x6a)
#define isp0_shp_mf_c21         isp_sfr_ptr(0x6b)
#define isp0_shp_mf_c22         isp_sfr_ptr(0x6c)
#define isp0_shp_mf_sft         isp_sfr_ptr(0x6d)

#define isp0_shp_cr_smt_th      isp_sfr_ptr(0x70)
#define isp0_shp_cr_c00         isp_sfr_ptr(0x71)
#define isp0_shp_cr_c01         isp_sfr_ptr(0x72)
#define isp0_shp_cr_c02         isp_sfr_ptr(0x73)
#define isp0_shp_cr_c10         isp_sfr_ptr(0x74)
#define isp0_shp_cr_c11         isp_sfr_ptr(0x75)
#define isp0_shp_cr_c12         isp_sfr_ptr(0x76)
#define isp0_shp_cr_c20         isp_sfr_ptr(0x77)
#define isp0_shp_cr_c21         isp_sfr_ptr(0x78)
#define isp0_shp_cr_c22         isp_sfr_ptr(0x79)
#define isp0_shp_cr_sft         isp_sfr_ptr(0x7a)

#define isp0_cbs_y_gain         isp_sfr_ptr(0x80)
#define isp0_cbs_u_gain         isp_sfr_ptr(0x81)
#define isp0_cbs_v_gain         isp_sfr_ptr(0x82)
#define isp0_cbs_y_offs         isp_sfr_ptr(0x83)
#define isp0_cbs_u_offs         isp_sfr_ptr(0x84)
#define isp0_cbs_v_offs         isp_sfr_ptr(0x85)

#define isp0_out_hst            isp_sfr_ptr(0x88)
#define isp0_out_hed            isp_sfr_ptr(0x89)
#define isp0_out_vst            isp_sfr_ptr(0x8a)
#define isp0_out_ved            isp_sfr_ptr(0x8b)

#define isp0_stc_ae_base0       isp_sfr_ptr(0x90)
#define isp0_stc_ae_base1       isp_sfr_ptr(0x91)
#define isp0_stc_ae_base2       isp_sfr_ptr(0x92)
#define isp0_stc_ae_base3       isp_sfr_ptr(0x93)
#define isp0_stc_ae_basex       isp_sfr_ptr(0x94)
#define isp0_stc_ae_en          isp_sfr_ptr(0x95)
#define isp0_stc_ae_lv1         isp_sfr_ptr(0x96)
#define isp0_stc_ae_lv2         isp_sfr_ptr(0x97)
#define isp0_stc_ae_lv3         isp_sfr_ptr(0x98)
#define isp0_stc_ae_lv4         isp_sfr_ptr(0x99)
#define isp0_stc_ae_lv5         isp_sfr_ptr(0x9a)
#define isp0_stc_ae_lv6         isp_sfr_ptr(0x9b)
#define isp0_stc_ae_lv7         isp_sfr_ptr(0x9c)

#define isp0_stc_wb_base0       isp_sfr_ptr(0xa0)
#define isp0_stc_wb_base1       isp_sfr_ptr(0xa1)
#define isp0_stc_wb_base2       isp_sfr_ptr(0xa2)
#define isp0_stc_wb_base3       isp_sfr_ptr(0xa3)
#define isp0_stc_wb_basex       isp_sfr_ptr(0xa4)
#define isp0_stc_wb_en          isp_sfr_ptr(0xa5)
#define isp0_stc_wb_r_th        isp_sfr_ptr(0xa6)
#define isp0_stc_wb_g_th        isp_sfr_ptr(0xa7)
#define isp0_stc_wb_b_th        isp_sfr_ptr(0xa8)
#define isp0_stc_wb_w_th        isp_sfr_ptr(0xa9)
#define isp0_stc_wb_y_min       isp_sfr_ptr(0xaa)
#define isp0_stc_wb_y_max       isp_sfr_ptr(0xab)
#define isp0_stc_wb_rg_min      isp_sfr_ptr(0xac)
#define isp0_stc_wb_rg_max      isp_sfr_ptr(0xad)
#define isp0_stc_wb_bg_min      isp_sfr_ptr(0xae)
#define isp0_stc_wb_bg_max      isp_sfr_ptr(0xaf)

//==============================================================//
//  imc		from dv17
//==============================================================//
#define imc_sfr_ptr(num)   (*(volatile u32 *)(eva_base(3) + num*4))

#define imc_pnd_con             imc_sfr_ptr(0x000)

#define imc_rep0_con            imc_sfr_ptr(0x001)
#define imc_rep0_h_cfg          imc_sfr_ptr(0x002)
#define imc_rep0_v_cfg          imc_sfr_ptr(0x003)
#define imc_rep0_y_base         imc_sfr_ptr(0x004)
#define imc_rep0_u_base         imc_sfr_ptr(0x005)
#define imc_rep0_v_base         imc_sfr_ptr(0x006)

#define imc_rep1_con            imc_sfr_ptr(0x009)
#define imc_rep1_h_cfg          imc_sfr_ptr(0x00a)
#define imc_rep1_v_cfg          imc_sfr_ptr(0x00b)
#define imc_rep1_y_base         imc_sfr_ptr(0x00c)
#define imc_rep1_u_base         imc_sfr_ptr(0x00d)
#define imc_rep1_v_base         imc_sfr_ptr(0x00e)

#define imc_ch0_com_con         imc_sfr_ptr(0x020)
#define imc_ch0_src_con         imc_sfr_ptr(0x021)
#define imc_ch0_crop_h          imc_sfr_ptr(0x022)
#define imc_ch0_crop_v          imc_sfr_ptr(0x023)
#define imc_ch0_h_stp           imc_sfr_ptr(0x024)
#define imc_ch0_h_wth           imc_sfr_ptr(0x025)
#define imc_ch0_v_stp           imc_sfr_ptr(0x026)
#define imc_ch0_v_wth           imc_sfr_ptr(0x027)
#define imc_ch0_dma_con         imc_sfr_ptr(0x028)
#define imc_ch0_dma_cnt         imc_sfr_ptr(0x029)
#define imc_ch0_dma_y_bs        imc_sfr_ptr(0x02a)
#define imc_ch0_dma_u_bs        imc_sfr_ptr(0x02b)
#define imc_ch0_dma_v_bs        imc_sfr_ptr(0x02c)
#define imc_ch0_osd_con         imc_sfr_ptr(0x02d)
#define imc_ch0_osd_color0      imc_sfr_ptr(0x02e)
#define imc_ch0_osd_color1      imc_sfr_ptr(0x02f)
#define imc_ch0_osd_color2      imc_sfr_ptr(0x030)
#define imc_ch0_osd_color3      imc_sfr_ptr(0x031)
#define imc_ch0_osd0_h_cfg      imc_sfr_ptr(0x032)
#define imc_ch0_osd0_v_cfg      imc_sfr_ptr(0x033)
#define imc_ch0_osd0_base       imc_sfr_ptr(0x034)
#define imc_ch0_osd1_h_cfg      imc_sfr_ptr(0x035)
#define imc_ch0_osd1_v_cfg      imc_sfr_ptr(0x036)
#define imc_ch0_osd1_base       imc_sfr_ptr(0x037)

#define imc_ch1_com_con         imc_sfr_ptr(0x040)
#define imc_ch1_src_con         imc_sfr_ptr(0x041)
#define imc_ch1_crop_h          imc_sfr_ptr(0x042)
#define imc_ch1_crop_v          imc_sfr_ptr(0x043)
#define imc_ch1_h_stp           imc_sfr_ptr(0x044)
#define imc_ch1_h_wth           imc_sfr_ptr(0x045)
#define imc_ch1_v_stp           imc_sfr_ptr(0x046)
#define imc_ch1_v_wth           imc_sfr_ptr(0x047)
#define imc_ch1_dma_con         imc_sfr_ptr(0x048)
#define imc_ch1_dma_cnt         imc_sfr_ptr(0x049)
#define imc_ch1_dma_y_bs        imc_sfr_ptr(0x04a)
#define imc_ch1_dma_u_bs        imc_sfr_ptr(0x04b)
#define imc_ch1_dma_v_bs        imc_sfr_ptr(0x04c)
#define imc_ch1_osd_con         imc_sfr_ptr(0x04d)
#define imc_ch1_osd_color0      imc_sfr_ptr(0x04e)
#define imc_ch1_osd_color1      imc_sfr_ptr(0x04f)
#define imc_ch1_osd_color2      imc_sfr_ptr(0x050)
#define imc_ch1_osd_color3      imc_sfr_ptr(0x051)
#define imc_ch1_osd0_h_cfg      imc_sfr_ptr(0x052)
#define imc_ch1_osd0_v_cfg      imc_sfr_ptr(0x053)
#define imc_ch1_osd0_base       imc_sfr_ptr(0x054)
#define imc_ch1_osd1_h_cfg      imc_sfr_ptr(0x055)
#define imc_ch1_osd1_v_cfg      imc_sfr_ptr(0x056)
#define imc_ch1_osd1_base       imc_sfr_ptr(0x057)

#define imc_ch2_com_con         imc_sfr_ptr(0x060)
#define imc_ch2_src_con         imc_sfr_ptr(0x061)
#define imc_ch2_crop_h          imc_sfr_ptr(0x062)
#define imc_ch2_crop_v          imc_sfr_ptr(0x063)
#define imc_ch2_h_stp           imc_sfr_ptr(0x064)
#define imc_ch2_h_wth           imc_sfr_ptr(0x065)
#define imc_ch2_v_stp           imc_sfr_ptr(0x066)
#define imc_ch2_v_wth           imc_sfr_ptr(0x067)
#define imc_ch2_dma_con         imc_sfr_ptr(0x068)
#define imc_ch2_dma_cnt         imc_sfr_ptr(0x069)
#define imc_ch2_dma_y_bs        imc_sfr_ptr(0x06a)
#define imc_ch2_dma_u_bs        imc_sfr_ptr(0x06b)
#define imc_ch2_dma_v_bs        imc_sfr_ptr(0x06c)

#define imc_ch3_com_con         imc_sfr_ptr(0x070)
#define imc_ch3_src_con         imc_sfr_ptr(0x071)
#define imc_ch3_crop_h          imc_sfr_ptr(0x072)
#define imc_ch3_crop_v          imc_sfr_ptr(0x073)
#define imc_ch3_h_stp           imc_sfr_ptr(0x074)
#define imc_ch3_h_wth           imc_sfr_ptr(0x075)
#define imc_ch3_v_stp           imc_sfr_ptr(0x076)
#define imc_ch3_v_wth           imc_sfr_ptr(0x077)
#define imc_ch3_dma_con         imc_sfr_ptr(0x078)
#define imc_ch3_dma_cnt         imc_sfr_ptr(0x079)
#define imc_ch3_dma_y_bs        imc_sfr_ptr(0x07a)
#define imc_ch3_dma_u_bs        imc_sfr_ptr(0x07b)
#define imc_ch3_dma_v_bs        imc_sfr_ptr(0x07c)

#define imc_ch4_com_con         imc_sfr_ptr(0x080)
#define imc_ch4_src_con         imc_sfr_ptr(0x081)
#define imc_ch4_crop_h          imc_sfr_ptr(0x082)
#define imc_ch4_crop_v          imc_sfr_ptr(0x083)
#define imc_ch4_h_stp           imc_sfr_ptr(0x084)
#define imc_ch4_h_wth           imc_sfr_ptr(0x085)
#define imc_ch4_v_stp           imc_sfr_ptr(0x086)
#define imc_ch4_v_wth           imc_sfr_ptr(0x087)
#define imc_ch4_dma_con         imc_sfr_ptr(0x088)
#define imc_ch4_dma_cnt         imc_sfr_ptr(0x089)
#define imc_ch4_dma_y_bs        imc_sfr_ptr(0x08a)
#define imc_ch4_dma_u_bs        imc_sfr_ptr(0x08b)
#define imc_ch4_dma_v_bs        imc_sfr_ptr(0x08c)

#define imc_ch5_com_con         imc_sfr_ptr(0x090)
#define imc_ch5_src_con         imc_sfr_ptr(0x091)
#define imc_ch5_crop_h          imc_sfr_ptr(0x092)
#define imc_ch5_crop_v          imc_sfr_ptr(0x093)
#define imc_ch5_h_stp           imc_sfr_ptr(0x094)
#define imc_ch5_h_wth           imc_sfr_ptr(0x095)
#define imc_ch5_v_stp           imc_sfr_ptr(0x096)
#define imc_ch5_v_wth           imc_sfr_ptr(0x097)
#define imc_ch5_dma_con         imc_sfr_ptr(0x098)
#define imc_ch5_dma_cnt         imc_sfr_ptr(0x099)
#define imc_ch5_dma_y_bs        imc_sfr_ptr(0x09a)
#define imc_ch5_dma_u_bs        imc_sfr_ptr(0x09b)
#define imc_ch5_dma_v_bs        imc_sfr_ptr(0x09c)

#define imc_lex_com_con         imc_sfr_ptr(0x0a0)
#define imc_lex_tpz_cfg0        imc_sfr_ptr(0x0a1)
#define imc_lex_tpz_cfg1        imc_sfr_ptr(0x0a2)
#define imc_lex_tpz_cfg2        imc_sfr_ptr(0x0a3)
#define imc_lex_sca_wth         imc_sfr_ptr(0x0a4)
#define imc_lex_dma_con         imc_sfr_ptr(0x0a5)
#define imc_lex_dma_base0       imc_sfr_ptr(0x0a6)
#define imc_lex_dma_base1       imc_sfr_ptr(0x0a7)

//==============================================================//
//  mipi csi
//==============================================================//
#define csi_sfr_ptr(num)   (*(volatile u32 *)(eva_base(4) + num*4))

#define csi_sys_con             csi_sfr_ptr(0x00)

#define csi_rmap_con            csi_sfr_ptr(0x01)
#define csi_lane_con            csi_sfr_ptr(0x02)
#define csi_tval_con            csi_sfr_ptr(0x03)
#define csi_task_con            csi_sfr_ptr(0x04)
#define csi_task_haw            csi_sfr_ptr(0x05)
#define csi_task_vaw            csi_sfr_ptr(0x06)
#define csi_phy_con0            csi_sfr_ptr(0x07)
#define csi_phy_con1            csi_sfr_ptr(0x08)
#define csi_phy_con2            csi_sfr_ptr(0x09)

#define lvds_rx_pll_con         csi_sfr_ptr(0x10)
#define lvds_rx_pll_nf          csi_sfr_ptr(0x11)
#define lvds_rx_pll_nr          csi_sfr_ptr(0x12)
#define lvds_rx_phy_con         csi_sfr_ptr(0x13)
#define lvds_rx_dec0_con        csi_sfr_ptr(0x14)
#define lvds_rx_lane0_con       csi_sfr_ptr(0x15)
#define lvds_rx_dec1_con        csi_sfr_ptr(0x16)
#define lvds_rx_lane1_con       csi_sfr_ptr(0x17)
#define lvds_rx_out_con         csi_sfr_ptr(0x18)
#define lvds_rx_out_haw         csi_sfr_ptr(0x19)
#define lvds_rx_out_vaw         csi_sfr_ptr(0x1a)

//==============================================================//
//  isp1
//==============================================================//

#define isp1_sfr_ptr(num)   (*(volatile u32 *)(eva_base(5) + num*4))

#define isp1_pnd_con            isp1_sfr_ptr(0x000)
#define isp1_src_con            isp1_sfr_ptr(0x001)
#define isp1_src_haw            isp1_sfr_ptr(0x002)
#define isp1_src_vaw            isp1_sfr_ptr(0x003)

#define isp2_src_con            isp1_sfr_ptr(0x004)
#define isp2_src_haw            isp1_sfr_ptr(0x005)
#define isp2_src_vaw            isp1_sfr_ptr(0x006)


//===============================================================================//
//
//      husb sfr address define
//
//===============================================================================//

#define husb_base(i)    (0x2b0000 + 0x1000*i)

#define husb_phy_com_sfr_ptr(num)   (*(volatile u32 *)(husb_base(0) + num*4))

#define HUSB_COM_CON0                   husb_phy_com_sfr_ptr(0x00)
#define HUSB_COM_CON1                   husb_phy_com_sfr_ptr(0x01)
#define HUSB_COM_CON2                   husb_phy_com_sfr_ptr(0x02)
#define HUSB_PHY0_CON0                  husb_phy_com_sfr_ptr(0x03)
#define HUSB_PHY0_CON1                  husb_phy_com_sfr_ptr(0x04)
#define HUSB_PHY0_CON2                  husb_phy_com_sfr_ptr(0x05)
#define HUSB_PHY0_CON3                  husb_phy_com_sfr_ptr(0x06)

#define HUSB_TRIM_CON0                  husb_phy_com_sfr_ptr(0x07)
#define HUSB_TRIM_CON1                  husb_phy_com_sfr_ptr(0x08)
#define HUSB_TRIM_PND                   husb_phy_com_sfr_ptr(0x09)
#define HUSB_PLL_NR                     husb_phy_com_sfr_ptr(0x0a)
#define HUSB_FRQ_CNT                    husb_phy_com_sfr_ptr(0x0b)
#define HUSB_FRQ_SCA                    husb_phy_com_sfr_ptr(0x0c)


#define husb_sie_sfr_ptr(num)   (husb_base(1) + num)

#define H0_FADDR                        (*(volatile u8  *)husb_sie_sfr_ptr(0x000))
#define H0_POWER                        (*(volatile u8  *)husb_sie_sfr_ptr(0x001))
#define H0_INTRTX                       (*(volatile u16 *)husb_sie_sfr_ptr(0x002))
#define H0_INTRRX                       (*(volatile u16 *)husb_sie_sfr_ptr(0x004))
#define H0_INTRTXE                      (*(volatile u16 *)husb_sie_sfr_ptr(0x006))
#define H0_INTRRXE                      (*(volatile u16 *)husb_sie_sfr_ptr(0x008))
#define H0_INTRUSB                      (*(volatile u8  *)husb_sie_sfr_ptr(0x00a))
#define H0_INTRUSBE                     (*(volatile u8  *)husb_sie_sfr_ptr(0x00b))
#define H0_FRAME                        (*(volatile u16 *)husb_sie_sfr_ptr(0x00c))
#define H0_INDEX                        (*(volatile u8  *)husb_sie_sfr_ptr(0x00e))
#define H0_TESTMODE                     (*(volatile u8  *)husb_sie_sfr_ptr(0x00f))
#define H0_FIFO0                        (*(volatile u8  *)husb_sie_sfr_ptr(0x020))
#define H0_FIFO1                        (*(volatile u8  *)husb_sie_sfr_ptr(0x024))
#define H0_FIFO2                        (*(volatile u8  *)husb_sie_sfr_ptr(0x028))
#define H0_FIFO3                        (*(volatile u8  *)husb_sie_sfr_ptr(0x02c))
#define H0_FIFO4                        (*(volatile u8  *)husb_sie_sfr_ptr(0x030))
#define H0_DEVCTL                       (*(volatile u8  *)husb_sie_sfr_ptr(0x060))
#define H0_CSR0                         (*(volatile u16 *)husb_sie_sfr_ptr(0x102))
#define H0_COUNT0                       (*(volatile u16 *)husb_sie_sfr_ptr(0x108))
#define H0_NAKLIMIT0                    (*(volatile u8  *)husb_sie_sfr_ptr(0x10b))
#define H0_CFGDATA                      (*(volatile u8  *)husb_sie_sfr_ptr(0x10f))
#define H0_EP1TXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x110))
#define H0_EP1TXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x112))
#define H0_EP1RXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x114))
#define H0_EP1RXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x116))
#define H0_EP1RXCOUNT                   (*(volatile u16 *)husb_sie_sfr_ptr(0x118))
#define H0_EP1TXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x11a))
#define H0_EP1TXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x11b))
#define H0_EP1RXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x11c))
#define H0_EP1RXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x11d))
#define H0_EP1FIFOSIZE                  (*(volatile u8  *)husb_sie_sfr_ptr(0x11f))
#define H0_EP2TXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x120))
#define H0_EP2TXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x122))
#define H0_EP2RXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x124))
#define H0_EP2RXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x126))
#define H0_EP2RXCOUNT                   (*(volatile u16 *)husb_sie_sfr_ptr(0x128))
#define H0_EP2TXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x12a))
#define H0_EP2TXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x12b))
#define H0_EP2RXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x12c))
#define H0_EP2RXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x12d))
#define H0_EP2FIFOSIZE                  (*(volatile u8  *)husb_sie_sfr_ptr(0x12f))
#define H0_EP3TXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x130))
#define H0_EP3TXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x132))
#define H0_EP3RXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x134))
#define H0_EP3RXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x136))
#define H0_EP3RXCOUNT                   (*(volatile u16 *)husb_sie_sfr_ptr(0x138))
#define H0_EP3TXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x13a))
#define H0_EP3TXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x13b))
#define H0_EP3RXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x13c))
#define H0_EP3RXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x13d))
#define H0_EP3FIFOSIZE                  (*(volatile u8  *)husb_sie_sfr_ptr(0x13f))
#define H0_EP4TXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x140))
#define H0_EP4TXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x142))
#define H0_EP4RXMAXP                    (*(volatile u16 *)husb_sie_sfr_ptr(0x144))
#define H0_EP4RXCSR                     (*(volatile u16 *)husb_sie_sfr_ptr(0x146))
#define H0_EP4RXCOUNT                   (*(volatile u16 *)husb_sie_sfr_ptr(0x148))
#define H0_EP4TXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x14a))
#define H0_EP4TXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x14b))
#define H0_EP4RXTYPE                    (*(volatile u8  *)husb_sie_sfr_ptr(0x14c))
#define H0_EP4RXINTERVAL                (*(volatile u8  *)husb_sie_sfr_ptr(0x14d))
#define H0_EP4FIFOSIZE                  (*(volatile u8  *)husb_sie_sfr_ptr(0x14f))
#define H0_RX_DPKTDIS                   (*(volatile u16 *)husb_sie_sfr_ptr(0x340))
#define H0_TX_DPKTDIS                   (*(volatile u16 *)husb_sie_sfr_ptr(0x342))
#define H0_C_T_UCH                      (*(volatile u16 *)husb_sie_sfr_ptr(0x344))



#define husb_ctl_sfr_ptr(num)   (*(volatile u32 *)(husb_base(2) + num*4))

#define H0_SIE_CON                      husb_ctl_sfr_ptr(0x00)
#define H0_EP0_CNT                      husb_ctl_sfr_ptr(0x01)
#define H0_EP1_CNT                      husb_ctl_sfr_ptr(0x02)
#define H0_EP2_CNT                      husb_ctl_sfr_ptr(0x03)
#define H0_EP3_CNT                      husb_ctl_sfr_ptr(0x04)
#define H0_EP4_CNT                      husb_ctl_sfr_ptr(0x05)
#define H0_EP5_CNT                      husb_ctl_sfr_ptr(0x06)
#define H0_EP6_CNT                      husb_ctl_sfr_ptr(0x07)
#define H0_EP1_TADR                     husb_ctl_sfr_ptr(0x08)
#define H0_EP1_RADR                     husb_ctl_sfr_ptr(0x09)
#define H0_EP2_TADR                     husb_ctl_sfr_ptr(0x0a)
#define H0_EP2_RADR                     husb_ctl_sfr_ptr(0x0b)
#define H0_EP3_TADR                     husb_ctl_sfr_ptr(0x0c)
#define H0_EP3_RADR                     husb_ctl_sfr_ptr(0x0d)
#define H0_EP4_TADR                     husb_ctl_sfr_ptr(0x0e)
#define H0_EP4_RADR                     husb_ctl_sfr_ptr(0x0f)
#define H0_EP5_TADR                     husb_ctl_sfr_ptr(0x10)
#define H0_EP5_RADR                     husb_ctl_sfr_ptr(0x11)
#define H0_EP6_TADR                     husb_ctl_sfr_ptr(0x12)
#define H0_EP6_RADR                     husb_ctl_sfr_ptr(0x13)
#define H0_EP3_ISO_CON                  husb_ctl_sfr_ptr(0x14)
#define H0_EP4_ISO_CON                  husb_ctl_sfr_ptr(0x15)


#endif


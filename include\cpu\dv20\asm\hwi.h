#ifndef HWI_H
#define HWI_H

#include "core.h"

#define IRQ_EXCEPTION_IDX  1		//0
#define IRQ_TICK_TMR_IDX   3		//0

#define IRQ_TIME0_IDX      4   	//0
#define IRQ_TIME1_IDX      5   	//0
#define IRQ_TIME2_IDX      6   	//0
#define IRQ_TIME3_IDX      7   	//0

#define IRQ_UART1_IDX       11		//0
#define IRQ_UART0_IDX       12		//0
#define IRQ_SD0_IDX        13		//0

#define IRQ_AUDIO_IDX	   18

#define IRQ_IIC0_IDX       23		//0
#define IRQ_IIC1_IDX       24		//0

#define IRQ_USB_SOF_IDX    46
#define IRQ_USB_SIE_IDX    47
#define IRQ_USB_TRIM_IDX   48

#define IRQ_PORT_IDX       38
#define IRQ_GPCNT_IDX      42
#define IRQ_LRCT_IDX       43

#define IRQ_YUYV_IDX       100
#define IRQ_IMC_IDX        103

#define IRQ_ISC_IDX        105
#define IRQ_JPG_IDX        106

#define IRQ_MEM_ADDR       (ISR_ENTRY)
// #define IRQ_MEM_ADDR       (0x0e0e00)

#define MAX_IRQ_ENTRY_NUM  128

//---------------------------------------------//
// interrupt cli/sti
//---------------------------------------------//

static inline int int_cli(void)
{
    int msg;
    asm volatile("cli %0" : "=r"(msg) :);
    return msg;
}

static inline void int_sti(int msg)
{
    asm volatile("sti %0" :: "r"(msg));
}

static inline int core_num(void)
{
    /* u32 num; */
    /* asm volatile("%0 = cnum" : "=r"(num) :); */
    /* return num; */
    return 0;
}

//---------------------------------------------//
// clock & power
//---------------------------------------------//

#define q32small_CKPWR_CNUM   1
#define q32small_RESET_MODE   0
#define q32small_IDLE_MODE    1
#define q32small_HOLD_MODE    2

void q32small_ckpwr_isr(void);
void q32small_ckpwr_into(unsigned char cnum, unsigned char mode);
void q32small_ckpwr_exit(unsigned char cnum, unsigned char mode);

//*********************************************************************************//
// Module name : testset                                                           //
// Description : q32small testset subroutine                                         //
// By Designer : zequan_liu                                                        //
// Dat changed :                                                                   //
//*********************************************************************************//

static inline void q32small_testset(u8 volatile *ptr)
{
    asm volatile(
        " 1:            \n\t "
        " testset b[%0] \n\t "
        " ifeq goto 1b  \n\t "
        :
        : "p"(ptr)
        : "memory"
    );
}

static inline void q32small_testclr(u8 volatile *ptr)
{
    asm volatile(
        " b[%0] = %1    \n\t "
        :
        : "p"(ptr), "r"(0)
        : "memory"
    );
}

void hwi_init(void);
void interrupt_init();
void local_irq_enable();
void local_irq_disable();
void HWI_Install(unsigned int index, unsigned int isr, unsigned int priority);
void bit_clr_ie(unsigned char index);
void bit_set_ie(unsigned char index);
void ENABLE_INT(void) ;
void DISABLE_INT(void) ;

#define INSTALL_HWI(a,b,c) HWI_Install(a,b,c)

#define enable_int ENABLE_INT
#define disable_int DISABLE_INT

#ifdef IRQ_TIME_COUNT_EN
void irq_handler_enter(int irq);
void irq_handler_exit(int irq);
void irq_handler_times_dump();
#else
#define irq_handler_enter(irq)      do { }while(0)
#define irq_handler_exit(irq)       do { }while(0)
#define irq_handler_times_dump()    do { }while(0)
#endif


#endif


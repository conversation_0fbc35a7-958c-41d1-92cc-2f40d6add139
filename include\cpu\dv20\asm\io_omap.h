

//===============================================================================//
//
//      output function define
//
//===============================================================================//
#define FO_GP_OCH0        ((0 << 2)|BIT(1))
#define FO_GP_OCH1        ((1 << 2)|BIT(1))
#define FO_GP_OCH2        ((2 << 2)|BIT(1))
#define FO_GP_OCH3        ((3 << 2)|BIT(1))
#define FO_GP_OCH4        ((4 << 2)|BIT(1))
#define FO_GP_OCH5        ((5 << 2)|BIT(1))
#define FO_GP_OCH6        ((6 << 2)|BIT(1))
#define FO_GP_OCH7        ((7 << 2)|BIT(1))
#define FO_SPI0_CLK        ((8 << 2)|BIT(1)|BIT(0))
#define FO_SPI0_DA0        ((9 << 2)|BIT(1)|BIT(0))
#define FO_SPI0_DA1        ((10 << 2)|BIT(1)|BIT(0))
#define FO_SPI0_DA2        ((11 << 2)|BIT(1)|BIT(0))
#define FO_SPI0_DA3        ((12 << 2)|BIT(1)|BIT(0))
#define FO_SD0_CLK        ((13 << 2)|BIT(1)|BIT(0))
#define FO_SD0_CMD        ((14 << 2)|BIT(1)|BIT(0))
#define FO_SD0_DA0        ((15 << 2)|BIT(1)|BIT(0))
#define FO_SD0_DA1        ((16 << 2)|BIT(1)|BIT(0))
#define FO_SD0_DA2        ((17 << 2)|BIT(1)|BIT(0))
#define FO_SD0_DA3        ((18 << 2)|BIT(1)|BIT(0))
#define FO_IIC0_SCL        ((19 << 2)|BIT(1)|BIT(0))
#define FO_IIC0_SDA        ((20 << 2)|BIT(1)|BIT(0))
#define FO_IIC1_SCL        ((21 << 2)|BIT(1)|BIT(0))
#define FO_IIC1_SDA        ((22 << 2)|BIT(1)|BIT(0))
#define FO_UART0_TX        ((23 << 2)|BIT(1)|BIT(0))
#define FO_UART1_TX        ((24 << 2)|BIT(1)|BIT(0))


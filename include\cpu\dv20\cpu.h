#ifndef  __CPU_H__
#define  __CPU_H__

#include "typedef.h"
#include "gpio.h"
#include "jiffies.h"
#include "printf.h"
#include "csfr.h"
#include "uart.h"
#include "clock.h"
#include "hwi.h"
#include "wdt.h"
#include "msg.h"
#include "my_malloc.h"
#include "dma_copy.h"
#include "delay.h"
#include "efuse.h"
#include "hw_timer.h"
#include "resfile.h"
#include "sfc.h"
#include "fs.h"
#include "park_det.h"
#include "ldo.h"
#include "usb/usb.h"
#include "usb/device/slave_uvc.h"
#include "usb/device/usb_stack.h"
#include "video.h"
#include "jpeg_abr.h"

static inline u8 get_chip_version(void)
{
    return (JL_ISP->CHIP_ID) & (0xf);
}
static inline u8 get_chip_id(void)
{
    return (JL_ISP->CHIP_ID >> 8) & (0xff);
}

void setup_arch();

void board_init();

/** 补光灯控制函数 **/
void set_board_led_light_io(u8 state);
/** LDO控制函数 **/
void set_board_ldo_power_init();

const char *get_board_camera_name(void);
extern const struct uvc_reso_info board_jpg_fmt;
#ifdef CONFIG_YUYV_ENABLE
extern const struct uvc_reso_info board_yuv_fmt;
#endif
#endif  /*CPU_H*/


#ifndef  __EFUSE_H__
#define  __EFUSE_H__

#include "typedef.h"

#define AT24C01     0    //128Byte
#define AT24C02     1    //256Byte
#define AT24C04     2    //512Byte
#define AT24C08     3    //1024Byte
#define AT24C16     4    //2048Byte
#define AT24C32     5    //4096Byte
#define AT24C64     6    //8192Byte
#define AT24C128    7    //16384Byte
#define AT24C256    8    //32768Byte
#define AT24C512    9    //65536Byte
#define AT24CTYPE   10   // Number of supported types

u8 is_chip_low_spec();



#endif  /*EFUSE_H*/


/**
 * @file gpio.h
 * @brief
 * <AUTHOR>
 * @version 1.0.0
 * @date 2018-10-11
 */
#ifndef  __GPIO_HW_H__
#define  __GPIO_HW_H__




#include "typedef.h"
enum {
    CH0_UT0_TX,
    CH0_UT1_TX,
    CH0_T0_PWM_OUT,
    CH0_T1_PWM_OUT,
    CH0_RTOSH_CLK,
    CH0_BTOSC_CLK,
    CH0_PLL_12M,
    CH0_UT2_TX,
    CH0_CH0_PWM_H,
    CH0_CH0_PWM_L,
    CH0_CH1_PWM_H,
    CH0_CH1_PWM_L,
    CH0_CH2_PWM_H,
    CH0_CH2_PWM_L,
    CH0_T2_PWM_OUT,
    CH0_T3_PWM_OUT,

    CH1_UT0_TX = 0x10,
    CH1_UT1_TX,
    CH1_T0_PWM_OUT,
    CH1_T1_PWM_OUT,
    CH1_RTOSL_CLK,
    CH1_BTOSC_CLK,
    CH1_PLL_24M,
    CH1_UT2_TX,
    CH1_CH0_PWM_H,
    CH1_CH0_PWM_L,
    CH1_CH1_PWM_H,
    CH1_CH1_PWM_L,
    CH1_CH2_PWM_H,
    CH1_CH2_PWM_L,
    CH1_T2_PWM_OUT,
    CH1_T3_PWM_OUT,

    CH2_UT0_RTS = 0x20,
    CH2_UT1_TX,
    CH2_T0_PWM_OUT,
    CH2_T1_PWM_OUT,
    CH2_RTOSL_CLK,
    CH2_BTOSC_CLK,
    CH2_PLL_24M,
    CH2_UT2_TX,
    CH2_CH0_PWM_H,
    CH2_CH0_PWM_L,
    CH2_CH1_PWM_H,
    CH2_CH1_PWM_L,
    CH2_CH2_PWM_H,
    CH2_CH2_PWM_L,
    CH2_T2_PWM_OUT,
    CH2_T3_PWM_OUT,
};


#define IO_GROUP_NUM 		16


#define IO_PORTA_00 				(IO_GROUP_NUM * 0 + 0)
#define IO_PORTA_01 				(IO_GROUP_NUM * 0 + 1)
#define IO_PORTA_02 				(IO_GROUP_NUM * 0 + 2)
#define IO_PORTA_03 				(IO_GROUP_NUM * 0 + 3)
#define IO_PORTA_04 				(IO_GROUP_NUM * 0 + 4)
#define IO_PORTA_05 				(IO_GROUP_NUM * 0 + 5)
#define IO_PORTA_06 				(IO_GROUP_NUM * 0 + 6)
#define IO_PORTA_07 				(IO_GROUP_NUM * 0 + 7)
#define IO_PORTA_08 				(IO_GROUP_NUM * 0 + 8)
#define IO_PORTA_09 				(IO_GROUP_NUM * 0 + 9)
#define IO_PORTA_10 				(IO_GROUP_NUM * 0 + 10)
#define IO_PORTA_11 				(IO_GROUP_NUM * 0 + 11)
#define IO_PORTA_12 				(IO_GROUP_NUM * 0 + 12)
#define IO_PORTA_13 				(IO_GROUP_NUM * 0 + 13)
#define IO_PORTA_14 				(IO_GROUP_NUM * 0 + 14)
// #define IO_PORTA_15 				(IO_GROUP_NUM * 0 + 15)

#define IO_PORTB_00 				(IO_GROUP_NUM * 1 + 0)
#define IO_PORTB_01 				(IO_GROUP_NUM * 1 + 1)
#define IO_PORTB_02 				(IO_GROUP_NUM * 1 + 2)
#define IO_PORTB_03 				(IO_GROUP_NUM * 1 + 3)
#define IO_PORTB_04 				(IO_GROUP_NUM * 1 + 4)
#define IO_PORTB_05 				(IO_GROUP_NUM * 1 + 5)
#define IO_PORTB_06 				(IO_GROUP_NUM * 1 + 6)
#define IO_PORTB_07 				(IO_GROUP_NUM * 1 + 7)
#define IO_PORTB_08 				(IO_GROUP_NUM * 1 + 8)
#define IO_PORTB_09 				(IO_GROUP_NUM * 1 + 9)
#define IO_PORTB_10 				(IO_GROUP_NUM * 1 + 10)

// #define IO_PORTC_00 				(IO_GROUP_NUM * 2 + 0)
// #define IO_PORTC_01 				(IO_GROUP_NUM * 2 + 1)
// #define IO_PORTC_02 				(IO_GROUP_NUM * 2 + 2)
// #define IO_PORTC_03 				(IO_GROUP_NUM * 2 + 3)
// #define IO_PORTC_04 				(IO_GROUP_NUM * 2 + 4)
// #define IO_PORTC_05 				(IO_GROUP_NUM * 2 + 5)
// #define IO_PORTC_06 				(IO_GROUP_NUM * 2 + 6)
// #define IO_PORTC_07 				(IO_GROUP_NUM * 2 + 7)

#define IO_PORTD_00 				(IO_GROUP_NUM * 3 + 0)
#define IO_PORTD_01 				(IO_GROUP_NUM * 3 + 1)
#define IO_PORTD_02 				(IO_GROUP_NUM * 3 + 2)
#define IO_PORTD_03 				(IO_GROUP_NUM * 3 + 3)
#define IO_PORTD_04 				(IO_GROUP_NUM * 3 + 4)
// #define IO_PORTD_05 				(IO_GROUP_NUM * 3 + 5)
// #define IO_PORTD_06 				(IO_GROUP_NUM * 3 + 6)
// #define IO_PORTD_07 				(IO_GROUP_NUM * 3 + 7)

// #define IO_PORTE_00 				(IO_GROUP_NUM * 4 + 0)
// #define IO_PORTE_01 				(IO_GROUP_NUM * 4 + 1)
// #define IO_PORTE_02 				(IO_GROUP_NUM * 4 + 2)
// #define IO_PORTE_03 				(IO_GROUP_NUM * 4 + 3)
// #define IO_PORTE_04 				(IO_GROUP_NUM * 4 + 4)
// #define IO_PORTE_05 				(IO_GROUP_NUM * 4 + 5)

#define IO_MAX_NUM 					(IO_PORTD_04+1)


#define USB_IO_OFFSET               0
#define IO_PORT_DP                  (IO_MAX_NUM + USB_IO_OFFSET)
#define IO_PORT_DM                  (IO_MAX_NUM + USB_IO_OFFSET + 1)


#define IO_PORT_MAX					(IO_PORT_DM + 1)

#define GPIOA                       (IO_GROUP_NUM * 0)
#define GPIOB                       (IO_GROUP_NUM * 1)
#define GPIOC                       (IO_GROUP_NUM * 2)
#define GPIOD                       (IO_GROUP_NUM * 3)
#define GPIOUSB                     (IO_MAX_NUM + USB_IO_OFFSET)

struct gpio_reg {
    volatile unsigned long out;
    volatile unsigned long in;
    volatile unsigned long dir;
    volatile unsigned long die;
    volatile unsigned long pu;
    volatile unsigned long pd;
    volatile unsigned long hd0;
    volatile unsigned long hd;
    volatile unsigned long dieh;
};


#define __gpio_mask(gpio) \
	BIT((gpio) % IO_GROUP_NUM)

extern const u32 gpio_regs[];

static struct gpio_reg *gpio2reg(u32 gpio)
{
    if (gpio > IO_MAX_NUM) {
        return NULL;
    }
    return (struct gpio_reg *)gpio_regs[gpio / IO_GROUP_NUM];
}

#define     ENABLE_USB_IO   0

u32 usb_set_direction(u32 gpio, u32 value);
u32 usb_output(u32 gpio, u32 value);
u32 usb_set_pull_up(u32 gpio, u32 value);
u32 usb_set_pull_down(u32 gpio, u32 value);
u32 usb_set_die(u32 gpio, u32 value);
u32 usb_set_dieh(u32 gpio, u32 value);
u32 usb_read(u32 gpio);
void usb_iomode(u32 enable);
u32 *gpio2crossbar_outreg(u32 gpio);
u32 gpio2crossbar_inport(u32 gpio);
#endif  /*GPIO_HW_H*/

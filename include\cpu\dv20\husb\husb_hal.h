#ifndef _HUSB_HAL_H_
#define _HUSB_HAL_H_
#include "typedef.h"

enum {
    USB0,
};
#define USB_MAX_HW_NUM      1

#define EP0_SETUP_LEN       0x40
#define USB_MAX_HW_EPNUM    5

/*****MUSB SFR BitMap******/
/*INTRUSB mode*/
#define INTRUSB_SUSPEND             BIT(0)
#define INTRUSB_RESUME              BIT(1)
#define INTRUSB_RESET_BABBLE        BIT(2)
#define INTRUSB_SOF                 BIT(3)
#define INTRUSB_CONNECT             BIT(4)
#define INTRUSB_DISCONNECT          BIT(5)
#define INTRUSB_SESS_REQ            BIT(6)
#define INTRUSB_VBUS_ERROR          BIT(7)

/*CSR0 peripheral mode*/
#define CSR0P_RxPktRdy           0x01
#define CSR0P_TxPktRdy           0x02
#define CSR0P_SentStall          0x04
#define CSR0P_DataEnd            0x08
#define CSR0P_SetupEnd           0x10
#define CSR0P_SendStall          0x20
#define CSR0P_ClrRxPktRdy        0x40
#define CSR0P_ClrSetupEnd        0x80


/*TXCSR1 peripheral mode*/
#define TXCSRP_TxPktRdy          0x01
#define TXCSRP_FIFONotEmpty      0x02
#define TXCSRP_UnderRun          0x04
#define TXCSRP_FlushFIFO         0x08
#define TXCSRP_SendStall         0x10
#define TXCSRP_SentStall         0x20
#define TXCSRP_ClrDataTog        0x40
#define TXCSRP_IncompTx          0x80
#define TXCSRP_DIR               (BIT(13))
#define TXCSRP_ISOCHRONOUS       (BIT(14))

/*RXCSR1 peripheral mode*/
#define RXCSRP_RxPktRdy          0x01
#define RXCSRP_FIFOFull          0x02
#define RXCSRP_OverRun           0x04
#define RXCSRP_DataError         0x08
#define RXCSRP_FlushFIFO         0x10
#define RXCSRP_SendStall         0x20
#define RXCSRP_SentStall         0x40
#define RXCSRP_ClrDataTog        0x80
#define RXCSRP_IncompRx          (BIT(8))
#define RXCSRP_ISOCHRONOUS       (BIT(14))

/*CSR0 host mode*/
#define CSR0H_RxPktRdy           0x01
#define CSR0H_TxPktRdy           0x02
#define CSR0H_RxStall            0x04
#define CSR0H_SetupPkt           0x08
#define CSR0H_Error              0x10
#define CSR0H_ReqPkt             0x20
#define CSR0H_StatusPkt          0x40
#define CSR0H_DISPING            (BIT(11))

/*TXCSR1 host mode*/
#define TXCSRH_TxPktRdy          0x01
#define TXCSRH_FIFONotEmpty      0x02
#define TXCSRH_Error             0x04
#define TXCSRH_FlushFIFO         0x08
#define TXCSRH_RxStall           0x20
#define TXCSRH_ClrDataTog        0x40
#define TXCSRH_NAK               0x80

/*RXCSR1 host mode*/
#define RXCSRH_RxPktRdy          0x01
#define RXCSRH_FIFOFull          0x02
#define RXCSRH_Error             0x04
#define RXCSRH_DataError         0x08
#define RXCSRH_FlushFIFO         0x10
#define RXCSRH_ReqPkt            0x20
#define RXCSRH_RxStall           0x40
#define RXCSRH_ClrDataTog        0x80
#define RXCSRH_IncompRx          BIT(8)
#define RXCSRH_PIDError          BIT(12)


//u32 usb_dev_con0(usb_dev usb_id);
void usb_sie_enable(usb_dev usb_id);
void usb_sie_disable(usb_dev id);
u32 usb_g_dev_status(usb_dev usb_id);
void usb_write_ep0(usb_dev usb_id, const u8 *ptr, u32 len);
void usb_read_ep0(usb_dev usb_id, u8 *ptr, u32 len);
u32 usb_get_dma_size(usb_dev usb_id, u32 ep);
void *usb_get_dma_taddr(usb_dev usb_id, u32 ep);
void usb_set_dma_taddr(usb_dev usb_id, u32 ep, void *ptr);
void usb_set_dma_dual_taddr(usb_dev usb_id, u32 ep, void *ptr);
void usb_set_dma_tsize(usb_dev usb_id, u32 ep, u32 size);
void *usb_get_dma_raddr(usb_dev usb_id, u32 ep);
void usb_set_dma_raddr(usb_dev usb_id, u32 ep, void *ptr);
void usb_set_dma_dual_raddr(usb_dev usb_id, u32 ep, void *ptr);
void usb_set_dma_rsize(usb_dev usb_id, u32 ep, u32 size);
u32 usb_read_csr0(usb_dev usb_id);
void usb_write_csr0(usb_dev usb_id, u32 csr0);
u16 usb_read_sofframe(usb_dev id);
void usb_ep0_ClrRxPktRdy(usb_dev usb_id);
void usb_ep0_TxPktEnd(usb_dev usb_id);
void usb_ep0_RxPktEnd(usb_dev usb_id);
void usb_ep0_Set_Stall(usb_dev usb_id);
void usb_ep0_Set_ignore(usb_dev id, u32 addr);
u32 usb_read_count0(usb_dev usb_id);
void usb_read_intre(usb_dev usb_id, u32 *const intr_usbe, u32 *const intr_txe, u32 *const intr_rxe);
void usb_read_intr(usb_dev usb_id, u32 *const intr_usb, u32 *const intr_tx, u32 *const intr_rx);
void usb_write_intr_usbe(usb_dev usb_id, u32 intr_usbe);
void usb_set_intr_txe(usb_dev usb_id, const u32 ep);
void usb_clr_intr_txe(usb_dev usb_id, const u32 ep);
void usb_set_intr_rxe(usb_dev usb_id, const u32 ep);
void usb_clr_intr_rxe(usb_dev usb_id, const u32 ep);
void usb_write_faddr(usb_dev usb_id, u32 addr);
void usb_write_txcsr(usb_dev usb_id, const u32 ep, u32 txcsr);
u32 usb_read_txcsr(usb_dev usb_id, const u32 ep);
void usb_write_rxcsr(usb_dev usb_id, const u32 ep, u32 rxcsr);
u32 usb_read_rxcsr(usb_dev usb_id, const u32 ep);
void usb_write_rxmaxp(usb_dev usb_id, const u32 ep, u32 value);
void usb_write_txmaxp(usb_dev usb_id, const u32 ep, u32 value);
void usb_write_rxtype(usb_dev usb_id, const u32 ep, u32 value);
void usb_write_txtype(usb_dev usb_id, const u32 ep, u32 value);
void usb_write_txinterval(usb_dev id, const u32 ep, u32 value);
void usb_write_rxinterval(usb_dev id, const u32 ep, u32 value);
u32 usb_read_rxcount(usb_dev usb_id, u32 ep);
u32 usb_g_ep_config(usb_dev usb_id, const u32 ep, u32 type, u32 ie, u8 *ptr, u32 dma_size);
u32 usb_g_ep_read64byte_fast(usb_dev usb_id, const u32 ep, u8 *ptr, u32 len);
u32 usb_g_ep_read(usb_dev usb_id, const u32 ep, u8 *ptr, u32 len, u32 block);
u32 usb_g_ep_write(usb_dev usb_id, u32 ep, const u8 *ptr, u32 len);
void usb_g_sie_init(usb_dev usb_id);
void usb_g_hold(usb_dev usb_id);
void usb_sie_close(usb_dev usb_id);
void usb_sie_close_all(void);
void usb_io_reset(usb_dev usb_id);
void usb_var_init(usb_dev usb_id, void *ptr);
void usb_var_reinit(usb_dev id);
void usb_var_release(usb_dev usb_id);
void usb_enable_ep(usb_dev usb_id, u32 eps);
void usb_disable_ep(usb_dev usb_id, u32 eps);

void usb_sofie_enable(usb_dev id);
void usb_sofie_disable(usb_dev id);
u32 usb_read_sofpnd(usb_dev id);
void usb_sof_clr_pnd(usb_dev id);
void usb_recover_io_status(usb_dev id);

u32 usb_get_jiffies();
void usb_mdelay(unsigned int ms);

/** @brief  usb slave interface
 * @{
 */
u32 usb_g_bulk_read64byte_fast(usb_dev usb_id, u32 ep, u8 *ptr, u32 len);
u32 usb_g_bulk_read(usb_dev usb_id, u32 ep, u8 *ptr, u32 len, u32 block);
u32 usb_g_bulk_write(usb_dev usb_id, u32 ep, const u8 *ptr, u32 len);
u32 usb_g_intr_read(usb_dev usb_id, u32 ep, u8 *ptr, u32 len, u32  block);
u32 usb_g_intr_write(usb_dev usb_id, u32 ep, const u8 *ptr, u32 len);
u32 usb_g_iso_read(usb_dev usb_id, u32 ep, u8 *ptr, u32 len, u32 block);
u32 usb_g_iso_write(usb_dev usb_id, u32 ep, const u8 *ptr, u32 len);
void usb_slave_init(usb_dev usb_id, u32 p_value);
/** @}*/

#endif

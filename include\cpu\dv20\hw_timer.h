#ifndef  __HW_TIMER_H__
#define  __HW_TIMER_H__

#include "jiffies.h"

struct sys_time {
    u16 year;
    u8 month;
    u8 day;
    u8 hour;
    u8 min;
    u8 sec;
};

void sys_tick_init(void);

void sys_timer_init(void *systimer_pools, int poolnum);
void sys_timer_schedule();
void sys_timer_del(int id);
int sys_timer_add(void *priv, void (*func)(void *priv), u32 msec);
int sys_timeout_add(void *priv, void (*func)(void *priv), u32 msec);


#endif  /*HW_TIMER_H*/

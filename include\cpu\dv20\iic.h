
#ifndef _IIC_HW_H_
#define _IIC_HW_H_

#include "typedef.h"
#include "device.h"
#include "ldo.h"

#define IIC_HW_NUM                  2

#define iic_enable(reg)             (reg->CON |= BIT(0))
#define iic_disable(reg)            (reg->CON &= ~BIT(0))
#define iic_kick_start(reg)         (reg->CON |= BIT(1))

#define iic_isel_direct(reg)        (reg->CON &= ~BIT(3))
#define iic_isel_filter(reg)        (reg->CON |= BIT(3))

#define iic_dir_out(reg)            (reg->CON &= ~BIT(4))
#define iic_dir_in(reg)             (reg->CON |= BIT(4))

#define iic_int_enable(reg)         (reg->CON |= BIT(8))
#define iic_int_disable(reg)        (reg->CON &= ~BIT(8))

#define iic_is_pnding(reg)          ((reg->CON & BIT(31)))
#define iic_pnding_clr(reg)         ((reg->CON |= BIT(7)))

#define iic_auto_ack(reg) 			(reg->CON &= ~BIT(11)) //该bit默认设0
#define iic_force_nack(reg) 		(reg->CON |= BIT(11))

#define iic_restart(reg) 			(reg->CON |= BIT(13)) //没有stop, 发start

#define iic_baud_reg(reg)           (reg->BAUD)
#define iic_buf_reg(reg)            (reg->BUF)
#define iic_normal_rate(reg)        (reg->DMA_NRATE)

//主机模式相关配置
#define iic_host_mode(reg)          				(reg->CON &= ~BIT(2))
#define iic_host_send_stop(reg)						(reg->CON |= BIT(5))
#define iic_host_is_stop_pending(reg)				((reg->CON & BIT(29)))
#define iic_host_send_is_ack(reg)   				((reg->CON & BIT(30)))
#define iic_host_receive_single_byte(reg) 			(reg->CON &= ~BIT(9))
#define iic_host_receive_continue_byte(reg) 		(reg->CON |= BIT(9))
#define iic_host_receive_continue_byte_stop(reg) 	(reg->CON |= BIT(10))
#define iic_host_nack_auto_stop(reg) 				(reg->CON |= BIT(14))
#define iic_host_read_kick_start(reg) 				(reg->CON |= BIT(16))

//从机模式相关配置
#define iic_slave_mode(reg)         			(reg->CON |= BIT(2))
#define iic_slave_dma_enable(reg)         		(reg->CON |= BIT(15))
#define iic_slave_dma_disable(reg)         		(reg->CON &= ~BIT(15))
#define iic_slave_dma_is_enable(reg)         	(reg->CON & BIT(15))
#define iic_slave_dma_buf(reg)         			(reg->CON1)
#define iic_slave_dma_big_endian(reg) 			(reg->CON |= BIT(17))
#define iic_slave_dma_little_endian(reg) 		(reg->CON &= ~BIT(17))
#define iic_slave_dma_buf_depth(reg)         	(reg->CON2)
#define iic_slave_dma_get_buf_len(reg)         	(((reg->CON2) & 0xFFFF) * sizeof(u32))
#define iic_slave_scl_pull_down_enble(reg) 		(reg->CON |= BIT(12))
#define iic_slave_scl_pull_down_release(reg) 	(reg->CON |= BIT(6))
#define iic_slave_is_required_send(reg) 		(reg->CON & BIT(28))
#define iic_slave_send_is_end(reg) 				(reg->CON & BIT(27))

///////////////////////////////////////////
#define iic_pnd(reg)             	(reg->CON & BIT(15))
#define iic_clr_pnd(reg)         	(reg->CON |= BIT(13))//清空普通pending
#define iic_ack_in(reg)             (reg->CON & BIT(11))
#define iic_pu_ack_out(reg)         (reg->CON |= BIT(10))
#define iic_pd_ack_out(reg)         (reg->CON &= ~BIT(10))
#define iic_clr_start(reg)          (reg->CON |= BIT(8))//清空起始位标志
#define iic_with_start_bit(reg)  			(reg->CON &= ~BIT(2))
#define iic_with_stop_bit(reg)  			(reg->CON &= ~BIT(3))


#define iic_clr_stop(iic)           (reg->CON |= BIT(12))//清空结束位标志
#define iic_add_start_bit(reg)      (reg->CON |= BIT(2))//加起始位
#define iic_add_end_bit(reg)        (reg->CON |= BIT(3))//加结束位
#define iic_set_ack(reg)         	(reg->CON &= ~BIT(10))

#define IIC_IOCTL_TX_START_BIT 				_IOW('I', 0,  0)
#define IIC_IOCTL_TX_WITH_START_BIT 		_IOW('I', 1,  1)
#define IIC_IOCTL_TX_STOP_BIT 				_IOW('I', 2,  1)
#define IIC_IOCTL_TX 						_IOW('I', 3,  8)
#define IIC_IOCTL_TX_WITH_STOP_BIT 			_IOW('I', 4,  9)
#define IIC_IOCTL_RX 						_IOR('I', 5,  8)
#define IIC_IOCTL_RX_WITH_STOP_BIT 			_IOR('I', 6,  9)
#define IIC_IOCTL_RX_WITH_NOACK 			_IOR('I', 7,  9)
#define IIC_IOCTL_RX_WITH_ACK 				_IOR('I', 8,  9)
#define IIC_IOCTL_SET_NORMAT_RATE 			_IOW('I', 9,  0)

#define IIC_IOCTL_START 					_IOW('I', 10,  0)
#define IIC_IOCTL_STOP 						_IOW('I', 11,  0)
#define IIC_IOCTL_CHECK_ACK 				_IOW('I', 12,  0)


#define IIC_SW_TYPE    0
#define IIC_HW_TYPE    1

struct software_iic {
    u32 clk_pin;
    u32 dat_pin;
    u32 sw_iic_delay;
};


struct hw_iic_config {
    u8 id: 1;
    u32 scl;
    u32 sda;
    u32 baudrate;
    u8 hdrive;
    u8 io_filter;
    u8 io_pu;
};

typedef struct hw_iic_config  *hw_iic_dev;

int hw_iic_init(hw_iic_dev iic);
void hw_iic_uninit(hw_iic_dev iic);
void hw_iic_suspend(hw_iic_dev iic);
void hw_iic_resume(hw_iic_dev iic);
void hw_iic_start(hw_iic_dev iic);
void hw_iic_stop(hw_iic_dev iic);
u8 hw_iic_tx(hw_iic_dev iic, u8 dat);
u8 hw_iic_tx_byte(hw_iic_dev iic, u8 byte, u8 start, u8 end, u8 restart);
u8 hw_iic_rx(hw_iic_dev iic, int *err);
u8 hw_iic_rx_byte(hw_iic_dev iic, u8 ack);
u8 hw_iic_rx_with_ack(hw_iic_dev iic, int *err);
u8 hw_iic_noack(hw_iic_dev iic);
int hw_iic_read_buf(hw_iic_dev iic, void *buf, int len);
int hw_iic_write_buf(hw_iic_dev iic, const void *buf, int len);
int hw_iic_set_baud(hw_iic_dev iic, u32 baud);


void sw_iic_start(struct software_iic *iic);
void sw_iic_stop(struct software_iic *iic);
u8 sw_iic_wait_ack(struct software_iic *iic);
void sw_iic_noack(struct software_iic *iic);
u8 sw_iic_rx(const struct software_iic *iic);
u8 sw_iic_tx(struct software_iic *iic, u8 byte, u8 start);

extern const struct device_operations hw_iic_dev_ops;
extern const struct device_operations sw_iic_dev_ops;
extern struct hw_iic_config _hw_iic;
extern struct software_iic _sw_iic;
#endif

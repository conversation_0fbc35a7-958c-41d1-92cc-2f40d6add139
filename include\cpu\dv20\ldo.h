#ifndef __LDO_H__
#define __LDO_H__

#include "typedef.h"

typedef enum __AVDD18 {
    AVDD18_1509 = 0x00,
    AVDD18_1609 = 0x01,
    AVDD18_1703 = 0x02,
    AVDD18_1809 = 0x03,
    AVDD18_1909 = 0x04,
    AVDD18_2009 = 0x05,
    AVDD18_2109 = 0x06,
    AVDD18_2209 = 0x07,
} AVDD18_LEV;


typedef enum __AVDD28 {
    AVDD28_2505 = 0x00,
    AVDD28_2605 = 0x01,
    AVDD28_2705 = 0x02,
    AVDD28_2805 = 0x03,
    AVDD28_2905 = 0x04,
    AVDD28_3005 = 0x05,
    AVDD28_3105 = 0x06,
    AVDD28_3205 = 0x07,
} AVDD28_LEV;

typedef enum __DVDD {
    DVDD_0903 = 0x00,
    DVDD_1004 = 0x01,
    DVDD_1104 = 0x02,
    DVDD_1154 = 0x03,
    DVDD_1204 = 0x04,
    DVDD_1254 = 0x05,
    DVDD_1305 = 0x06,
    DVDD_1405 = 0x07,
} DVDD_LEV;

typedef enum __IOVDD {
    IOVDD_301 = 0x00,
    IOVDD_302 = 0x01,
    IOVDD_303 = 0x02,
    IOVDD_304 = 0x03,
} IOVDD_LEV;

void avdd18_ctl(u8 lev, u8 avdd18en);
void avdd28_ctl(u8 lev, u8 avdd28en);
void dvdd_ctl(u8 lev);
void vbg_ctl(u8 lev);
void iovdd_ctl(u8 lev);

#endif

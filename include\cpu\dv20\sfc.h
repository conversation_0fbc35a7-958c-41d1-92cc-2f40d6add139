#ifndef __SFC_H__
#define __SFC_H__
#include "typedef.h"
#include "icache.h"

enum sfc_rmode {
    SFC_RMODE_2WIRE_NORMAL_READ = 0,
    SFC_RMODE_2WIRE_FAST_READ,
    SFC_RMODE_NORMAL_READ,
    SFC_RMODE_FAST_READ,
    SFC_RMODE_DUAL_FAST_READ_OUTPUT,
    SFC_RMODE_QUAD_FAST_READ_OUTPUT,
    SFC_RMODE_DUAL_FAST_READ_IO,
    SFC_RMODE_QUAD_FAST_READ_IO,
    SFC_RMODE_DUAL_NO_SEND_COMMAND_MODE,
    SFC_RMODE_QUAD_NO_SEND_COMMAND_MODE,
    SFC_RMODE_DUAL_SEND_COMMAND_MODE,
    SFC_RMODE_QUAD_SEND_COMMAND_MODE,
};


/*
 * function: sfc_init
 * parameter:
 *      base_addr - code base addr in flash
 *      baud      - spi clk = sfc_clk / (BAUD + 1)
 *      dummy     - dummy clk after address
 *      rmode     - sfc read mode
 *      bidir     - Full duplex mode
 *      enc_key   - sfc encrypt key(0xFFFF for unencrypt)
 *      uenc_addrl- sfc unencrypt minimum address
 *      uenc_addrh- sfc unencrypt maximum address
 */

void sfc_init(u32 base_addr, u8 baud, enum sfc_rmode mode, u32 enc_key, u32 uenc_addrl, u32 uenc_addrh);
void flushinv_cache(void *ptr, int len);
void flushinv_cache_all(void);
#endif

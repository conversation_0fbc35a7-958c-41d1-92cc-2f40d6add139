#ifndef  __WDT_H__
#define  __WDT_H__

#define WDT_1MS         0x00
#define WDT_2MS         0x01
#define WDT_4MS         0x02
#define WDT_8MS         0x03
#define WDT_16MS        0x04
#define WDT_32MS        0x05
#define WDT_64MS        0x06
#define WDT_128MS       0x07
#define WDT_256MS       0x08
#define WDT_512MS       0x09
#define WDT_1S          0x0A
#define WDT_2S          0x0B
#define WDT_4S          0x0C
#define WDT_8S          0x0D
#define WDT_16S         0x0E
#define WDT_32S         0x0F


void wdt_clr();

static inline void wdt_clear()
{
    JL_WDT->CON |= BIT(6);
}
static inline void wdt_init(u8 time)
{
    /*
     * 超时: 0-15 对应 {1ms, 2ms, 4ms, 8ms, ...512ms, 1s, 2s, 4s, 8s, 16s, 32s}
     */
    // JL_PWR->LDO_CON |= BIT(0);  //rc_en
    JL_CRC->REG = 0x6EA5;
    JL_WDT->CON = BIT(6) | BIT(4) | (time & 0xf);
    JL_CRC->REG = 0x0;
}
static inline void wdt_close()
{
    JL_CRC->REG = 0x6EA5;
    JL_WDT->CON &= ~BIT(4);
    JL_CRC->REG = 0x0;
}

void chip_reset();

#endif  /*WDT_H*/

#ifndef CHRDEV_H
#define CHRDEV_H


#include "typedef.h"
#include "list.h"
#include "atomic.h"
#include "ioctl.h"


struct dev_node;
struct device;


struct device_operations {
    bool (*online)(struct dev_node *node);
    int (*init)(struct dev_node *node, void *);
    int (*open)(struct dev_node *node, struct device **device, void *arg);
    int (*read)(struct device *device, void *buf, u32 len, u32);
    int (*write)(struct device *device, void *buf, u32 len, u32);
    int (*bulk_read)(struct device *device, void *buf, u32 len, u32);
    int (*bulk_write)(struct device *device, void *buf, u32 len, u32);
    int (*seek)(struct device *device, u32 offset, int orig);
    int (*ioctl)(struct device *device, u32 cmd, u32 arg);
    int (*close)(struct device *device);
    int (*uninit)(struct dev_node *node);
};

struct dev_node {
    const char *name;
    const struct device_operations *ops;
    void *priv_data;
};


struct device {
    atomic_t ref;
    void *private_data;
    const struct device_operations *ops;
    void *driver_data;
    // void *platform_data;
};

#define IOCTL_SET_IRQ_NUM 				1
#define IOCTL_SET_PRIORITY 				2
#define IOCTL_SET_DATA_WIDTH 			3
#define IOCTL_SET_SPEED 				4
#define IOCTL_SET_DETECT_MODE 			5
#define IOCTL_SET_DETECT_FUNC 			6
#define IOCTL_SET_DETECT_TIME_INTERVAL  7
#define IOCTL_SET_PORT 					8
#define IOCTL_SET_PORT_FUNC 			9
#define IOCTL_SET_CS_PORT_FUNC 		 	10
#define IOCTL_SET_READ_MODE 			11
#define IOCTL_SET_WRITE_MODE 		    12
#define IOCTL_SET_WRITE_PROTECT 	    13
#define IOCTL_SET_START_BIT 			14
#define IOCTL_SET_STOP_BIT 				15
#define IOCTL_FLUSH 					16
#define IOCTL_REGISTER_IRQ_HANDLER      17
#define IOCTL_UNREGISTER_IRQ_HANDLER    18
#define IOCTL_GET_SYS_TIME              19
#define IOCTL_SET_SYS_TIME              20
#define IOCTL_GET_ALARM                 21
#define IOCTL_SET_ALARM                 22
#define IOCTL_SET_CAP_LOWSPEED_CARD	    23
#define IOCTL_SET_VDD50_EN              30
#define IOCTL_GET_WEEKDAY             	32
#define IOCTL_CLR_READ_MODE             33
#define IOCTL_SET_READ_CRC              34
#define IOCTL_GET_READ_CRC              35
#define IOCTL_GET_VOLUME                36
#define IOCTL_SET_VOLUME                37
#define IOCTL_SET_ASYNC_MODE            38
#define IOCTL_CMD_RESUME                39
#define IOCTL_CMD_SUSPEND               40
#define IOCTL_SET_ENCKEY				41

#define IOCTL_GET_ID 					100
#define IOCTL_GET_SECTOR_SIZE			101
#define IOCTL_GET_BLOCK_SIZE			102
#define IOCTL_GET_CAPACITY 				103
#define IOCTL_GET_WIDTH 				104
#define IOCTL_GET_HEIGHT				105
#define IOCTL_GET_BLOCK_NUMBER          106
#define IOCTL_CHECK_WRITE_PROTECT       107
#define IOCTL_GET_STATUS                108
#define IOCTL_GET_TYPE                  109


#define IOCTL_ERASE_SECTOR 				200
#define IOCTL_ERASE_BLOCK 				201
#define IOCTL_ERASE_CHIP 				202
#define IOCTL_ERASE_PAGE 				203



#define REGISTER_DEVICES(node) \
    const struct dev_node node[] sec_used(.device)


int devices_init();
int _devices_init();

int dev_init(char *name, void *data);

int dev_uninit(char *name);

bool dev_online(const char *name);

void *dev_open(const char *name, void *arg);
void *_dev_open(const char *name, void *arg);


int dev_read(void *device, void *buf, u32 len);


int dev_write(void *device, void *buf, u32 len);


int dev_seek(void *device, u32 offset, int orig);


int dev_ioctl(void *device, int cmd, u32 arg);


int dev_close(void *device);


int dev_bulk_read(void *_device, void *buf, u32 offset, u32 len);

int dev_bulk_write(void *_device, void *buf, u32 offset, u32 len);

#endif



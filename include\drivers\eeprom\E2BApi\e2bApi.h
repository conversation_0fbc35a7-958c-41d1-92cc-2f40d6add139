#ifndef _E2BAPI_H
#define  _E2BAPI_H

#include "my_malloc.h"

#define _TAGHEAD_			0xfdf0
#define _SENNAME_			0xfdf1
#define _SENRSTPWDNSTATUS_	0xfdf2
#define _SENRSTDELAY_		0xfdf3
#define _YUVFORMAT_			0xfdf4
#define _SENOUTSIZE_		0xfdf5
#define _IICSET_			0xfdf6
#define _SENREGSETTING_		0xfdf7
#define _SENBUSSETTING		0xfdf8
#define _USBPACKSIZE_		0xfdf9
#define _LED_				0xfdfa
#define _JPEGBITS_			0xfdfb
#define _UARTDEBUG_			0xfdfc
#define _USBUSERDEVDESC_	0xfdfd
#define _USBUSERCONFDESC_	0xfdfe
#define _USBUSERDESC_	    0xfdff
#define _JPEGBITSCTRL_		0xfde0
#define _SENEXREGSET_		0xfde1
#define _JPEGYUVFMT_		0xfde2
#define _PARKCMD_	    	0xfde3
#define _IMGMIRROR_	    	0xfde4
#define _USBDYTRIM_         0xfde5
#define _USBTRANMODE_       0xfde6
#define _JPEGDATASEG_       0xfde7
#define _PARKINGCLOSECMD_   0xfde9
#define _PARKINGSETUPCMD_   0xfdea
#define _JPGABRMODE_        0xfdeb
#define _SENSORLDOCFG_      0xfded

typedef struct {
    u16 flag;
    u16 len;
} sTipHeader;

s32 OpenE2b();
s32 ReadByTagindex(u16 tagindex, u8 *buf, int len);
s32 ReadByOffset(u32 addr, u8 *buf, int len);
s32 GetLenByTagindex(u16 tagindex);

u8 get_eeprom_led_on();
u8 get_eeprom_jpg_abr_en();
u8 get_eeprom_jpg_abr_mode();
u8 get_eeprom_jpg_dri();
u8 get_eeprom_jpg_yuv_format();
u16 get_eeprom_jpg_abr_level();
u8 get_eeprom_image_mirror();
u16 get_eeprom_usb_packet_size();
u8 *get_eeprom_usb_dev_desc();
u8 *get_eeprom_usb_config_desc();
u8 *get_eeprom_usb_string_desc();
u8 *get_eeprom_usb_user_setup_packet();
u32 get_eeprom_park_cmd();
u32 get_eeprom_unpark_cmd();
#endif

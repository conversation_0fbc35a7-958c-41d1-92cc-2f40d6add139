#ifndef __SPIFLASH_H__
#define __SPIFLASH_H__
#include "typedef.h"
#include "spi.h"
#include "list.h"

enum spiflash_read_mode {
    FAST_READ_OUTPUT_MODE,
    FAST_READ_IO_MODE,
    FAST_READ_IO_CONTINUOUS_READ_MODE,
};

struct sf_info {
    u32 id;
    u16 page_size;     //byte
    u16 block_size;   //KByte
    u32 chip_size;   //KByte
    u8  uid[16];
};


struct spiflash {
    void *device;
    struct sf_info info;
    u8 inited;
    u8 mode;
    u8 read_cmd_mode;
    u8 write_cmd_mode;
    u8 continuous_read_mode;
};
typedef struct _RM_SPI {
    void *dev_priv_data;
    u8 isDec;
} RM_SPI;




extern int spiflash_init(enum hw_spi_io_mode iomode, u8 baud, enum spiflash_read_mode mode);
extern int spiflash_open();
extern int spiflash_write(void *buf, u32 offset, u32 len);
extern int spiflash_read(void *buf, u32 offset, u32 len);
extern u32 spiflash_ioctl_get_id(u32 arg);
extern u32 spiflash_ioctl_get_sector_size(u32 arg);
extern u32 spiflash_ioctl_get_block_size(u32 arg);
extern u32 spiflash_ioctl_get_capacity(u32 arg);
extern u32 spiflash_ioctl_erase_sector(u32 arg);
extern u32 spiflash_ioctl_erase_block(u32 arg);
extern u32 spiflash_ioctl_erase_chip(u32 arg);
extern u32 spiflash_ioctl_set_write_protect(u32 arg);
extern u32 spiflash_ioctl_set_read_crc(u32 arg);
extern u32 spiflash_ioctl_get_read_crc(u32 arg);
extern u32 spiflash_ioctl_set_enckey(u32 arg);
extern int spiflash_close();

extern u32 nor_flash_init(struct spi_config_data *pdata);
extern u32 norflash_read(RM_SPI *pReadCmd, void *buf, u32 addr, u32 len);
extern void flash_poweron(u32 port, u32 delay);
extern void flash_poweroff(u32 port);



#endif

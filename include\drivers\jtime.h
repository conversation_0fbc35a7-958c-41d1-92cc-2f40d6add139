#ifndef  __JTIME_H__
#define  __JTIME_H__

#include "typedef.h"
struct Jtime {
    u32 start_us;
    u32 end_us;
};

void Jtime_init(u32 src);
u32 Jtime_updata_jiffies();

u32 Jtime_create(struct Jtime *t, u32 timeout); //10us

u32 Jtime_elapsed(const struct Jtime *t);
u32 Jtime_stop(const struct Jtime *t);
u32 Jtime_pause(const struct Jtime *t);
u32 Jtime_timeout(const struct Jtime *t);
#endif  /*JTIME_H*/

#ifndef __KEY_DRIVER_H__
#define __KEY_DRIVER_H__

#include "typedef.h"
#include "device.h"

enum KEY_VALUE {
    KEY_NONE = 0,
    KEY_POWER,
    KEY_UP,
    KEY_DOWN,
    KEY_LEFT,
    KEY_RIGHT,
    KEY_OK,
    KEY_MODE,
    KEY_MENU,
};

struct key_driver_ops {
    int (*init)(void *key, void *arg);
    u16(*get_value)(void *key);
};

struct key_driver {
    u8 prev_value;
    u8 prev_event;
    u8 last_key;
    u8 filter_cnt;
    u8 base_cnt;
    u8 long_cnt;
    u8 hold_cnt;
    u8 press_cnt;
    u32 scan_time;
    const char *name;
    const struct key_driver_ops *ops;
};

extern struct key_driver key_driver_begin[];
extern struct key_driver key_driver_end[];

extern const struct device_operations key_dev_ops;

#define REGISTER_KEY_DRIVER(driver) \
	static struct key_driver driver sec_used(.key_driver)


#define list_for_each_key_dirver(p) \
	for (p=key_driver_begin; p<key_driver_end; p++)




#endif

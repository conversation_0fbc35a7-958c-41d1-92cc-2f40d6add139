#ifndef __USB_H__
#define __USB_H__

#include "typedef.h"

#ifndef min
#define min(a,b) ((a)<(b) ? (a) : (b))
#endif
#define USB_DIR_OUT			0		/* to device */
#define USB_DIR_IN			0x80		/* to host */

#define ___ntohl(X)     ((((u16)(X) & 0xff00) >> 8) |(((u16)(X) & 0x00ff) << 8))

#define ___ntohs(X)     ((((u32)(X) & 0xff000000) >> 24) | \
                        (((u32)(X) & 0x00ff0000) >> 8) | \
                        (((u32)(X) & 0x0000ff00) << 8) | \
                        (((u32)(X) & 0x000000ff) << 24))

#define cpu_to_be16(v16) ___ntohl(v16)
#define cpu_to_be32(v32) ___ntohs(v32)

#define be16_to_cpu(v16) cpu_to_be16(v16)
#define be32_to_cpu(v32) cpu_to_be32(v32)
#define __le16_to_cpu(v16)     (v16)
#define __le32_to_cpu(v32)     (v32)


#define cpu_to_le16(v16) (v16)
#define cpu_to_le32(v32) (v32)

#define le16_to_cpu(v16) cpu_to_le16(v16)
#define le32_to_cpu(v32) cpu_to_le32(v32)

#define LOWORD(l)           ((u16)(l))
#define HIWORD(l)           ((u16)(((u32)(l) >> 16) & 0xFFFF))

#define LOBYTE(w)           ((u8)(w))
#define HIBYTE(w)           ((u8)(((u16)(w) >> 8) & 0xFF))

#define DW1BYTE(dw)         (LOBYTE(LOWORD(dw)))
#define DW2BYTE(dw)         (HIBYTE(LOWORD(dw)))
#define DW3BYTE(dw)         (LOBYTE(HIWORD(dw)))
#define DW4BYTE(dw)         (HIBYTE(HIWORD(dw)))

///USB Slave 控制传输各阶段
#define USB_EP0_STAGE_SETUP       0
#define USB_EP0_STAGE_IN          1
#define USB_EP0_STAGE_OUT         2
#define USB_EP0_SET_STALL         3
#define USB_EP0_IGNORE            4
#define USB_EP0_STAGE_NAK         5

#ifndef _WEAK_
#define _WEAK_	        __attribute__((weak))
#endif
#ifndef SEC
#define SEC(x)          __attribute__((section(#x)))
#endif

struct usb_ep_addr_t {
    u32 ep0_addr;
    u32 ep_usage;
    u32 ep_taddr[6];
    u32 ep_dual_taddr[6];
    u32 ep_raddr[6];
    u32 ep_dual_raddr[6];
    u32 ep_tsize[6];
    u32 ep_rsize[6];
} __attribute__((aligned(4)));

typedef const u8 usb_dev;

#define USB_EP_DUAL_BUF_EN      0  //是否使能端点的double buffer，不使能可以省内存空间，但是传输速度会变慢

#ifndef USB_DBG_LEV
#define USB_DBG_LEV     0
#endif

#if USB_DBG_LEV >= 1
#define usb_log_error(str, ...) printf(str"\n", ##__VA_ARGS__)
#else
#define usb_log_error(str, ...)
#endif
#if USB_DBG_LEV >= 2
#define usb_log_warn(str, ...) printf(str"\n", ##__VA_ARGS__)
#else
#define usb_log_warn(str, ...)
#endif
#if USB_DBG_LEV >= 3
#define usb_log_debug(str, ...) printf(str"\n", ##__VA_ARGS__)
#define usb_put_buf(buf, len) printf_buf(buf, len)
#else
#define usb_log_debug(str, ...)
#define usb_put_buf(buf, len)
#endif
#if USB_DBG_LEV >= 4
#define usb_log_info(str, ...) printf(str"\n", ##__VA_ARGS__)
#else
#define usb_log_info(str, ...)
#endif

#if defined(USB_HW_11)
#include "fusb_hal.h"
#elif defined(USB_HW_20)
#include "husb_hal.h"
#else
#error undefined usb hardware type
#endif

#endif  //__USB_H__

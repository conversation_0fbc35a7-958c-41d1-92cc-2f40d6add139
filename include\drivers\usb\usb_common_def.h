#ifndef __USB_COMMON_DEFINE_H__
#define __USB_COMMON_DEFINE_H__

///<<<注意此文件不要放函数声明, 只允许宏定义, 并且差异化定义可以根据需求在对应板卡中重新定义, 除非新增，否则不要直接修改这里
///<<<注意此文件不要放函数声明, 只允许宏定义, 并且差异化定义可以根据需求在对应板卡中重新定义, 除非新增，否则不要直接修改这里
///<<<注意此文件不要放函数声明, 只允许宏定义, 并且差异化定义可以根据需求在对应板卡中重新定义, 除非新增，否则不要直接修改这里
//
/**************************************************************************/
/*
               CLASS  BITMAP
    7   |   6   |   5   |   4   |   3   |   2   |   1   |   0
                                   HID    AUDIO  SPEAKER   Mass Storage
*/
/**************************************************************************/
#define     MASSSTORAGE_CLASS   0x00000001
#define     SPEAKER_CLASS       0x00000002
#define     MIC_CLASS           0x00000004
#define     HID_CLASS           0x00000008
#define     CDC_CLASS           0x00000010
#define     VIDEO_CLASS         0x00000020

#define     AUDIO_CLASS         (SPEAKER_CLASS|MIC_CLASS)


#define     USB_ROOT2   0

/// board文件没有定义的宏,在这里定义,防止编译报warning
#ifndef TCFG_PC_ENABLE
#define TCFG_PC_ENABLE  0
#endif
#ifndef TCFG_CHARGE_ENABLE
#define TCFG_CHARGE_ENABLE     0
#endif

/********************************/
#ifndef USB_HOST_ENABLE
#define USB_HOST_ENABLE                     0
#endif

#if TCFG_CHARGE_ENABLE
#define TCFG_OTG_MODE_CHARGE                (OTG_CHARGE_MODE)
#else
#define TCFG_OTG_MODE_CHARGE                0
#endif

#if (TCFG_PC_ENABLE)
#define TCFG_PC_UPDATE                      1
#define TCFG_OTG_MODE_SLAVE                 (OTG_SLAVE_MODE)
#else
#define TCFG_PC_UPDATE                      0
#define TCFG_OTG_MODE_SLAVE                 0
#endif

#if (USB_HOST_ENABLE)
#define TCFG_OTG_MODE_HOST                  (OTG_HOST_MODE)
#else
#define TCFG_OTG_MODE_HOST                  0
#endif

#if TCFG_PC_ENABLE
#define TCFG_USB_SLAVE_ENABLE               1
#if (USB_DEVICE_CLASS_CONFIG & MASSSTORAGE_CLASS)
#define TCFG_USB_SLAVE_MSD_ENABLE           1
#else
#define TCFG_USB_SLAVE_MSD_ENABLE           0
#endif

#if (USB_DEVICE_CLASS_CONFIG & VIDEO_CLASS)
#define TCFG_USB_SLAVE_UVC_ENABLE           1
#else
#define TCFG_USB_SLAVE_UVC_ENABLE           0
#endif

#if (USB_DEVICE_CLASS_CONFIG & MIC_CLASS)
#define TCFG_USB_SLAVE_AUDIO_MIC_ENABLE         1
#else
#define TCFG_USB_SLAVE_AUDIO_MIC_ENABLE         0
#endif

#if (USB_DEVICE_CLASS_CONFIG & SPEAKER_CLASS)
#define TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE         1
#else
#define TCFG_USB_SLAVE_AUDIO_SPEAKER_ENABLE         0
#endif


#else  /* TCFG_PC_ENABLE == 0*/
#define TCFG_USB_SLAVE_ENABLE               0
#define TCFG_USB_SLAVE_MSD_ENABLE           0
#define TCFG_USB_SLAVE_UVC_ENABLE           0
#endif

#define TCFG_OTG_SLAVE_ONLINE_CNT           2
#define TCFG_OTG_SLAVE_OFFLINE_CNT          2

#define TCFG_OTG_HOST_ONLINE_CNT            2
#define TCFG_OTG_HOST_OFFLINE_CNT           3

#ifndef TCFG_OTG_MODE
#define TCFG_OTG_MODE                       (TCFG_OTG_MODE_HOST|TCFG_OTG_MODE_SLAVE|TCFG_OTG_MODE_CHARGE)
#endif

#define TCFG_OTG_DET_INTERVAL               50


#endif

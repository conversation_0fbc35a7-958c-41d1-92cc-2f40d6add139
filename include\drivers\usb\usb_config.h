#ifndef  __USB_CONFIG_H__
#define  __USB_CONFIG_H__

#include "typedef.h"
#include "usb/usb.h"
#include "usb/device/usb_stack.h"

void usb_g_isr_reg(usb_dev usb_id, u8 priority, u8 cpu_id);



void usb_sof_isr_reg(usb_dev usb_id, u8 priority, u8 cpu_id);
void *usb_get_ep_buffer(usb_dev usb_id, u32 class, u8 dir);
u32 usb_config(usb_dev usb_id);
u32 usb_release(usb_dev usb_id);

void usb_ex_device_init(usb_dev usb_id);
#endif  /*USB_CONFIG_H*/

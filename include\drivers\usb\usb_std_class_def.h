#ifndef __USB_STD_CLASS_DEF_H__
#define __USB_STD_CLASS_DEF_H__
#include "app_config.h"

#ifndef USB_MALLOC_ENABLE
#define USB_MALLOC_ENABLE           0
#endif
#ifndef USB_HOST_ASYNC
#define USB_HOST_ASYNC              1
#endif
#ifndef USB_H_MALLOC_ENABLE
#define USB_H_MALLOC_ENABLE         1
#endif
#ifndef USB_DEVICE_CLASS_CONFIG
#define USB_DEVICE_CLASS_CONFIG     (MASSSTORAGE_CLASS)
#endif


///////////MassStorage Class
#ifndef MSD_BULK_EP_OUT
#define MSD_BULK_EP_OUT             1
#endif
#ifndef MSD_BULK_EP_IN
#define MSD_BULK_EP_IN              1
#endif
// #if defined(FUSB_MODE) && FUSB_MODE
// #ifndef MAXP_SIZE_BULKOUT
// #define MAXP_SIZE_BULKOUT           64
// #endif
// #ifndef MAXP_SIZE_BULKIN
// #define MAXP_SIZE_BULKIN            64
// #endif
// #else
// #ifndef MAXP_SIZE_BULKOUT
// #define MAXP_SIZE_BULKOUT           512
// #endif
// #ifndef MAXP_SIZE_BULKIN
// #define MAXP_SIZE_BULKIN            512
// #endif
// #endif
#ifndef MAXP_SIZE_BULKOUT
#define MAXP_SIZE_BULKOUT           64
#endif
#ifndef MAXP_SIZE_BULKIN
#define MAXP_SIZE_BULKIN            64
#endif

#ifndef MSD_STR_INDEX
#define MSD_STR_INDEX               4
#endif


///////////USB Video Class
#ifndef UVC_ISO_MODE
    #ifdef CONFIG_ENABLE_BULK_MODE
    #define UVC_ISO_MODE                0
    #else
    #define UVC_ISO_MODE                1
    #endif
#endif
#ifndef UVC_STREAM_EP_IN
#define UVC_STREAM_EP_IN            4
#endif
#ifndef VIDEO_STATUS_EP_IN
#define VIDEO_STATUS_EP_IN          5
#endif
#ifndef VIDEO_STR_INDEX
#define VIDEO_STR_INDEX             5
#endif

// #if !defined(UVC_PKT_SPILT) || !defined(UVC_FIFO_TXMAXP)
// #if defined(FUSB_MODE) && FUSB_MODE
// #if UVC_ISO_MODE
// #define UVC_PKT_SPILT               1
// #define UVC_FIFO_TXMAXP             768//1023
// #else
// #define UVC_PKT_SPILT               1
// #define UVC_FIFO_TXMAXP             512
// #endif
// #else  //HUSB_MODE
// #if UVC_ISO_MODE
// #define UVC_PKT_SPILT               1//2//3
// #define UVC_FIFO_TXMAXP             1024
// #else
// #define UVC_PKT_SPILT               1
// #define UVC_FIFO_TXMAXP             512
// #endif  //UVC_ISO_MODE
// #endif  //defined(FUSB_MODE) && FUSB_MODE
// #endif  //!defined(UVC_PKT_SPILT) || !defined(UVC_FIFO_TXMAXP)
#if UVC_ISO_MODE
#define UVC_PKT_SPILT               1

#if (CONFIG_COMPATIBLE_YIKANG_VM0)
// 兼容移康VM0 最大只能为512，这个时候不能开回声消除，开了回声消除出不了图
#define UVC_FIFO_TXMAXP             512
#else
#if defined(CONFIG_PRODUCT_ZB01_QIXI)
#define UVC_FIFO_TXMAXP             1024
#else
#define UVC_FIFO_TXMAXP             1023
#endif
#endif
#ifdef HUSB_MODE   //USB2.0
#if !defined (CONFIG_PRODUCT_SU7PRO_ISOC_WITH_7256)
#undef UVC_PKT_SPILT
#define UVC_PKT_SPILT               3
#endif
#endif
#else //bulk mode
#define UVC_PKT_SPILT               1
#define UVC_FIFO_TXMAXP             512
#endif


#ifndef VIDEO_STATUS_TXMAXP
#define VIDEO_STATUS_TXMAXP         16
#endif



/////////////Audio Class
#ifndef UAC_ISO_INTERVAL
#ifndef HUSB_MODE//defined(FUSB_MODE) && FUSB_MODE
#ifndef UAC_ISO_INTERVAL
#define UAC_ISO_INTERVAL           1
#endif
#else  //HUSB Mode
#ifndef UAC_ISO_INTERVAL
#define UAC_ISO_INTERVAL           4
#endif
#endif
#endif
//speaker class
#ifndef SPK_SamplingFrequency
#define SPK_SamplingFrequency       1
#endif
#if SPK_SamplingFrequency   == 1
#ifndef SPK_AUDIO_RATE
#ifdef CONFIG_NLPFIX_ENABLE
#define SPK_AUDIO_RATE              8000
#else
#ifdef AUDIO_SPK_16K
#define SPK_AUDIO_RATE              16000
#else
#define SPK_AUDIO_RATE              8000 ////48000 20230329
#endif
#endif
#endif
#else
#define SPK_AUDIO_RATE              8000
#define SPK_AUDIO_RATE1             16000
#endif

#ifndef SPK_AUDIO_RES
#define SPK_AUDIO_RES               16
#endif
#ifndef SPK_CHANNEL
#define SPK_CHANNEL                 1
#endif
#ifndef SPK_FRAME_LEN
#define SPK_FRAME_LEN               (((SPK_AUDIO_RATE) * SPK_AUDIO_RES / 8 * SPK_CHANNEL)/1000+4)
#endif
#ifndef SPK_PCM_Type
#define SPK_PCM_Type                (SPK_AUDIO_RES >> 4)                // 0=8 ,1=16
#endif
#ifndef SPK_AUDIO_TYPE
#define SPK_AUDIO_TYPE              (0x02 - SPK_PCM_Type)           // TYPE1_PCM16
#endif
#ifndef SPK_ISO_EP_OUT
#define SPK_ISO_EP_OUT              2
#endif
#ifndef SPEAKER_STR_INDEX
#define SPEAKER_STR_INDEX           7
#endif
#ifndef SPK_INPUT_TERMINAL_ID
#define SPK_INPUT_TERMINAL_ID       1
#endif
#ifndef SPK_FEATURE_UNIT_ID
#define SPK_FEATURE_UNIT_ID         2
#endif
#ifndef SPK_OUTPUT_TERMINAL_ID
#define SPK_OUTPUT_TERMINAL_ID      3
#endif

/////////////Microphone Class
#ifndef MIC_SamplingFrequency
#ifdef CONFIG_NLPFIX_ENABLE
#define MIC_SamplingFrequency       1
#else
#define MIC_SamplingFrequency       4
#endif
#endif

#if MIC_SamplingFrequency   == 1
#ifdef CONFIG_NLPFIX_ENABLE
#define MIC_AUDIO_RATE              8000 ////by frank 20221221 default is 16000  //8000
#else
#define MIC_AUDIO_RATE              8000 ////48000  20230329
#endif
#else
#ifdef  AUDIO_MIC_16K
#define MIC_AUDIO_RATE              16000
#else
#define MIC_AUDIO_RATE              8000
#endif
#define MIC_AUDIO_RATE_1            44100
#define MIC_AUDIO_RATE_2            16000
#define MIC_AUDIO_RATE_4            8000
#endif

#ifndef MIC_AUDIO_RES
#define MIC_AUDIO_RES               16
#endif
#ifndef MIC_CHANNEL
#define MIC_CHANNEL                 1
#endif
#ifndef MIC_FRAME_LEN
#define MIC_FRAME_LEN               ((MIC_AUDIO_RATE * MIC_AUDIO_RES / 8 * MIC_CHANNEL)/1000)
#endif
#ifndef MIC_PCM_TYPE
#define MIC_PCM_TYPE                (MIC_AUDIO_RES >> 4)                // 0=8 ,1=16
#endif
#ifndef MIC_AUDIO_TYPE
#define MIC_AUDIO_TYPE              (0x02 - MIC_PCM_TYPE)
#endif
#ifndef MIC_ISO_EP_IN
#define MIC_ISO_EP_IN               2
#endif
#ifndef MIC_STR_INDEX
#define MIC_STR_INDEX               6
#endif
#ifndef MIC_INPUT_TERMINAL_ID
#define MIC_INPUT_TERMINAL_ID       4
#endif
#ifndef MIC_FEATURE_UNIT_ID
#define MIC_FEATURE_UNIT_ID         5
#endif
#ifndef MIC_OUTPUT_TERMINAL_ID
#define MIC_OUTPUT_TERMINAL_ID      6
#endif
#ifndef MIC_SELECTOR_UNIT_ID
#define MIC_SELECTOR_UNIT_ID        7
#endif
#endif

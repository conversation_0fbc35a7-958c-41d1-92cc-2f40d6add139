
#ifndef __USB_AUDIO_H__
#define __USB_AUDIO_H__

#include "device.h"
#include "usb/device/usb_stack.h"
#include "usb/device/msd.h"
#include "usb/device/slave_uvc.h"
#include "usb/device/uac_stream.h"
#include "printf.h"
#include "hw_timer.h"
#include "dma_copy.h"
#include "msg.h"
#include "fs.h"
#include "audio.h"

// return current play audio frame index
typedef void (*usb_audio_spk_play_cb_t)(u32 frame_idx);
typedef void (*usb_audio_spk_close_cb_t)(void);
int usb_audio_register_spk_callback(usb_audio_spk_play_cb_t play_cb, usb_audio_spk_close_cb_t close_cb);
void usb_audio_reset_audio_frame_cnt(void);
void usb_audio_clear_audio_data(void);

void usb_audio_process(struct sys_msg *msg);

int usb_audio_mic_near_buf_read(void *buf, u32 len);
int usb_audio_dac_aec_farbuf_read(u8 *buf, u32 len);
void usb_audio_dac_aec_farbuf_clear(void);
int usb_audio_mic_aec_buf_write(void *buf, u32 len);
void usb_audio_mic_near_buf_clear(void);
void usb_audio_mic_restart(void);
#endif

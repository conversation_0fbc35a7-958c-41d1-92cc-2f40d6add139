#ifndef JIFFIES_H
#define JIFFIES_H


#include "dv20.h"
/* timer interface */
/* Parameters used to convert the timespec values: */
#define HZ				100L
#define MSEC_PER_SEC	1000L
#define USEC_PER_MSEC	1000L
#define NSEC_PER_USEC	1000L
#define NSEC_PER_MSEC	1000000L
#define USEC_PER_SEC	1000000L
#define NSEC_PER_SEC	1000000000L
#define FSEC_PER_SEC	1000000000000000LL


#define time_before(tck1,tck2)					((tck1) < (tck2))
#define time_after(a,b)							time_before(b,a)

// extern unsigned long jiffies;


static inline unsigned long get_jiffies(void)
{
    unsigned long jiff ;
    unsigned char chip_ver = (JL_ISP->CHIP_ID) & (0xf) ;
    if (chip_ver == 0) {
        //A版
        jiff = *(unsigned long *)(0x01f5cc);
    } else if (chip_ver == 1) {
        //B版
        jiff = *(unsigned long *)(0x01f2c4);
    }

    return jiff;
}

#define jiffies_to_msecs(j) ((j) * 2)

#define msecs_to_jiffies(j) ((j) / 2)

//unsigned long jiffies_to_msecs(unsigned long j);
//unsigned long msecs_to_jiffies(unsigned long m);

#endif



#ifndef _PRINTF_DEFINED_
#define _PRINTF_DEFINED_
#include "typedef.h"
#include <stdarg.h>
#define line_inf printf("%s %s %d \r\n" ,__FILE__, __func__ , __LINE__) ;
int sprintf(char *out, const char *format, ...);
int vprintf(const char *fmt, __builtin_va_list va);
int vsnprintf(char *, unsigned long, const char *, __builtin_va_list);
int snprintf(char *buf, unsigned long size, const char *fmt, ...);

int sscanf(const char *buf, const char *fmt, ...);   //BUG: 多个参数? 最后又空格?

#ifdef __PRINTF_DEBUG
#define     UART_DEBUG  1
#else
#define     UART_DEBUG  0
#endif

#if UART_DEBUG
void putchar(int a);
int puts(const char *out);
void put_u8hex(unsigned char dat);
void put_u16hex(unsigned short dat);
void put_u32hex(unsigned int dat);
void printf_buf(const u8 *buf, int len);
int printf(const char *format, ...);
#else
#define     puts(s)
#define     put_u8hex(a)
#define     put_u32hex(a)
#define     printf_buf(p,l)
#define     printf(...)
#define     putchar(a)
#endif

/* #define DEBUG_MSG */

#ifdef DEBUG_MSG
#define my_log 		printf

#else
#define my_log(...)
#endif

#endif

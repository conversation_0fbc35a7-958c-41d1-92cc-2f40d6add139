/*************************************************************************
	> File Name: include/typedef.h
	> Author:
	> Mail:
	> Created Time: Wed 17 May 2017 03:39:23 PM HKT
 ************************************************************************/

#ifndef _TYPEDEF_H
#define _TYPEDEF_H

#ifdef WIN32
#include <windows.h>


#define _xdata
#define _data
#define _idata
#define _pdata
#define _bdata
#define _code
#define _bit		bool
#define _sbit		bool
#define pu16		u16
#define pu32		u32

#else

typedef unsigned char	u8, BYTE, BaseType_t;
typedef char			s8;
typedef unsigned short	u16 ;
typedef signed short	s16;
typedef unsigned int	u32, <PERSON><PERSON><PERSON>, bool, tu8, tu16, tbool, tu32, CLUST;
typedef signed int		s32;
typedef unsigned long long 		u64;

/* Unsigned.  */
typedef unsigned char		uint8_t;
typedef unsigned short int	uint16_t;
#ifndef __uint32_t_defined
typedef unsigned int		uint32_t;
# define __uint32_t_defined
#endif
#if __WORDSIZE == 64
typedef unsigned long int	uint64_t;
#else
__extension__
typedef unsigned long long int	uint64_t;
#endif

typedef unsigned long DWORD, UINT;
typedef unsigned short WORD;
#ifndef NULL
#define NULL    0
#endif

#ifndef NULL
#define NULL    (void *)0
#endif

#endif

#include "dv20.h"
#include "csfr.h"


#ifdef __GNUC__
#define ALIGNE(x)       __attribute__((aligned(x)))
#define sec(x)          __attribute__((section(#x)))
#define sec_used(x)     __attribute__((section(#x),used))
#define AT(x)           __attribute__((section(#x),used))
#define _GNU_PACKED_	__attribute__((packed))
//#define SET_INTERRUPT   __attribute__((interrupt("SWITCH")))
#define SET_INTERRUPT   __attribute__((interrupt("")))
#else
#define sec(x)
#define AT(x)
#define SET(x)
#define _GNU_PACKED_
#endif

#define __asm_csync() \
    do { \
		asm volatile("csync;"); \
    } while (0)


#define	LD_WORD(ptr)		(u16)(*(u16*)(u8*)(ptr))
#define	LD_DWORD(ptr)		(u32)(*(u32*)(u8*)(ptr))
#define	ST_WORD(ptr,val)	*(u16*)(u8*)(ptr)=(u16)(val)
#define	ST_DWORD(ptr,val)	*(u32*)(u8*)(ptr)=(u32)(val)


#define FALSE	0
#define TRUE    1

#define false	0
#define true    1


#define     BIT(n)	            (1 << (n))
#define     BitSET(REG,POS)     (REG |= (1 << POS))
#define     BitCLR(REG,POS)     (REG &= (~(1 << POS)))
#define     BitXOR(REG,POS)     (REG ^= (~(1 << POS)))
#define     BitCHK_1(REG,POS)   ((REG & (1 << POS)) == (1 << POS))
#define     BitCHK_0(REG,POS)   ((REG & (1 << POS)) == 0x00)
#define     testBit(REG,POS)    (REG & (1 << POS))

#define     clrBit(x,y)         x &= ~(1L << y)
#define     setBit(x,y)         x |= (1L << y)

#ifndef SFR
#define SFR(sfr, start, len, dat) (sfr = sfr & ~((~(0xffffffff << len)) << start) | ((dat & (~(0xffffffff << len))) << start))
#endif
#if 1
#define ASSERT(a,...)   \
        do { \
            if(!(a)){ \
                printf("ASSERT-FAILD: "#a"\n"__VA_ARGS__); \
                while(1); \
            } \
        }while(0);
#else
#define ASSERT(a,...)
#endif
#define BUILD_BUG_ON_ZERO(e)  (sizeof(struct{int : -!!(e);}))
#define __must_be_array(a) \
	BUILD_BUG_ON_ZERO(__builtin_types_compatible_p(typeof(a), typeof(&a[0])))
#define     ARRAY_SIZE(arr)	((sizeof(arr) / sizeof(arr[0])) + __must_be_array(arr))
#include "errno-base.h"
#include "string.h"

#ifdef offsetof
#undef offsetof
#endif

#ifdef container_of
#undef container_of
#endif

#define offsetof(type, memb) \
	((unsigned long)(&((type *)0)->memb))

#define container_of(ptr, type, memb) \
	((type *)((char *)(ptr) - offsetof(type, memb)))

static inline u32 rand32()
{
    return 0;
}
void delay(unsigned int);
void delay_2ms(unsigned int);
void delay_us(unsigned int);
#endif

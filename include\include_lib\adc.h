#ifndef __ADC_H__
#define __ADC_H__

#include "typedef.h"

enum ADC_CH {
    ADC_CH0_PA0 = 0,
    ADC_CH1_PA2,
    ADC_CH2_PA3,
    ADC_CH3_PA4,
    ADC_CH4_PA10,
    ADC_CH5_PA11,
    ADC_CH6_PB2,
    ADC_CH7_PB5,
    ADC_CH8_PB6,
    ADC_CH9_PB7,
    ADC_CH10_PB8,
    ADC_CH11_PB9,
    ADC_CH12_PB10,
    ADC_CH13_HUSB_RETX__CSI_TEST,
    ADC_CH14_AUDIO,
    ADC_CH15_LDO,
};

void adc_scan_open(enum ADC_CH ch);
void adc_scan_close(enum ADC_CH ch);
u16 adc_scan_getvalue(enum ADC_CH ch);

#endif



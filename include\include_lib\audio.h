#ifndef __AUDIO_H__
#define __AUDIO_H__

#include "typedef.h"



enum DAC_SAMPLE_RATE {
    DAC_SMP_96000 = 0,
    DAC_SMP_88200,
    DAC_SMP_64000,
    DAC_SMP_RESERVED,
    D<PERSON>_SMP_48000,
    DAC_SMP_44100,
    DAC_SMP_32000,
    DAC_SMP_RESERVED1,
    DAC_SMP_24000,
    DAC_SMP_22050,
    DAC_SMP_16000,
    DAC_SMP_RESERVED2,
    DAC_SMP_12000,
    DAC_SMP_11025,
    DAC_SMP_8000,
    DAC_SMP_RESERVED3,
};

enum ADC_SAMPLE_RATE {
    ADC_SMP_RESERVED = 0,
    ADC_SMP_RESERVED1,
    ADC_SMP_RESERVED2,
    ADC_SMP_RESERVED3,
    ADC_SMP_48000,
    ADC_SMP_44100,
    ADC_SMP_32000,
    ADC_SMP_RESERVED4,
    ADC_SMP_24000,
    AD<PERSON>_SMP_22050,
    ADC_SMP_16000,
    AD<PERSON>_SMP_RESERVED5,
    ADC_SMP_12000,
    AD<PERSON>_SMP_11025,
    ADC_SMP_8000,
    ADC_SMP_RESERVED6,
};

enum ADC_TYPE {
    ADC_TYPE_SINGLE = 0,	//单端
    ADC_TYPE_DIFF,			//差分
    ADC_TYPE_LINEIN,		//linein
};

struct audio_dev {
    u8 sample_rate;	//采样率，填枚举值
    u8 volume;		//音量，0~255
    u8 adc_type;	//ADC输入模式
    u8 *buf;		//音频buf，ADC/DAC共用的时候可以不另外编写handler，adc数据直通到dac
    u32 buf_size;	//音频buf采样点数，每帧长度为此大小的一半
};

void adc_handler(u8 *buf, u32 len);		//ADC中断处理函数，内部有空的弱定义
void dac_handler(u8 *buf, u32 len);		//DAC中断处理函数，内部有空的弱定义

void audio_init(u16 sample_point);		//音频初始化，必须在open之前调用一次
u8 adc_open(struct audio_dev *dev);		//打开ADC
u8 dac_open(struct audio_dev *dev);		//打开DAC
void adc_close();						//关闭ADC
void dac_close();						//关闭DAC
void adc_set_volume(u8 vol);			//设置ADC增益0~255（共32级，内部换算）
void dac_set_volume(u8 vol);			//设置DAC增益0~255（共16324级，内部换算）
int dac_get_data(u8 *buf, int offset, u32 cnt);  //获取dac循环buf的数据，offset为从当前dac播放点算的偏移量(byte)
u32 adc_get_data_len();					//获取adc循环buf里的数据量

#endif

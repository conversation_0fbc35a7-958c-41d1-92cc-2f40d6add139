#ifndef  __CLOCK_H__
#define  __CLOCK_H__

#include "typedef.h"
#include "gpio.h"
#include "jiffies.h"

#define SYS_CLK_RC_250K         0
#define SYS_CLK_PAT_CLK         1
#define SYS_CLK_W_CLK           2
#define SYS_CLK_LRC_200K        3
#define SYS_CLK_OSC_12M         4
#define SYS_CLK_PLL_96M         5
#define SYS_CLK_PLL_SYS         6
#define SYS_CLK_PLL_48M         7

#define PLL_96M                 (96*1000000L)
#define PLL_48M                 (48*1000000L)
#define RC_CLOCK                (25*10000) //250K
#define LRC_CLOCK               (20*10000) //200K
#define     MHz_Unit            (1000000L)

#define TIMER_SRC_LSB           0
#define TIMER_SRC_OSC           1
#define TIMER_SRC_HTC           2
#define TIMER_SRC_LRC           3
#define TIMER_SRC_RC_16M        4

enum {
    EVA_ORG_CLK = 0,
    <PERSON><PERSON>_<PERSON><PERSON><PERSON>,
    <PERSON>SB_CLK,
    <PERSON><PERSON><PERSON>M_CLK,
    <PERSON>_<PERSON><PERSON><PERSON>,
    LRC_CLK,
    JPG_ORG_CLK,
    OSC_12M_CLK,
    SEN_XCLK,
};
enum {
    CLK_OUT0 = 0,
    CLK_OUT1,
    CLK_OUT2,
    CLK_OUT3,
    CSI_DBG0,
    CSI_DBG1,
    CSI_DBG2,
    ICH7,
    TIME0_PWM,
    TIME1_PWM,
    TIME2_PWM,
    TIME3_PWM,
    DAC_CKO,
    ADC_CKO,
    ADC_DO0,
    ADC_DO1,
};

void xosc_init(void);
void lrc_clk_init(void);
// void jpeg_clock_config(u16 clock_rate);
void sys_clk_sel(u8 clk_src);
void sysclk_init(u8 xosc_diable, u32 pll_nr);

int clk_out_to_io(u32 io, u8 outputchannel, u8 clk_out_sel, u8 clk_sel);
u32 clk_get(const char *clk);
void sen_xclk_output(u32 io, u8 clk_out_sel, int freq);

int lrc_trim(u16 *fbdiv);
void lrc_trim_dump();
void efuse_info_dump(void);
void sys_clk_reinit(u16 pll_nr);
void start_dynamic_lrc_trim();
void usb_ana_init(void);
void _sys_clk_init();
void _sys_clk_reinit();
void _pll_init(u8 ref_clk);
void ic_sys_clk_init_xosc(u8 xosc);
void pll_ic_sfr_init(u8 ref_clk, u32 pll_nr);
void xosc_sys_clk_init(u8 mode);
int clk_out2_to_io();
void lrc_2_xosc(u8 mode, u16 pll_nr);



#endif  /*CLOCK_H*/

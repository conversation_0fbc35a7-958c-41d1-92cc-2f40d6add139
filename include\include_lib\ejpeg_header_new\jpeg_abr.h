#ifndef _JPEG_ABR_H
#define _JPEG_ABR_H
#include "typedef.h"


#define STD_USB_SPEED 50000
#define WIN_SIZE 3

typedef struct jpeg_abr_fd {
    u8 fps;
    u32 kbps;
    u32 std_kbps;
    u32 mbFactor;
    u32 frames;
    u32 cur_abr;
    u32 neighbor_bytes[WIN_SIZE];
    u8 win_idx;
    u32 total_kbytes;
    int rate_diff;
    u32 max_usb_speed;
    u32 slow_cnt;
    u32 width;
    u32 height;
    u8 q_step;
    s8 max_q;
    s8 min_q;
    s8 q;
    u8 abr_mode;
    u8 abr_set_fps_en;
    u8 usb_mode;   //usb 工作模式
    u32 abnormal_limit_kbps;   //异常情况码率限制
    u32 high_speed_th;   //usb2.0速度阈值
    u32 full_buffer_size;
    u32(*jpeg_get_usb_speed)(void);
    u32(*jpeg_get_usb_bufsize)(void);
} mabr_t;

//输入帧率，目标码率，宽高，初始q，最大q, 最小q, 每次调整q的步长(推荐q步长为<=3; 太大容易出现跳跃，或者画面变化太剧烈)
mabr_t *jpeg_abr_init(u32 fps, u32 abr_kbps, u32 w, u32 h, s8 init_q, s8 max_q, s8 min_q, u8 q_step);
//输入前一帧的大小（KBytes), 返回新的一帧Q值；
int jpeg_abr_update(mabr_t *h, u32 pre_kbytes);

u8 jpeg_change_bitrate(mabr_t *h, u32 pre_kbytes, u8 upd_qval);

void jpeg_abr_uninit(mabr_t *jpeg_abr);

//void jpeg_abr_reset(mabr_t *jpeg_abr, u32 abr_kbps);

//int jpeg_abr_check_remain_space(mabr_t *h, u32 size);
//int jpeg_abr_check_remain_space(u8 cur_q, u32 size);


#endif

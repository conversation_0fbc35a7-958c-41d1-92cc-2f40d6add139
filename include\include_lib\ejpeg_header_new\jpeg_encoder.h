#ifndef  __JPEG_ENCODER_H__
#define  __JPEG_ENCODER_H__
#include "typedef.h"

#define JPEG_DEFAULT_EXIF_LEN     0

#define JPG_SAMP_FMT_INVALID     0x0
#define JPG_SAMP_FMT_YUV444      0x1
#define JPG_SAMP_FMT_YUV422      0x2
#define JPG_SAMP_FMT_YUV420      0x3

#define JPEG_DEBUG_EN     0
#if JPEG_DEBUG_EN
#define jpeg_log_debug(str, ...) printf(str, ##__VA_ARGS__)
#else
#define jpeg_log_debug(str, ...)
#endif

//static const u8 qval_tab_map[13] = {0, 1, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22};
static const u8 qval_tab_map[13] = {0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24};

struct jpg_q_table {
    u16  YQT_DCT[0x40] ;
    u16  UVQT_DCT[0x40];
    u8   DQT[138];
};

typedef struct jpg_q_table qtfilehead_t;

struct jpeg_data {
    u8 *buf;
    u32 len;
};

struct jpeg_codec_handle {
    JL_JPG_TypeDef *reg;
    struct jpeg_encoder_fd *encoder_fd;
};

struct jpeg_encode_info {
    u16 width;
    u16 height;
    u32 kbps;
    u8  fps;
    u8  fmt;
    u8  ytype;
    u8  ycnt;
    u8  q_val;
    u16 mcu_w;
    u16 mcu_h;
    u16 x_mcu_num;
    u16 y_mcu_num;
    u32 ysize;
    u32 usize;
    u32 yuv_size;
};

struct jpeg_encoder_fd {
    void *parent;
    struct jpeg_encode_info info;
    struct jpeg_data tdat;
    u8 enable_abr;
    u8 abr_mode;
    u8 hori_sample;
    u8 vert_sample;
    u8 sample_rate;
    u8 enable_dri;
    u8 rst_marker;
    u8 enable_dynamic_fps;
    u8 usb_mode;           //usb 工作模式
    u16 rst_interval;
    u32 cb_ylen;
    u32	cb_ulen;
    u32	cb_vlen;
    u32 jpg_buf_size;
    u32 fps;
    u32 tar_Kbps;
    u32 bits_buffer_size;
    u32 abnormal_limit_kbps;   //异常情况码率限制
    u32 high_speed_th;   //usb2.0速度阈值

    volatile u32 mcu_cnt;
    volatile u32 mcu_line_cnt;
    volatile u32 encode_mcu;
    volatile u32 bits_cnt;
    volatile u32 file_size;
    volatile u32 frame_size;
    u32 buf_len;
    u32 head_len;
    u8 line_mcu_pnd;
    u16 row_mcu_num;
    u16 enc_line;
    volatile u32 jpeg_offset;

    u8 *data;
    u8 *ybuf;
    u8 *ubuf;
    u8 *vbuf;
    u8 *line_yuv_buf;

    volatile u8 sem_stop;
    volatile u8 sem_mcu_pnd;
    volatile u8 sem_bits_pnd;
    volatile u8 sem_speed_pnd;

    struct jpeg_abr_fd *abr_fd;
    int (*jpeg_output_buf)(void *buf, int len, u32 arg);
    void (*jpeg_set_fps)(s8 cur_q, int rate_diff, u32 max_usb_speed);
    u32(*jpeg_get_usb_speed)(void);
    u32(*jpeg_get_usb_bufsize)(void);
};

struct jpeg_attr {
    u8 jpeg_clock;         //jpeg工作频率(96M/120M/160M/192M/240M)
    u16 width;             //jpeg编码图像宽
    u16 height;            //jpeg编码图像高
    u16 enc_line;          //jpeg编码行数
    u8 yuv_fmt;            //jpeg编码yuv格式(yuv420/yuv422)
    u8 fps;                //jpeg编码帧率
    u8 abr_en;             //jpeg码率控制使能
    u8 abr_mode;           //jpeg码率控制方式(0:粗调 1:细调)
    u8 dri_en;             //jpeg位流分段使能
    u8 set_fps_en;         //动态帧率开关
    u8 *in_buffer;         //jpeg输入yuv buffer
    u8 *bits_buffer;       //jpeg输出位流buffer
    u8 usb_mode;           //usb 工作模式
    u32 bits_size;         //jpeg输出位流buffer大小
    u32 tar_Kbps;          //jpeg目标码率
    u32 jpeg_dri_mcu_cnt;  //jpeg分段位流 mcu配置
    u32 bits_buffer_size;  //jpeg输出位流总buffer大小
    u32 abnormal_limit_kbps;   //异常情况码率限制
    u32 high_speed_th;   //usb2.0速度阈值
    int (*jpeg_output_buf)(void *buf, int len, u32 arg); //jpeg位流输出回调
    void (*jpeg_set_fps)(s8 cur_q, int rate_diff, u32 max_usb_speed); //jpeg动态设置帧率回调
    u32(*jpeg_get_usb_speed)(void); // jpeg模块获取usb速率回调
    u32(*jpeg_get_usb_bufsize)(void);
};

int jpeg_frame_bits_init(struct jpeg_encoder_fd *fd, u8 *bits_buf, int buf_len);
int jpeg_encoder_image_start(void *_fd);
int jpeg_abr_param_config(struct jpeg_encoder_fd *encoder_fd);
int encoder_bits_pnd_handler(struct jpeg_encoder_fd *fd);
int encoder_mcu_pnd_handler(struct jpeg_encoder_fd *fd);
//int encoder_speed_pnd_handler(struct jpeg_encoder_fd *fd);
void jpeg_encoder_restart(u8 reset_buf);
int jpeg_encoder_start(struct jpeg_attr *jattr);
int jpeg_encoder_stop(void);

int qtfilehead_get_size(void);

int jpeg_ex_set_baddr(u8 *baddr, int baddr_len, u8 frame_end);
#endif




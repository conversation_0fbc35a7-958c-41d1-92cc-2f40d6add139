
#ifndef __IMC_H__
#define __IMC_H__

#include "typedef.h"
#include "hwi.h"
#include "irq.h"

struct scale_reg {
    volatile u32 com_con;
    volatile u32 src_con;
    volatile u32 crop_h;
    volatile u32 crop_v;
    volatile u32 h_step;
    volatile u32 h_width;
    volatile u32 v_step;
    volatile u32 v_width;
};

struct dma_reg {
    volatile u32 con;
    volatile u32 cnt;
    volatile u32 y_base0;
    volatile u32 u_base0;
    volatile u32 v_base0;

};

struct osd_reg {
    volatile u32 con;
    volatile u32 color0; //1bit模式下颜色
    volatile u32 color1; //2bit颜色
    volatile u32 color2; //2bit颜色
    volatile u32 color3; //2bit颜色
    volatile u32 osd0_h_cfg;
    volatile u32 osd0_v_cfg;
    volatile u32 osd0_base;
    volatile u32 osd1_h_cfg;
    volatile u32 osd1_v_cfg;
    volatile u32 osd1_base;
};

struct imc_ch_reg {
    struct scale_reg *scale;
    struct dma_reg   *dma;
    struct osd_reg   *osd;
};

struct channel_osd_config {
    volatile u8 enable;
    u8 index;
    volatile u8 inused;
    u8 mode;
    u8 type;
    u8 id;
    u8 ready_to_flush;
    u8 pause;
    int timer;

    u16 x;
    u16 y;
    u16 width;
    u16 height;

    u8  *buf_addr[2];
    u32 buf_size;

    //colors
    u32 color[4];
    //font
    u16  font_width;
    u16  font_height;
    char *format_str;
    u16  format_len;
    u16  horz_str_max_len;
    char str_cache[2][0x80 * 4];
    char *matrix_strings;
    int  matrix_strings_len;
    u8   *matrix_addr;
    u32  matrix_bytes;

};
struct video_text_osd {
    u16     x;//起始地址
    u16     y;//结束地址
    // u32     osd_yuv;//osd颜色
    u32     color[3]; //2bit图像yuv颜色配置
//注意：下面的字符串地址必须是全局的,然后年是yyyy，月是nn，日是dd，时是hh，分是mm，秒是ss,其他字符是英文字母&&符号&&汉字
    char    *text_format; //用户自定义格式，例如 "yyyy-nn-dd\hh:mm:ss" 或者 "hh:mm:ss"
    char    *font_matrix_table; //用户自定义字模字符串,例如“abcd....0123..”
    u8      *font_matrix_base; //用户自定义字模的起始地址
    u32     font_matrix_len;//用户自定义字模数组的长度,no str len!!!
    u8      font_w;//用户自定义字体大小,8的倍数
    u8      font_h;//8的倍数
    u8      direction; //0 -- 顺向, 1 -- 逆向
    u8      bit_mode; //2 - 2bit 1 - 1bit
};

enum osd_color_mode {
    IMC_OSD_CLEAR_MODE = 0,
    IMC_OSD_1BIT_MODE,
    IMC_OSD_2BIT_MODE,
    IMC_OSD_16BIT_MODE, //yuv422
};

enum imc_src_type {
    IMC_SRC_DIS = 0,
    IMC_SRC_ISP0,
    IMC_SRC_ISP1,
};

enum {
    IMC_ONE_FRAME = 0,
    IMC_16LINE,
    IMC_32LINE,
    IMC_64LINE,
};

enum {
    IMC_FRAME_BUFF = 0,
    IMC_ROUND_BUFF,
};

enum {
    IMC_YUV420 = 0,
    IMC_YUV422,
};

enum {
    IMC_MOUDLE_JPEG = 1,
    IMC_MOUDLE_YUYV,
};

struct imc_iattr {
    enum imc_src_type src;      //imc输入源(isp0/isp1)
    u16 src_w;                  //imc输入图像宽
    u16 src_h;                  //imc输入图像高
    u16 tar_w;                  //imc输出图像宽
    u16 tar_h;                  //imc输出图像高
    u8 *out_buf;                //imc输出缓存buf
    u8 yuvformat;               //imc输出yuv格式(yuv420/yuv422)
    u8 imc_buf_mode;            //imc buf输出模式(循环模式/整帧模式)
    u8 imc_int_cfg;             //imc中断行数
    u8 dma_cnt; //2 line align  //imc转换行数
    u16 crop_x;                 //imc裁剪起始x
    u16 crop_y;                 //imc裁剪起始y
    u16 crop_width;             //imc裁剪图像宽
    u16 crop_height;            //imc裁剪图像高
    u8 mirror;                  //imc水平镜像功能
    u8 ext_sel; //联动选择 0:不联动 1:jpeg  2:yuv_recorder(该属性无效)

    u8 in_fps;                  //无效
    u8 out_fps;                 //无效

    u8 osd_en;                  //无效

};
int imc_close(void);
void imc_kstart(void);
void *imc_open(struct imc_iattr *attr);
void *ex_imc_open(struct imc_iattr *attr);
int imc_dump_image_config(int src_width, int src_height, int out_width, int out_height, int source, u8 *out);

static inline void imc_set_fps(u8 src_fps, u8 tar_fps)
{
    int frc_cfg = (src_fps * 65536) / tar_fps - 65536;// * 65536;

    imc_ch0_src_con |=  frc_cfg;
}

#endif

#ifndef LBUF_H
#define LBUF_H


#include "list.h"
// #include "system/spinlock.h"

#ifdef BIT
#undef BIT
#endif

struct lbuff_head {
    int magic_a;
    struct list_head head;
    struct list_head free;
    // spinlock_t lock;
    unsigned char align;
    unsigned short priv_len;
    unsigned int total_size;
    unsigned int last_addr;
    int magic_b;
};

struct lbuff_state {
    unsigned int avaliable;
    unsigned int fragment;
    unsigned int max_continue_len;
    int num;
};


#define BIT(x)    (1<<x)

struct lbuff_head *lbuf_init(void *buf, unsigned int len, int align, int priv_head_len);

void *lbuf_alloc(struct lbuff_head *head, unsigned int len);

void *lbuf_realloc(struct lbuff_head *head, void *lbuf, u32 size);

int lbuf_empty(struct lbuff_head *head);

void lbuf_clear(struct lbuff_head *head);

void lbuf_push(struct lbuff_head *head, void *lbuf, unsigned char channel_map);

void *lbuf_pop(struct lbuff_head *head, unsigned char channel);

void lbuf_free(struct lbuff_head *head, void *lbuf);

unsigned int lbuf_free_space(struct lbuff_head *head);

void lbuf_state(struct lbuff_head *head, struct lbuff_state *state);

void lbuf_dump(struct lbuff_head *head);

int lbuf_traversal(struct lbuff_head *head);

#endif



#ifndef MSG_H
#define MSG_H

#include "typedef.h"
#include "cbuf.h"

#define SYS_DEVICE_EVENT 		0x01
#define SYS_MSG_EVENT 		    0x02

enum {
    DEVICE_EVENT_OUT = 0,
    DEVICE_EVENT_IN,
    DEVICE_EVENT_ONLINE,
    DEVICE_EVENT_OFFLINE,
    DEVICE_EVENT_CHANGE,
};

struct sys_msg {
    u8 type : 2;
    void *arg;
    u8 event;
    int value;
};
struct sys_msg *get_sys_msg(void);
int msg_pool_init(void *msg_pool_buf, u32 size);
void sys_msg_post(struct sys_msg *msg);
void sys_event_clear(void);
#endif




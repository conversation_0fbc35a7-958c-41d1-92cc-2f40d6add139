#ifndef __MY_MALLOC_H__
#define __MY_MALLOC_H__

#include "typedef.h"



#define configUSE_MALLOC_FAILED_HOOK    0

#ifndef portPOINTER_SIZE_TYPE
#define portPOINTER_SIZE_TYPE u32
#endif

#define portBYTE_ALIGNMENT      4   //对齐规则


#if portBYTE_ALIGNMENT == 32
#define portBYTE_ALIGNMENT_MASK ( 0x001f )
#endif

#if portBYTE_ALIGNMENT == 16
#define portBYTE_ALIGNMENT_MASK ( 0x000f )
#endif

#if portBYTE_ALIGNMENT == 8
#define portBYTE_ALIGNMENT_MASK ( 0x0007 )
#endif

#if portBYTE_ALIGNMENT == 4
#define portBYTE_ALIGNMENT_MASK ( 0x0003 )
#endif

#if portBYTE_ALIGNMENT == 2
#define portBYTE_ALIGNMENT_MASK ( 0x0001 )
#endif

#if portBYTE_ALIGNMENT == 1
#define portBYTE_ALIGNMENT_MASK ( 0x0000 )
#endif

#define vTaskSuspendAll()
#define traceMALLOC(...)
#define configASSERT       ASSERT


#define pdFALSE   0
#define pdTRUE    1

// extern const char MM_ASSERT;


typedef enum _mm_type {
    MM_NONE = 0,
    MM_VFS,
    MM_SYDFS,
    MM_SYDFF,
    MM_NORFS,
    MM_NORFF,
    MM_FATFS,
    MM_FATFF,
    MM_FAT_TMP,
    MM_SRC,
    MM_MIO,
} mm_type;


#ifndef traceFREE
#define traceFREE(pvAddress, uSize)
#endif


#ifdef DMALLOC_EN
void dlmalloc_stats();
extern void *dlmalloc(size_t bytes);
extern void dlfree(void *mem);
extern void *zalloc(unsigned long size);
#define malloc_stats      dlmalloc_stats
#define malloc      dlmalloc
#define free        dlfree

#else


#if 1
void vPortInit(void *pAddr, uint32_t xLen);
// void *pvPortMalloc( size_t xWantedSize );

// void *pvPortMalloc(size_t xWantedSize, mm_type type);
void *pvPortMalloc(size_t xWantedSize);
void vPortFree(void *pv);
// void *my_malloc(u32 size, mm_type xType);
void *my_malloc(u32 size);
void *my_zalloc(unsigned long size);
void *my_free(void *pv);
void malloc_init(void);
void malloc_stats(void);
#define malloc      my_malloc
#define free        my_free
#define zalloc      my_zalloc
#else
#define malloc      my_malloc
#define free        my_free
void *my_malloc(unsigned long size);
void *my_zalloc(unsigned long size);
void my_free(void *rmem);
void malloc_stats(void);
#define malloc      my_malloc
#define free        my_free
#define malloc      my_malloc
#define zalloc      my_zalloc
#endif
#endif


#endif


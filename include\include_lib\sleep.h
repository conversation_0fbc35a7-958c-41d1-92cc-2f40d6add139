#ifndef __SLEEP_H__
#define __SLEEP_H__

#include "includes.h"
#include "io_imap.h"
#include "gpio.h"


//唤醒源
enum WAKEUP_PORT {
    WAKEUP_PORT0_INPUT_CHANNEL8 = 0,
    WAKEUP_PORT1_INPUT_CHANNEL9,
    WAKEUP_PORT2_INPUT_CHANNEL10,
    WAKEUP_PORT3_INPUT_CHANNEL11,
    WAKEUP_PORT4_UART0_RX,
    WAKEUP_PORT5_UART1_RX,
    WAKEUP_PORT6_SD_DAT1,
};

//唤醒电平
enum WAKEUP_EDGE {
    WAKEUP_EDGE_UP = 0,
    WAKEUP_EDGE_DOWN,
};

//是否使用filter
enum WAKEUP_FLEN {
    WAKEUP_FLEN_DISABLE = 0,
    WAKEUP_FLEN_ENABLE,
};


//设置唤醒（唤醒源[eunum WAKEUP_PORT]， 输入通道通道[0~7]， 管脚[IO_PORTA_00..], 使能[0/1]， 唤醒电平[enum WAKEUP_EDGE]， 是否使用filter[enum WAKEUP_FLEN]）
void set_wakeup_port(u8 port, u8 gp_ich, u32 io, u8 en, u8 edge, u8 flen);
//进入睡眠模式
void enter_sleep_mode();
//退出睡眠模式
void exit_sleep_mode();



#endif

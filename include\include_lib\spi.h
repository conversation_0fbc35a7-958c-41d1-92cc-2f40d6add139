
/**
 * @file spi.h
 * @brief
 * <AUTHOR>
 * @version 1.0.0
 * @date 2021-07-31
 */

#ifndef  __SPI_H__
#define  __SPI_H__
#include "typedef.h"


enum hw_spi_mode {
    SPI_HOST_MODE,
    SPI_SLAVE_MODE,
};

enum hw_spi_io_mode {
    SPI_2WIRE_MODE = 0,
    SPI_ODD_MODE,
    SPI_DUAL_MODE,
    SPI_QUAD_MODE,
};

struct sfc_spi_data {
    u32 addr;
    u32 len;
};


struct spi_config_data {
    u32 wire: 3; //0 1 2 4
    u32 port: 5;
    u32 baud: 8;
};


int spi_init(enum hw_spi_io_mode io_mode, u8 baud);
int spi_open();
int spi_read(void *buf, u32 len);
void spi_dma_read(u8 *buf, u32 len);
int spi_write(void *buf, u32 len);
int spi_close();
u8 spi_read_byte();
void spi_dma_read(u8 *buf, u32 len);
void spi_write_byte(u8 byte);
void spi_cs(u32 status);
u32 spi_ioctl_get_bit_mode(u32 arg);
u32 spi_ioctl_send_byte(u32 arg);
u32 spi_ioctl_read_byte(u32 arg);
u32 spi_ioctl_set_cs(u32 arg);
u32 spi_ioctl_send_cmd(u32 arg);
u32 spi_ioctl_send_dummy(u32 arg);
u32 spi_ioctl_set_crc(u32 arg);
u32 spi_ioctl_read_crc(u32 arg);
u32 spi_ioctl_set_enckey(u32 arg);
void spi_ioctl_cs_pu(u32 en);



#endif  /*SPI_H*/


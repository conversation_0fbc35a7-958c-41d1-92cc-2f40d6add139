
#ifndef __YUV_RECORDER_H__
#define __YUV_RECORDER_H__

#include "typedef.h"
#include "hwi.h"

struct yuv_recorder_attr {
    u8 yuyv_recorder_ready;
    u16 src_line_num;
    u8 *y_start_addr;
    u8 *u_start_addr;
    u8 *v_start_addr;
    u16 image_width;
    u16 image_height;
    u16 des_line_num;
    u16 src_y_stride;
    u16 src_u_stride;
    u16 src_v_stride;
    u16 des_yuv_stride;
    u8 *des_start_addr;
    int (*yuv_output_buf)(void *buf, int len, u32 arg);
};
void yuyv_recorder_open(struct yuv_recorder_attr *attr);
void yuyv_recorder_start(void);
void yuyv_recorder_stop(void);
void yuyv_recorder_restart(void);
void yuyv_recorder_set_des_addr(u32 des_addr);
#endif

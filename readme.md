# S30/YU7/SU7杰里方案

本工程为使用杰里AC5316/AC5326芯片的猫眼方案,杰里相关的文档均放在minio以下目录:[sw-share-folder/Doc/00杰里531N相关资料](https://192.168.20.9:9001/buckets/sw-share-folder/browse/RG9jLzAw5p2w6YeMNTMxTuebuOWFs+i1hOaWmS8=)

## 1.编译环境安装

下载并安装杰里[工具链](https://pkgman.jieliapp.com/s/win-toolchain),如果安装遇到问题查看杰里帮助[文档](https://doc.zh-jieli.com/Tools/zh-cn/dev_tools/dev_env/jl_toolchain.html)

> **Note**  
杰里官方使用的是codeblock进行编译,这不利于管理多版本多产品的release,本工程已经经过配置,可以脱离codeblocks使用,直接通过menuconfig进行产品选择,然后make即可;如需要release产品,则可以运行对应的`release_product_*.py`脚本

## 2.编译下载程序

连接硬件模组,通过`make menuconfig`选择对应的产品类型make即可,在编译完成后会自动下载固件到模组

## 3.ISP效果调试

(1)不同的产品有不同的效果文件;同一个产品为了适配不同客户的屏幕也会有不同的效果文件;我们根据产品和客户把效果文件放到`cpu/dv20/tools/isp_res_*`文件夹中

(2)我们一般针对每个产品调整4个效果文件,外加一个扫码用的效果文件,放在J5L文件夹中,分别为

- JL_JT_NIGHT.J5L   暗室效果文件
- JL_JT_INDOOR.J5L  室内弱光效果文件
- JL_JT_OUTCLUDY.J5L 室内充足光线效果文件
- JL_JT_OUTDOOR.J5L  室外效果文件
- JL_JT_SCANCODE.J5L 扫码效果文件,目前几乎没有产品用到,直接复制即可

一般调试流程如下:

- 根据[AC531N ISPTOOL调试流程.pdf](https://192.168.20.9:9001/buckets/sw-share-folder/browse/RG9jLzAw5p2w6YeMNTMxTuebuOWFs+i1hOaWmS/lt6XlhbflkozmlofmoaMv5omL5YaMLw==)进行效果调试
- 先在室内充足光线下完成JL_JT_OUTCLUDY.J5L的调试
- 然后在JL_JT_OUTCLUDY.J5L基础上适当提高降噪水平得到JL_JT_INDOOR.J5L
- 再在JL_JT_INDOOR.J5L基础上提高降噪水平,降低饱和度,提高亮度(调整gamma等方式)得到JL_JT_NIGHT.J5L
- 最后对于室外场景,可以适当在JL_JT_OUTCLUDY.J5L基础上适当调整测光矩阵,加大中间区域的权重,尽量以人体范围作为测光依据

> **Note**
> 实际调试的时候可以打开app_config.h中的`#define CONFIG_ENABLE_OSD`宏定义,这样在实时视频上会显示当前的场景号,依次为0~3

调试过程中不同场景之间的切换阈值可以通过`isp_scenes.c`中的`USER_ISP_CFG_*_L/H`变量进行调整

特别的,进入暗光环境之后会开白光灯,开灯的时机可以通过`isp_acenes.c`中的`LED_ON_OFF_LV_LOW/HIGH`进行调整

(3) 调试完成后通过运行`make generate_isp_res`生成效果文件;下次make的时候就会使用新的效果文件

> **Note**
> 会根据选择的产品类型自动选择生成对应产品的效果文件

## 4.release固件

在完成调试,修改好版本号,git提交,完成release note的撰写后,通过运行`python ./release_product*.py`release对应的产品.脚本会自动打包文件到build目录,并上传固件到minio备份;同时会自动添加tag并推送tag到服务器

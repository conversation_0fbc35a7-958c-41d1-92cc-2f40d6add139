SU7_with_7256客户信息:

1. 志诚方案商,客户包括悟空等
志诚使用7256,只能用USB1.1,同时jpeg不能开abr,导致图像质量非常差

===============================================================
SU7_V1.0.16

主要修改如下：
1.修复uac描述符错误的bug

===============================================================
SU7_V1.0.12

主要修改如下：
1.安装方向切换

===============================================================
SU7_V1.0.12

主要修改如下：
1.安装方向切换

===============================================================
SU7_V1.0.11

主要修改如下：
1.适配志诚,主机为7256,只支持usb1.1模式:
  需要特别注意usb1.1模式下,工厂调焦的时候,需要打开低分辨率的图像,否则会花屏
  所以在设置为usb1.1模式的时候,需要确保提供小分辨率
2.适配客户主机,必须将jattr.abr_en设置为0
3.适配客户主机出图慢,必须将USB_ACTIVE_CHECK_CNT设置为较大数字,本次设置为10,测试能够
  正常出图,原有默认值4,会导致第一次不能出图,第二次才能出图
4.需要倒装版本;没有对讲功能

===============================================================
SU7_V1.0.10  commit_id: 933efa

主要修改如下：
1.客户的主机为7256，可能性能较低，tar_kbps现已改为800，出图效果较差
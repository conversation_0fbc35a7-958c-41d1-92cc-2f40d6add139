YU7L 客户信息:

1.西尚  蜂慧眼Pro+锁滴 小程序


2.米家(4.5寸屏对讲)  易欧优家 小程序

3.贺升 叮叮智能 app
===============================================================
<<版本对应客户>> (如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. YU7L_V1.0.3 ISOC  贺升

<<特殊适配>> (某客户的独立适配)
无

<<正常修改如下>>
1.无

<<说明>>
1.使用 default 和米家相同配置的 menuconfig

===============================================================
<<版本对应客户>> (如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. YU7L_V1.0.2 ISOC  米家

<<特殊适配>> (某客户的独立适配)
无

<<正常修改如下>>
1.优化了灯光控制阈值, 合理控制开启补光灯时机
2.优化了场景效果文件切换阈值，减少不必要的场景切换

<<说明>>
1.此次更新为常规更新, 主要优化了灯光和场景切换

===============================================================
<<版本对应客户>> (如果同一固件适配客户时版本号不同, 请在 app_config.h 中修改对应版本号)
1. YU7L_V1.0.0 ISOC  米家
2. YU7L_V1.0.1 ISOC  XISHANG

<<特殊适配>> (某客户的独立适配)
1.西尚 为西尚单独增加 USB 速率控制逻辑, 防止西尚出图时卡顿或者重启，但是动态控制 USB speed 的代价就是降低图像质量，来避免卡顿和重启

<<正常修改如下>>
1.创建 YU7L 效果文件
2.将 VTS 绑定 itime
************** 丢帧链路逻辑优化 ******************
3.适配米家过程中出现从黑屏到图像收敛到正常的线性变化问题，做出如下更改：
    CMOS->ISP->JPEG->USB  这是图像在传输的链路
    本次改动优化了丢帧逻辑, 原本的逻辑是从 USB_BUF 中读取数据的时候开始丢帧,
    现在优化为在写入 USB_BUF 之前就开始丢帧
*************************************************
4.YU7L 的 default 和 XISHANG 两个版本的 app_config.h 版本号合二为一, 其他的代码中的宏以及config配置中都还是分开的

<<说明>>
1.YU7L 分为 default 和 XISHANG 两个版本, default 目前适配的是米家4.5对讲, 这两个版本在 menuconfig 中会有体现
2.为西尚单独增加 USB 速率控制逻辑, 防止西尚出图时卡顿或者重启，但是动态控制 USB speed 的代价就是降低图像质量，来避免卡顿和重启（重点问题，如若测试请多加注意此问题）
3.C2595 由于 sensor 原因在适配 ISP 过程中做出一些改动可能会产生曝光抖动，导致图像亮度闪烁（如果出现很频繁还请告知）

===============================================================
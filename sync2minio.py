#!/usr/bin/env python3

import sys
import os

def env_check() -> None:
    version_info = sys.version_info
    if version_info.major != 3 or version_info.minor < 7:
        sys.stdout.write('\x1b[1;31m' + '\nPython version must >= 3.7 exit...\n\n' + '\x1b[0m')
        sys.exit(-1)
    try:
        from minio import Minio
    except:
        cmd = f'{sys.executable} -m pip install minio'
        os.system(cmd)
        from minio import Minio
    try:
        import argparse
    except:
        cmd = f'{sys.executable} -m pip install argparse'
        os.system(cmd)
        import argparse

env_check()

# file_uploader.py MinIO Python SDK example
from minio import Minio
from minio.error import S3Error
import glob

def upload_local_directory_to_minio(client, local_path, bucket_name, minio_path):
    assert os.path.isdir(local_path)

    for local_file in glob.glob(local_path + '/**'):
        local_file = local_file.replace(os.sep, "/") # Replace \ with / on Windows
        if not os.path.isfile(local_file):
            upload_local_directory_to_minio(client,
                local_file, bucket_name, minio_path + "/" + os.path.basename(local_file))
        else:
            remote_path = os.path.join(
                minio_path, local_file[1 + len(local_path):])
            remote_path = remote_path.replace(
                os.sep, "/")  # Replace \ with / on Windows
            client.fput_object(bucket_name, remote_path, local_file)
            # print(f'send ${local_file} to ${remote_path}')

def sync_to_minio(src, dst):
    # Create a client with the MinIO server playground, its access key
    # and secret key.
    client = Minio("192.168.20.9:9000",
        access_key="sw-release-user",
        secret_key="sw-release-user",
        cert_check=False,
    )
    bucket_name = "sw-release"

    # Make the bucket if it doesn't exist.
    found = client.bucket_exists(bucket_name)
    if not found:
        client.make_bucket(bucket_name)
        print("Created bucket", bucket_name)
    else:
        print("Bucket", bucket_name, "already exists")

    if os.path.isfile(src):
        # print(f'send ${src} to ${os.path.join(dst, src)}')
        client.fput_object(bucket_name, os.path.join(dst, src).replace(os.sep, "/"), src)
    else:
        upload_local_directory_to_minio(client, src, bucket_name, os.path.join(dst, src))

    print(
        src, "successfully uploaded as object",
        dst, "to bucket", bucket_name,
    )

if __name__ == "__main__":
    env_check()
    import argparse
    parser = argparse.ArgumentParser()
    parser.add_argument('--src_file', type = str, required=True)
    parser.add_argument('--dst_file', type = str, required=True)
    args = parser.parse_args()
    try:
        sync_to_minio(args.src_file, args.dst_file)
    except S3Error as exc:
        print("error occurred.", exc)

# use mc
# import os

# mc_path = f'$HOME/minio-binaries/mc'
# if not os.path.isfile(mc_path):
#     os.system(f'curl https://dl.min.io/client/mc/release/linux-amd64/mc --create-dirs -o {mc_path}')

# os.system(f'{mc_path} cp --recursive test/small_file_1024 local/test')
